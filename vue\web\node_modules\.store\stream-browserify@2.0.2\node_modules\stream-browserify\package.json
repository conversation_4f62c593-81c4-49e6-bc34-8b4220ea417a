{"name": "stream-browserify", "version": "2.0.2", "description": "the stream module from node core for browsers", "main": "index.js", "dependencies": {"inherits": "~2.0.1", "readable-stream": "^2.0.2"}, "devDependencies": {"safe-buffer": "^5.1.2", "tape": "^4.2.0", "typedarray": "~0.0.6"}, "scripts": {"test": "tape test/*.js"}, "repository": {"type": "git", "url": "git://github.com/browserify/stream-browserify.git"}, "homepage": "https://github.com/browserify/stream-browserify", "keywords": ["stream", "browser", "browserify"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://substack.net"}, "license": "MIT", "testling": {"files": "test/*.js", "browsers": ["ie/8..latest", "firefox/3.5", "firefox/10", "firefox/nightly", "chrome/10", "chrome/latest", "chrome/canary", "opera/12..latest", "opera/next", "safari/5.1..latest", "ipad/6.0..latest", "iphone/6.0..latest", "android-browser/4.2..latest"]}, "__npminstall_done": true, "_from": "stream-browserify@2.0.2", "_resolved": "https://registry.npmmirror.com/stream-browserify/-/stream-browserify-2.0.2.tgz"}