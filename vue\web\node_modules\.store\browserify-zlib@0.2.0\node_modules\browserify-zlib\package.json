{"name": "browserify-zlib", "version": "0.2.0", "description": "Full zlib module for the browser", "keywords": ["zlib", "browserify"], "main": "lib/index.js", "directories": {"test": "test"}, "dependencies": {"pako": "~1.0.5"}, "devDependencies": {"assert": "^1.4.1", "babel-cli": "^6.24.1", "babel-plugin-transform-es2015-arrow-functions": "^6.22.0", "babel-plugin-transform-es2015-block-scoping": "^6.24.1", "babel-plugin-transform-es2015-template-literals": "^6.22.0", "babelify": "^7.3.0", "brfs": "^1.4.3", "browserify": "^14.4.0", "exec-glob": "^1.2.2", "glob": "^7.1.2", "karma": "^1.7.0", "karma-chrome-launcher": "^2.1.1", "karma-firefox-launcher": "^1.0.1", "karma-mocha": "^1.3.0", "karma-mocha-own-reporter": "^1.1.2", "karma-phantomjs-launcher": "^1.0.4", "mocha": "^3.4.2", "phantomjs-prebuilt": "^2.1.14", "standard": "^10.0.2", "watchify": "^3.9.0"}, "scripts": {"build": "babel src --out-dir lib", "lint": "standard \"*.js\" \"!(node_modules|lib)/!(*test-zlib*|index).js\"", "pretest": "npm run build", "test": "npm run test:node && npm run test:browser", "test:node": "node node_modules/exec-glob node \"test/test-*\"", "pretest:browser": "node test/build", "test:browser": "karma start --single-run=true karma.conf.js"}, "babel": {"plugins": ["transform-es2015-arrow-functions", "transform-es2015-block-scoping", "transform-es2015-template-literals"]}, "author": "<PERSON> Go<PERSON>t <<EMAIL>>", "homepage": "https://github.com/devongovett/browserify-zlib", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/devongovett/browserify-zlib.git"}, "bugs": {"url": "https://github.com/devongovett/browserify-zlib/issues"}, "__npminstall_done": true, "_from": "browserify-zlib@0.2.0", "_resolved": "https://registry.npmmirror.com/browserify-zlib/-/browserify-zlib-0.2.0.tgz"}