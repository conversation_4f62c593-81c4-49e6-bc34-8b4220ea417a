'use strict';

exports.type = 'perItem';

exports.active = true;

exports.description = 'removes XML processing instructions';

/**
 * Remove XML Processing Instruction.
 *
 * @example
 * <?xml version="1.0" encoding="utf-8"?>
 *
 * @param {Object} item current iteration item
 * @return {<PERSON><PERSON>an} if false, item will be filtered out
 *
 * <AUTHOR>
 */
exports.fn = function(item) {

    return !(item.processinginstruction && item.processinginstruction.name === 'xml');

};
