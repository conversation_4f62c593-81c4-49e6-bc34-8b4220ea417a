#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="$basedir\C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\resolve@1.22.10\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="$basedir/C:/Users/<USER>/Desktop/p/vue/web/node_modules/.store/resolve@1.22.10/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$env_node_path$pathsep$new_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../.store/resolve@1.22.10/node_modules/resolve/bin/resolve" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../.store/resolve@1.22.10/node_modules/resolve/bin/resolve" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../.store/resolve@1.22.10/node_modules/resolve/bin/resolve" $args
  } else {
    & "node$exe"  "$basedir/../.store/resolve@1.22.10/node_modules/resolve/bin/resolve" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
