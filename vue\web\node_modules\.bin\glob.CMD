@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=%~dp0\C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\glob@11.0.3\node_modules"
) ELSE (
  @SET "NODE_PATH=%NODE_PATH%;%~dp0\C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\glob@11.0.3\node_modules"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\.store\glob@11.0.3\node_modules\glob\dist\esm\bin.mjs" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\.store\glob@11.0.3\node_modules\glob\dist\esm\bin.mjs" %*
)
