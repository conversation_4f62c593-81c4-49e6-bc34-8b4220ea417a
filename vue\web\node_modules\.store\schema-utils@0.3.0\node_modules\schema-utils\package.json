{"name": "schema-utils", "version": "0.3.0", "description": "Webpack Schema Validation Utilities", "main": "dist/cjs.js", "files": ["dist"], "engines": {"node": ">= 4.3 < 5.0.0 || >= 5.10"}, "scripts": {"start": "yarn run build -- -w", "prebuild": "yarn run clean", "build": "cross-env NODE_ENV=production babel src -d dist --ignore 'src/**/*.test.js'", "clean": "del-cli dist", "lint": "eslint --cache src test", "lint-staged": "lint-staged", "security": "nsp check", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --collectCoverageFrom='src/**/*.js' --coverage", "travis:coverage": "yarn run test:coverage", "travis:lint": "yarn run lint && yarn run security", "travis:test": "yarn run test", "webpack-defaults": "webpack-defaults", "prepublish": "yarn run build", "release": "yarn run standard-version"}, "dependencies": {"ajv": "^5.0.0"}, "devDependencies": {"babel-cli": "^6.24.1", "babel-jest": "^19.0.0", "babel-plugin-transform-object-rest-spread": "^6.23.0", "babel-polyfill": "^6.23.0", "babel-preset-env": "^1.4.0", "babel-preset-webpack": "^1.0.0", "codecov": "^2.0.1", "cross-env": "^4.0.0", "del-cli": "^0.2.1", "eslint": "^3.19.0", "eslint-config-webpack": "^1.2.1", "eslint-plugin-import": "^2.2.0", "jest": "^19.0.2", "lint-staged": "^3.4.0", "nsp": "^2.6.3", "pre-commit": "^1.2.2", "standard-version": "^4.0.0", "webpack-defaults": "^0.4.5"}, "pre-commit": "lint-staged", "lint-staged": {"*.js": ["eslint --fix", "git add"]}, "eslintConfig": {"extends": "webpack", "installedESLint": true}, "keywords": ["webpack", "plugin", "es2015"], "author": "Webpack Contrib (https://github.com/webpack-contrib)", "contributors": [{"name": "<PERSON><PERSON> <@bebraw>"}, {"name": "<PERSON> <@d3viant0ne>"}, {"name": "<PERSON> <@micha<PERSON>-c<PERSON><PERSON><PERSON>>"}], "repository": {"type": "git", "url": "git+https://github.com/webpack-contrib/schema-utils.git"}, "bugs": {"url": "https://github.com/webpack-contrib/schema-utils/issues"}, "homepage": "https://github.com/webpack-contrib/schema-utils#readme", "license": "MIT", "__npminstall_done": true, "_from": "schema-utils@0.3.0", "_resolved": "https://registry.npmmirror.com/schema-utils/-/schema-utils-0.3.0.tgz"}