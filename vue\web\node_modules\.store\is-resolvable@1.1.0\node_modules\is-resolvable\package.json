{"name": "is-resolvable", "version": "1.1.0", "description": "Check if a module ID is resolvable with require()", "repository": "shinnn/is-resolvable", "author": "<PERSON><PERSON><PERSON> (https://github.com/shinnn)", "scripts": {"pretest": "eslint --fix --format=codeframe index.js test.js", "test": "node --throw-deprecation --track-heap-objects test.js", "coverage": "istanbul cover --print=both test.js"}, "license": "ISC", "files": ["index.js"], "keywords": ["file", "path", "resolve", "resolvable", "check", "module"], "devDependencies": {"@shinnn/eslint-config-node": "^5.0.0", "eslint": "^4.16.0", "istanbul": "^0.4.5", "tape": "^4.8.0"}, "eslintConfig": {"extends": "@shinnn/node", "rules": {"no-var": "off", "prefer-template": "off"}}, "__npminstall_done": true, "_from": "is-resolvable@1.1.0", "_resolved": "https://registry.npmmirror.com/is-resolvable/-/is-resolvable-1.1.0.tgz"}