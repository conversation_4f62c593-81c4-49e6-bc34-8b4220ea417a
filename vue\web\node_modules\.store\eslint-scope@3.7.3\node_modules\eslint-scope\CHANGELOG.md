v3.7.1 - April 12, 2017

* ced6262 Fix: restore previous Scope API exports from escope (#31) (Vitor Balocco)
* 5c3d966 Fix: Remove and Modify tests that contain invalid ES6 syntax (#29) (<PERSON><PERSON>)

v3.7.0 - March 17, 2017

* 9e27835 Chore: Add files section to package.json (#24) (<PERSON><PERSON>)
* 3e4d123 Upgrade: eslint-config-eslint to 4.0.0 (#21) (<PERSON>)
* 38c50fb Chore: Rename src to lib and test to tests (#20) (<PERSON>)
* f4cd920 Chore: Remove esprima (#19) (<PERSON>)
* f81fad5 Revert "Chore: Remove esprima" (#18) (<PERSON>)
* 31b0085 Chore: Remove es6-map and es6-weakmap as they are included in node4 (#10) (#13) (<PERSON>)
* 12a1ca1 Add Makefile.js and eslint (#15) (<PERSON><PERSON>)
* 7d23f8e Chore: Remove es6-map and es6-weakmap as they are included in node4 (#10) (<PERSON>)
* 019441e Chore: Convert to ES6 that is supported on Node 4, commonjs modules and remove Babel (#14) (<PERSON>lton)
* c647f65 Update: Add check for node.body in referencer (#2) (Corbin Uselton)
* eb5c9db Remove browserify and jsdoc (#12) (Corbin Uselton)
* cf38df0 Chore: Update README.md (#3) (James Henry)
* 8a142ca Chore: Add eslint-release scripts (#6) (James Henry)
* e60d8cb Chore: Remove unused bower.json (#5) (James Henry)
* 049c545 Chore: Fix tests for eslint-scope (#4) (James Henry)
* f026aab Chore: Update package.json for eslint fork (#1) (James Henry)
* a94d281 Chore: Update license with JSF copyright (Nicholas C. Zakas)

