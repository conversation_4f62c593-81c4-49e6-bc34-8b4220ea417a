{"name": "regenerator-runtime", "author": "<PERSON> <<EMAIL>>", "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.14.1", "main": "runtime.js", "keywords": ["regenerator", "runtime", "generator", "async"], "sideEffects": true, "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/main/packages/runtime"}, "license": "MIT", "__npminstall_done": true, "_from": "regenerator-runtime@0.14.1", "_resolved": "https://registry.npmmirror.com/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz"}