Microsoft Windows [版本 10.0.19045.6093]
(c) Microsoft Corporation。保留所有权利。

C:\Users\<USER>\Desktop\p\vue\web>npm run build

> longbing_back@1.0.0 build
> node build/build.js

- building for production...(node:18912) Warning: Accessing non-existent property 'cat' of module exports inside circular dependency
(Use `node --trace-warnings ...` to show where the warning was created)
(node:18912) Warning: Accessing non-existent property 'cd' of module exports inside circular dependency
(node:18912) Warning: Accessing non-existent property 'chmod' of module exports inside circular dependency
(node:18912) Warning: Accessing non-existent property 'cp' of module exports inside circular dependency
(node:18912) Warning: Accessing non-existent property 'dirs' of module exports inside circular dependency
(node:18912) Warning: Accessing non-existent property 'pushd' of module exports inside circular dependency
(node:18912) Warning: Accessing non-existent property 'popd' of module exports inside circular dependency
(node:18912) Warning: Accessing non-existent property 'echo' of module exports inside circular dependency
(node:18912) Warning: Accessing non-existent property 'tempdir' of module exports inside circular dependency
(node:18912) Warning: Accessing non-existent property 'pwd' of module exports inside circular dependency
(node:18912) Warning: Accessing non-existent property 'exec' of module exports inside circular dependency
(node:18912) Warning: Accessing non-existent property 'ls' of module exports inside circular dependency
(node:18912) Warning: Accessing non-existent property 'find' of module exports inside circular dependency
(node:18912) Warning: Accessing non-existent property 'grep' of module exports inside circular dependency
(node:18912) Warning: Accessing non-existent property 'head' of module exports inside circular dependency
(node:18912) Warning: Accessing non-existent property 'ln' of module exports inside circular dependency
(node:18912) Warning: Accessing non-existent property 'mkdir' of module exports inside circular dependency
(node:18912) Warning: Accessing non-existent property 'rm' of module exports inside circular dependency
(node:18912) Warning: Accessing non-existent property 'mv' of module exports inside circular dependency
(node:18912) Warning: Accessing non-existent property 'sed' of module exports inside circular dependency
(node:18912) Warning: Accessing non-existent property 'set' of module exports inside circular dependency
(node:18912) Warning: Accessing non-existent property 'sort' of module exports inside circular dependency
(node:18912) Warning: Accessing non-existent property 'tail' of module exports inside circular dependency
(node:18912) Warning: Accessing non-existent property 'test' of module exports inside circular dependency
(node:18912) Warning: Accessing non-existent property 'to' of module exports inside circular dependency
(node:18912) Warning: Accessing non-existent property 'toEnd' of module exports inside circular dependency
(node:18912) Warning: Accessing non-existent property 'touch' of module exports inside circular dependency
(node:18912) Warning: Accessing non-existent property 'uniq' of module exports inside circular dependency
(node:18912) Warning: Accessing non-existent property 'which' of module exports inside circular dependency
Hash: 86ae6a934e3ea5988e18
Version: webpack 3.12.0
Time: 7137ms
                                                                     Asset       Size  Chunks                    Chunk Names
                                      static/js/vendor.js?v=20250805132452    7.44 kB       0  [emitted]         vendor
                                         static/js/app.js?v=20250805132452     3.1 kB       1  [emitted]         app
                                    static/js/manifest.js?v=20250805132452  800 bytes       2  [emitted]         manifest
                                                                index.html  773 bytes          [emitted]
             static/Ueditor/dialogs/attachment/fileTypeImages/icon_chm.gif  923 bytes          [emitted]
                                 static/Ueditor/dialogs/anchor/anchor.html    1.59 kB          [emitted]
                          static/Ueditor/dialogs/attachment/attachment.css    14.4 kB          [emitted]
                           static/Ueditor/dialogs/attachment/attachment.js    30.4 kB          [emitted]
                         static/Ueditor/dialogs/attachment/attachment.html    2.31 kB          [emitted]
             static/Ueditor/dialogs/attachment/fileTypeImages/icon_exe.gif  949 bytes          [emitted]
             static/Ueditor/dialogs/attachment/fileTypeImages/icon_doc.gif    1.01 kB          [emitted]
         static/Ueditor/dialogs/attachment/fileTypeImages/icon_default.png  841 bytes          [emitted]
             static/Ueditor/dialogs/attachment/fileTypeImages/icon_jpg.gif  950 bytes          [emitted]
             static/Ueditor/dialogs/attachment/fileTypeImages/icon_mp3.gif  986 bytes          [emitted]
              static/Ueditor/dialogs/attachment/fileTypeImages/icon_mv.gif       1 kB          [emitted]
             static/Ueditor/dialogs/attachment/fileTypeImages/icon_pdf.gif  996 bytes          [emitted]
             static/Ueditor/dialogs/attachment/fileTypeImages/icon_ppt.gif       1 kB          [emitted]
             static/Ueditor/dialogs/attachment/fileTypeImages/icon_psd.gif    1.01 kB          [emitted]
             static/Ueditor/dialogs/attachment/fileTypeImages/icon_txt.gif  970 bytes          [emitted]
             static/Ueditor/dialogs/attachment/fileTypeImages/icon_rar.gif    1.01 kB          [emitted]
             static/Ueditor/dialogs/attachment/fileTypeImages/icon_xls.gif       1 kB          [emitted]
                    static/Ueditor/dialogs/attachment/images/alignicon.gif    1.05 kB          [emitted]
                    static/Ueditor/dialogs/attachment/images/alignicon.png    3.71 kB          [emitted]
                           static/Ueditor/dialogs/attachment/images/bg.png    2.81 kB          [emitted]
                      static/Ueditor/dialogs/attachment/images/success.gif  445 bytes          [emitted]
                   static/Ueditor/dialogs/attachment/images/file-icons.gif    20.1 kB          [emitted]
                        static/Ueditor/dialogs/attachment/images/image.png    1.67 kB          [emitted]
                   static/Ueditor/dialogs/attachment/images/file-icons.png    44.1 kB          [emitted]
                        static/Ueditor/dialogs/attachment/images/icons.png    2.68 kB          [emitted]
                        static/Ueditor/dialogs/attachment/images/icons.gif  453 bytes          [emitted]
                     static/Ueditor/dialogs/attachment/images/progress.png    1.27 kB          [emitted]
                      static/Ueditor/dialogs/attachment/images/success.png    1.62 kB          [emitted]
                          static/Ueditor/dialogs/background/background.css     2.4 kB          [emitted]
                         static/Ueditor/dialogs/background/background.html     2.9 kB          [emitted]
                           static/Ueditor/dialogs/background/background.js    14.4 kB          [emitted]
                           static/Ueditor/dialogs/background/images/bg.png    2.81 kB          [emitted]
                                  static/Ueditor/dialogs/charts/charts.css     2.6 kB          [emitted]
                      static/Ueditor/dialogs/background/images/success.png    1.62 kB          [emitted]
                                 static/Ueditor/dialogs/charts/charts.html     4.8 kB          [emitted]
                             static/Ueditor/dialogs/charts/chart.config.js    1.41 kB          [emitted]
                          static/Ueditor/dialogs/charts/images/charts1.png    19.3 kB          [emitted]
                                   static/Ueditor/dialogs/charts/charts.js    11.1 kB          [emitted]
                          static/Ueditor/dialogs/charts/images/charts2.png      23 kB          [emitted]
                          static/Ueditor/dialogs/charts/images/charts0.png    25.3 kB          [emitted]
                          static/Ueditor/dialogs/charts/images/charts3.png    7.82 kB          [emitted]
                          static/Ueditor/dialogs/charts/images/charts4.png    8.34 kB          [emitted]
                                static/Ueditor/dialogs/emotion/emotion.css     1.8 kB          [emitted]
                               static/Ueditor/dialogs/emotion/images/0.gif   43 bytes          [emitted]
                                 static/Ueditor/dialogs/emotion/emotion.js    6.29 kB          [emitted]
                               static/Ueditor/dialogs/emotion/emotion.html     5.7 kB          [emitted]
                          static/Ueditor/dialogs/charts/images/charts5.png    47.1 kB          [emitted]
                           static/Ueditor/dialogs/emotion/images/cface.gif     8.6 kB          [emitted]
                           static/Ueditor/dialogs/emotion/images/bface.gif    27.2 kB          [emitted]
                           static/Ueditor/dialogs/emotion/images/fface.gif    18.5 kB          [emitted]
                                     static/Ueditor/dialogs/gmap/gmap.html    4.05 kB          [emitted]
                           static/Ueditor/dialogs/emotion/images/tface.gif    19.8 kB          [emitted]
                         static/Ueditor/dialogs/emotion/images/jxface2.gif    40.7 kB          [emitted]
                static/Ueditor/dialogs/emotion/images/neweditor-tab-bg.png  216 bytes          [emitted]
                                      static/Ueditor/dialogs/help/help.css  389 bytes          [emitted]
                           static/Ueditor/dialogs/emotion/images/wface.gif    49.9 kB          [emitted]
                                       static/Ueditor/dialogs/help/help.js     1.5 kB          [emitted]
                           static/Ueditor/dialogs/emotion/images/yface.gif    28.4 kB          [emitted]
                                     static/Ueditor/dialogs/help/help.html    2.84 kB          [emitted]
                                   static/Ueditor/dialogs/image/image.html    5.61 kB          [emitted]
                                    static/Ueditor/dialogs/image/image.css    18.1 kB          [emitted]
                             static/Ueditor/dialogs/image/images/icons.gif  453 bytes          [emitted]
                                static/Ueditor/dialogs/image/images/bg.png    2.81 kB          [emitted]
                             static/Ueditor/dialogs/image/images/icons.png    2.68 kB          [emitted]
                             static/Ueditor/dialogs/image/images/image.png    1.67 kB          [emitted]
                          static/Ueditor/dialogs/image/images/progress.png    1.27 kB          [emitted]
                           static/Ueditor/dialogs/image/images/success.gif  445 bytes          [emitted]
                           static/Ueditor/dialogs/image/images/success.png    1.62 kB          [emitted]
                                     static/Ueditor/dialogs/image/image.js     128 kB          [emitted]
                       static/Ueditor/dialogs/insertframe/insertframe.html    4.28 kB          [emitted]
                                        static/Ueditor/dialogs/internal.js     2.6 kB          [emitted]
                         static/Ueditor/dialogs/image/images/alignicon.jpg    16.1 kB          [emitted]
                                     static/Ueditor/dialogs/link/link.html    4.39 kB          [emitted]
                                      static/Ueditor/dialogs/map/show.html    4.97 kB          [emitted]
                                    static/Ueditor/dialogs/music/music.css    1.68 kB          [emitted]
                                       static/Ueditor/dialogs/map/map.html    6.01 kB          [emitted]
                           static/Ueditor/dialogs/scrawl/images/addimg.png  628 bytes          [emitted]
                            static/Ueditor/dialogs/scrawl/images/brush.png  608 bytes          [emitted]
                                   static/Ueditor/dialogs/music/music.html  950 bytes          [emitted]
                            static/Ueditor/dialogs/scrawl/images/empty.png  519 bytes          [emitted]
                          static/Ueditor/dialogs/scrawl/images/delimgH.png  578 bytes          [emitted]
                           static/Ueditor/dialogs/scrawl/images/delimg.png  516 bytes          [emitted]
                                     static/Ueditor/dialogs/music/music.js    7.82 kB          [emitted]
                           static/Ueditor/dialogs/scrawl/images/emptyH.png  657 bytes          [emitted]
                               static/Ueditor/dialogs/preview/preview.html    1.18 kB          [emitted]
                            static/Ueditor/dialogs/scrawl/images/scale.png  435 bytes          [emitted]
                           static/Ueditor/dialogs/scrawl/images/eraser.png    43.3 kB          [emitted]
                             static/Ueditor/dialogs/scrawl/images/redo.png  454 bytes          [emitted]
                            static/Ueditor/dialogs/scrawl/images/redoH.png  536 bytes          [emitted]
                            static/Ueditor/dialogs/scrawl/images/undoH.png  511 bytes          [emitted]
                           static/Ueditor/dialogs/scrawl/images/scaleH.png  330 bytes          [emitted]
                             static/Ueditor/dialogs/scrawl/images/undo.png  444 bytes          [emitted]
                                  static/Ueditor/dialogs/scrawl/scrawl.css    3.81 kB          [emitted]
                                 static/Ueditor/dialogs/scrawl/scrawl.html    3.93 kB          [emitted]
                             static/Ueditor/dialogs/scrawl/images/size.png  775 bytes          [emitted]
                     static/Ueditor/dialogs/searchreplace/searchreplace.js     4.4 kB          [emitted]
                                   static/Ueditor/dialogs/scrawl/scrawl.js    27.2 kB          [emitted]
                         static/Ueditor/dialogs/snapscreen/snapscreen.html    1.92 kB          [emitted]
                   static/Ueditor/dialogs/searchreplace/searchreplace.html    4.18 kB          [emitted]
                             static/Ueditor/dialogs/spechars/spechars.html  829 bytes          [emitted]
                               static/Ueditor/dialogs/table/edittable.html    2.39 kB          [emitted]
                                 static/Ueditor/dialogs/table/dragicon.png  304 bytes          [emitted]
                                  static/Ueditor/dialogs/table/edittd.html    1.56 kB          [emitted]
                               static/Ueditor/dialogs/spechars/spechars.js    4.67 kB          [emitted]
                                static/Ueditor/dialogs/table/edittable.css    1.19 kB          [emitted]
                             static/Ueditor/dialogs/template/images/bg.gif   84 bytes          [emitted]
                                 static/Ueditor/dialogs/table/edittable.js    8.93 kB          [emitted]
                                 static/Ueditor/dialogs/table/edittip.html  863 bytes          [emitted]
                           static/Ueditor/dialogs/template/images/pre0.png  250 bytes          [emitted]
                           static/Ueditor/dialogs/template/images/pre2.png  394 bytes          [emitted]
                           static/Ueditor/dialogs/template/images/pre4.png  393 bytes          [emitted]
                           static/Ueditor/dialogs/template/images/pre1.png  291 bytes          [emitted]
                                 static/Ueditor/dialogs/template/config.js    12.5 kB          [emitted]
                           static/Ueditor/dialogs/template/images/pre3.png  485 bytes          [emitted]
                              static/Ueditor/dialogs/template/template.css    1.03 kB          [emitted]
                               static/Ueditor/dialogs/template/template.js    1.59 kB          [emitted]
                             static/Ueditor/dialogs/template/template.html  922 bytes          [emitted]
                             static/Ueditor/dialogs/video/images/icons.gif  453 bytes          [emitted]
                        static/Ueditor/dialogs/video/images/file-icons.png    44.1 kB          [emitted]
                      static/Ueditor/dialogs/video/images/center_focus.jpg    11.8 kB          [emitted]
                        static/Ueditor/dialogs/video/images/file-icons.gif    20.1 kB          [emitted]
                                static/Ueditor/dialogs/video/images/bg.png    2.81 kB          [emitted]
                             static/Ueditor/dialogs/video/images/icons.png    2.68 kB          [emitted]
                          static/Ueditor/dialogs/video/images/progress.png    1.27 kB          [emitted]
                        static/Ueditor/dialogs/video/images/left_focus.jpg    11.4 kB          [emitted]
                             static/Ueditor/dialogs/video/images/image.png    1.67 kB          [emitted]
                       static/Ueditor/dialogs/video/images/right_focus.jpg    11.3 kB          [emitted]
                           static/Ueditor/dialogs/video/images/success.gif  445 bytes          [emitted]
                           static/Ueditor/dialogs/video/images/success.png    1.62 kB          [emitted]
                        static/Ueditor/dialogs/video/images/none_focus.jpg    11.5 kB          [emitted]
                                   static/Ueditor/dialogs/video/video.html    4.19 kB          [emitted]
                                    static/Ueditor/dialogs/video/video.css    14.9 kB          [emitted]
                                 static/Ueditor/dialogs/webapp/webapp.html    2.38 kB          [emitted]
                   static/Ueditor/dialogs/wordimage/fClipboard_ueditor.swf    1.91 kB          [emitted]
                                     static/Ueditor/dialogs/video/video.js    30.5 kB          [emitted]
                           static/Ueditor/dialogs/wordimage/wordimage.html    6.38 kB          [emitted]
                               static/Ueditor/dialogs/wordimage/tangram.js    47.5 kB          [emitted]
                                static/Ueditor/lang/en/images/addimage.png    3.37 kB          [emitted]
                        static/Ueditor/dialogs/wordimage/imageUploader.swf    62.9 kB          [emitted]
                                                 static/Ueditor/index.html     6.6 kB          [emitted]
                   static/Ueditor/lang/en/images/alldeletebtnhoverskin.png  743 bytes          [emitted]
                      static/Ueditor/lang/en/images/alldeletebtnupskin.png  743 bytes          [emitted]
                             static/Ueditor/dialogs/wordimage/wordimage.js    4.22 kB          [emitted]
                              static/Ueditor/lang/en/images/background.png    3.85 kB          [emitted]
                                              static/Ueditor/lang/en/en.js    29.4 kB          [emitted]
                                  static/Ueditor/lang/en/images/button.png    4.93 kB          [emitted]
                                    static/Ueditor/lang/en/images/copy.png    1.22 kB          [emitted]
                              static/Ueditor/lang/en/images/localimage.png    3.08 kB          [emitted]
                           static/Ueditor/lang/en/images/deletedisable.png  649 bytes          [emitted]
                                   static/Ueditor/lang/en/images/music.png    91.6 kB          [emitted]
                      static/Ueditor/lang/en/images/rotaterightdisable.png  754 bytes          [emitted]
                            static/Ueditor/lang/en/images/deleteenable.png  664 bytes          [emitted]
                          static/Ueditor/lang/en/images/listbackground.png    3.75 kB          [emitted]
                        static/Ueditor/lang/en/images/rotateleftenable.png  952 bytes          [emitted]
                       static/Ueditor/lang/en/images/rotateleftdisable.png  719 bytes          [emitted]
                                static/Ueditor/lang/zh-cn/images/music.png    23.1 kB          [emitted]
                           static/Ueditor/lang/zh-cn/images/localimage.png    6.98 kB          [emitted]
                                  static/Ueditor/lang/en/images/upload.png    3.94 kB          [emitted]
                                 static/Ueditor/lang/zh-cn/images/copy.png    4.32 kB          [emitted]
                               static/Ueditor/lang/zh-cn/images/upload.png    6.61 kB          [emitted]
                       static/Ueditor/lang/en/images/rotaterightenable.png    1.01 kB          [emitted]
                                        static/Ueditor/php/action_list.php     2.9 kB          [emitted]
                                        static/Ueditor/lang/zh-cn/zh-cn.js    29.3 kB          [emitted]
                                     static/Ueditor/php/action_crawler.php    1.09 kB          [emitted]
                                        static/Ueditor/php/action_data.php  820 bytes          [emitted]
                                        static/Ueditor/php/action_site.php  873 bytes          [emitted]
                                      static/Ueditor/php/action_upload.php    1.99 kB          [emitted]
                                            static/Ueditor/php/config.json    6.17 kB          [emitted]
                             static/Ueditor/themes/default/css/ueditor.css    44.3 kB          [emitted]
                           static/Ueditor/themes/default/images/anchor.gif  184 bytes          [emitted]
                                     static/Ueditor/php/Uploader.class.php    12.6 kB          [emitted]
                                         static/Ueditor/php/controller.php    2.42 kB          [emitted]
                         static/Ueditor/themes/default/css/ueditor.min.css    34.9 kB          [emitted]
                         static/Ueditor/themes/default/images/arrow_up.png    1.65 kB          [emitted]
                       static/Ueditor/themes/default/images/arrow_down.png    1.61 kB          [emitted]
                              static/Ueditor/themes/default/dialogbase.css    1.69 kB          [emitted]
                         static/Ueditor/themes/default/images/cursor_h.gif  253 bytes          [emitted]
                           static/Ueditor/themes/default/images/charts.png  518 bytes          [emitted]
                            static/Ueditor/themes/default/images/arrow.png    1.17 kB          [emitted]
                     static/Ueditor/themes/default/images/cancelbutton.gif    1.23 kB          [emitted]
                         static/Ueditor/themes/default/images/cursor_h.png  175 bytes          [emitted]
                         static/Ueditor/themes/default/images/cursor_v.png  177 bytes          [emitted]
                         static/Ueditor/themes/default/images/cursor_v.gif  370 bytes          [emitted]
                  static/Ueditor/themes/default/images/dialog-title-bg.png  938 bytes          [emitted]
                      static/Ueditor/themes/default/images/highlighted.gif  111 bytes          [emitted]
                        static/Ueditor/themes/default/images/icons-all.gif    3.75 kB          [emitted]
                            static/Ueditor/themes/default/images/icons.gif    20.9 kB          [emitted]
                        static/Ueditor/themes/default/images/loaderror.png    3.21 kB          [emitted]
                 static/Ueditor/themes/default/images/neweditor-tab-bg.png  216 bytes          [emitted]
                         static/Ueditor/themes/default/images/filescan.png    4.28 kB          [emitted]
                            static/Ueditor/themes/default/images/icons.png    19.7 kB          [emitted]
                        static/Ueditor/themes/default/images/button-bg.gif    1.11 kB          [emitted]
                          static/Ueditor/themes/default/images/loading.gif  734 bytes          [emitted]
                            static/Ueditor/themes/default/images/scale.png  167 bytes          [emitted]
                        static/Ueditor/themes/default/images/pagebreak.gif   54 bytes          [emitted]
                             static/Ueditor/themes/default/images/lock.gif    1.06 kB          [emitted]
                         static/Ueditor/themes/default/images/sortable.png    2.85 kB          [emitted]
                           static/Ueditor/themes/default/images/spacer.gif   43 bytes          [emitted]
                       static/Ueditor/themes/default/images/sparator_v.png  122 bytes          [emitted]
                 static/Ueditor/themes/default/images/table-cell-align.png    1.85 kB          [emitted]
                       static/Ueditor/themes/default/images/toolbar_bg.png  170 bytes          [emitted]
                    static/Ueditor/themes/default/images/unhighlighted.gif  111 bytes          [emitted]
                        static/Ueditor/themes/default/images/videologo.gif     1.6 kB          [emitted]
                                          static/Ueditor/themes/iframe.css   41 bytes          [emitted]
                             static/Ueditor/themes/default/images/word.gif    1.02 kB          [emitted]
              static/Ueditor/themes/default/images/tangram-colorpicker.png    17.4 kB          [emitted]
                      static/Ueditor/third-party/codemirror/codemirror.css    2.89 kB          [emitted]
                           static/Ueditor/themes/default/images/upload.png    6.61 kB          [emitted]
        static/Ueditor/third-party/highcharts/adapters/mootools-adapter.js    2.26 kB          [emitted]
                        static/Ueditor/themes/default/images/wordpaste.png    6.47 kB          [emitted]
    static/Ueditor/third-party/highcharts/adapters/mootools-adapter.src.js    7.79 kB          [emitted]
                       static/Ueditor/third-party/codemirror/codemirror.js     159 kB          [emitted]
       static/Ueditor/third-party/highcharts/adapters/prototype-adapter.js    3.31 kB          [emitted]
    static/Ueditor/third-party/highcharts/adapters/standalone-framework.js    5.07 kB          [emitted]
   static/Ueditor/third-party/highcharts/adapters/prototype-adapter.src.js    8.93 kB          [emitted]
                  static/Ueditor/third-party/highcharts/highcharts-more.js    21.8 kB          [emitted]
              static/Ueditor/third-party/highcharts/highcharts-more.src.js    60.2 kB          [emitted]
static/Ueditor/third-party/highcharts/adapters/standalone-framework.src.js    11.7 kB          [emitted]
              static/Ueditor/third-party/highcharts/modules/annotations.js     3.4 kB          [emitted]
                       static/Ueditor/third-party/highcharts/highcharts.js     141 kB          [emitted]
         static/Ueditor/third-party/highcharts/modules/canvas-tools.src.js     101 kB          [emitted]
                     static/Ueditor/third-party/highcharts/modules/data.js    4.44 kB          [emitted]
             static/Ueditor/third-party/highcharts/modules/canvas-tools.js    57.9 kB          [emitted]
                   static/Ueditor/third-party/highcharts/highcharts.src.js     444 kB          [emitted]  [big]
                static/Ueditor/third-party/highcharts/modules/drilldown.js    5.55 kB          [emitted]
          static/Ueditor/third-party/highcharts/modules/annotations.src.js    8.38 kB          [emitted]
                static/Ueditor/third-party/highcharts/modules/exporting.js    7.25 kB          [emitted]
                 static/Ueditor/third-party/highcharts/modules/data.src.js    15.5 kB          [emitted]
            static/Ueditor/third-party/highcharts/modules/exporting.src.js    17.3 kB          [emitted]
                   static/Ueditor/third-party/highcharts/modules/funnel.js    1.97 kB          [emitted]
                  static/Ueditor/third-party/highcharts/modules/heatmap.js  535 bytes          [emitted]
              static/Ueditor/third-party/highcharts/modules/heatmap.src.js    1.13 kB          [emitted]
               static/Ueditor/third-party/highcharts/modules/funnel.src.js    6.54 kB          [emitted]
            static/Ueditor/third-party/highcharts/modules/drilldown.src.js    10.9 kB          [emitted]
                  static/Ueditor/third-party/highcharts/modules/map.src.js    25.8 kB          [emitted]
       static/Ueditor/third-party/highcharts/modules/no-data-to-display.js    1.36 kB          [emitted]
                      static/Ueditor/third-party/highcharts/modules/map.js    10.1 kB          [emitted]
   static/Ueditor/third-party/highcharts/modules/no-data-to-display.src.js    2.86 kB          [emitted]
                static/Ueditor/third-party/highcharts/themes/dark-green.js    4.34 kB          [emitted]
                     static/Ueditor/third-party/highcharts/themes/skies.js    1.76 kB          [emitted]
                 static/Ueditor/third-party/highcharts/themes/dark-blue.js    4.35 kB          [emitted]
                      static/Ueditor/third-party/highcharts/themes/grid.js    1.79 kB          [emitted]
                               static/Ueditor/third-party/jquery-1.10.2.js     273 kB          [emitted]  [big]
                      static/Ueditor/third-party/highcharts/themes/gray.js    4.49 kB          [emitted]
                           static/Ueditor/third-party/jquery-1.10.2.min.js    93.1 kB          [emitted]
            static/Ueditor/third-party/SyntaxHighlighter/shCoreDefault.css    7.12 kB          [emitted]
                          static/Ueditor/third-party/video-js/font/vjs.eot    3.54 kB          [emitted]
                          static/Ueditor/third-party/video-js/font/vjs.ttf    3.37 kB          [emitted]
                          static/Ueditor/third-party/jquery-1.10.2.min.map     140 kB          [emitted]
               static/Ueditor/third-party/snapscreen/UEditorSnapscreen.exe     520 kB          [emitted]  [big]
                    static/Ueditor/third-party/SyntaxHighlighter/shCore.js     160 kB          [emitted]
                         static/Ueditor/third-party/video-js/font/vjs.woff    4.23 kB          [emitted]
                          static/Ueditor/third-party/video-js/font/vjs.svg    9.87 kB          [emitted]
                      static/Ueditor/third-party/video-js/video-js.min.css    11.5 kB          [emitted]
                       static/Ueditor/third-party/webuploader/Uploader.swf    49.4 kB          [emitted]
                    static/Ueditor/third-party/webuploader/webuploader.css  515 bytes          [emitted]
                          static/Ueditor/third-party/video-js/video-js.css    21.3 kB          [emitted]
                          static/Ueditor/third-party/video-js/video.dev.js     215 kB          [emitted]
                          static/Ueditor/third-party/video-js/video-js.swf    16.7 kB          [emitted]
                              static/Ueditor/third-party/video-js/video.js    55.4 kB          [emitted]
          static/Ueditor/third-party/webuploader/webuploader.custom.min.js    46.9 kB          [emitted]
           static/Ueditor/third-party/webuploader/webuploader.flashonly.js     139 kB          [emitted]
       static/Ueditor/third-party/webuploader/webuploader.flashonly.min.js    33.6 kB          [emitted]
              static/Ueditor/third-party/webuploader/webuploader.custom.js     198 kB          [emitted]
                 static/Ueditor/third-party/webuploader/webuploader.min.js    58.3 kB          [emitted]
           static/Ueditor/third-party/webuploader/webuploader.html5only.js     187 kB          [emitted]
       static/Ueditor/third-party/webuploader/webuploader.html5only.min.js    47.1 kB          [emitted]
                                     static/Ueditor/third-party/xss.min.js    27.8 kB          [emitted]
    static/Ueditor/third-party/webuploader/webuploader.withoutimage.min.js    39.8 kB          [emitted]
                static/Ueditor/third-party/zeroclipboard/ZeroClipboard.swf    3.93 kB          [emitted]
                     static/Ueditor/third-party/webuploader/webuploader.js     232 kB          [emitted]
                 static/Ueditor/third-party/zeroclipboard/ZeroClipboard.js    43.2 kB          [emitted]
             static/Ueditor/third-party/zeroclipboard/ZeroClipboard.min.js      19 kB          [emitted]
                                       static/Ueditor/ueditor.parse.min.js    14.8 kB          [emitted]
        static/Ueditor/third-party/webuploader/webuploader.withoutimage.js     152 kB          [emitted]
                                          static/Ueditor/ueditor.config.js      22 kB          [emitted]
                                         static/Ueditor/ueditor.all.min.js     382 kB          [emitted]  [big]
                                           static/Ueditor/ueditor.parse.js    31.6 kB          [emitted]
                                             static/Ueditor/ueditor.all.js    1.23 MB          [emitted]  [big]

WARNING in ./src/main.js

  ✘  https://google.com/#q=import%2Fno-duplicates  Resolve error: Cannot find module 'resolve'
Require stack:
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\eslint-import-resolver-node\index.js
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\eslint-module-utils@2.12.1\node_modules\eslint-module-utils\resolve.js
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\eslint-plugin-import@2.32.0\node_modules\eslint-plugin-import\lib\rules\no-unresolved.js
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\eslint-plugin-import@2.32.0\node_modules\eslint-plugin-import\lib\index.js
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\eslint@4.19.1\node_modules\eslint\lib\config\plugins.js
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\eslint@4.19.1\node_modules\eslint\lib\config.js
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\eslint@4.19.1\node_modules\eslint\lib\cli-engine.js
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\eslint@4.19.1\node_modules\eslint\lib\api.js
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\eslint-loader@2.2.1\node_modules\eslint-loader\index.js
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\loader-runner@2.4.0\node_modules\loader-runner\lib\loadLoader.js
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\loader-runner@2.4.0\node_modules\loader-runner\lib\LoaderRunner.js
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\webpack@3.12.0\node_modules\webpack\lib\NormalModule.js
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\webpack@3.12.0\node_modules\webpack\lib\NormalModuleFactory.js
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\webpack@3.12.0\node_modules\webpack\lib\Compiler.js
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\webpack@3.12.0\node_modules\webpack\lib\webpack.js
- C:\Users\<USER>\Desktop\p\vue\web\build\build.js
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\p\vue\web\node_modules\eslint-import-resolver-node\index.js:1:15)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
  src\main.js:1:1
  /*
   ^

  ✘  https://google.com/#q=import%2Ffirst          Import in body of module; reorder to top                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     
  src\main.js:42:1
  import directives from './directives'
   ^


✘ 2 problems (2 errors, 0 warnings)


Errors:
  1  https://google.com/#q=import%2Fno-duplicates
  1  https://google.com/#q=import%2Ffirst
 @ multi babel-polyfill ./src/main.js

ERROR in ./src/main.js
Module build failed: Error: Cannot find module 'browserslist'
Require stack:
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\babel-preset-env\lib\targets-parser.js
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\babel-preset-env\lib\index.js
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\babel-core@6.26.3\node_modules\babel-core\lib\transformation\file\options\option-manager.js
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\babel-core@6.26.3\node_modules\babel-core\lib\transformation\file\index.js
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\babel-core@6.26.3\node_modules\babel-core\lib\api\node.js
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\babel-core@6.26.3\node_modules\babel-core\index.js
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\babel-loader@7.1.5\node_modules\babel-loader\lib\index.js
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\loader-runner@2.4.0\node_modules\loader-runner\lib\loadLoader.js
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\loader-runner@2.4.0\node_modules\loader-runner\lib\LoaderRunner.js
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\webpack@3.12.0\node_modules\webpack\lib\NormalModule.js
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\webpack@3.12.0\node_modules\webpack\lib\NormalModuleFactory.js
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\webpack@3.12.0\node_modules\webpack\lib\Compiler.js
- C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\webpack@3.12.0\node_modules\webpack\lib\webpack.js
- C:\Users\<USER>\Desktop\p\vue\web\build\build.js (While processing preset: "C:\\Users\\<USER>\\Desktop\\p\\vue\\web\\node_modules\\babel-preset-env\\lib\\index.js")
    at Function._resolveFilename (node:internal/modules/cjs/loader:1401:15)
    at defaultResolveImpl (node:internal/modules/cjs/loader:1057:19)
    at resolveForCJSWithHooks (node:internal/modules/cjs/loader:1062:22)
    at Function._load (node:internal/modules/cjs/loader:1211:37)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
    at require (node:internal/modules/helpers:135:16)
    at Object.<anonymous> (C:\Users\<USER>\Desktop\p\vue\web\node_modules\babel-preset-env\lib\targets-parser.js:5:21)
    at Module._compile (node:internal/modules/cjs/loader:1730:14)
    at Object..js (node:internal/modules/cjs/loader:1895:10)
    at Module.load (node:internal/modules/cjs/loader:1465:32)
    at Function._load (node:internal/modules/cjs/loader:1282:12)
    at TracingChannel.traceSync (node:diagnostics_channel:322:14)
    at wrapModuleLoad (node:internal/modules/cjs/loader:235:24)
    at Module.require (node:internal/modules/cjs/loader:1487:12)
 @ multi babel-polyfill ./src/main.js

ERROR in ./node_modules/babel-polyfill/lib/index.js
Module not found: Error: Can't resolve 'core-js/fn/regexp/escape' in 'C:\Users\<USER>\Desktop\p\vue\web\node_modules\babel-polyfill\lib'
 @ ./node_modules/babel-polyfill/lib/index.js 7:0-35
 @ multi babel-polyfill ./src/main.js

ERROR in ./node_modules/babel-polyfill/lib/index.js
Module not found: Error: Can't resolve 'core-js/shim' in 'C:\Users\<USER>\Desktop\p\vue\web\node_modules\babel-polyfill\lib'
 @ ./node_modules/babel-polyfill/lib/index.js 3:0-23
 @ multi babel-polyfill ./src/main.js

  Build failed with errors.


C:\Users\<USER>\Desktop\p\vue\web>