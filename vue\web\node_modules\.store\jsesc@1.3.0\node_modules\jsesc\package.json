{"name": "jsesc", "version": "1.3.0", "description": "A JavaScript library for escaping JavaScript strings while generating the shortest possible valid output.", "homepage": "https://mths.be/jsesc", "main": "jsesc.js", "bin": "bin/jsesc", "man": "man/jsesc.1", "keywords": ["string", "escape", "javascript", "tool"], "license": "MIT", "author": {"name": "<PERSON>", "url": "https://mathiasbynens.be/"}, "repository": {"type": "git", "url": "https://github.com/mathiasbynens/jsesc.git"}, "bugs": "https://github.com/mathiasbynens/jsesc/issues", "files": ["LICENSE-MIT.txt", "jsesc.js", "bin/", "man/"], "scripts": {"test": "node tests/tests.js", "build": "grunt template"}, "devDependencies": {"coveralls": "^2.11.6", "grunt": "^0.4.5", "grunt-shell": "^1.1.2", "grunt-template": "^0.2.3", "istanbul": "^0.4.2", "qunit-extras": "^1.4.5", "qunitjs": "~1.11.0", "regenerate": "^1.2.1", "requirejs": "^2.1.22"}, "__npminstall_done": true, "_from": "jsesc@1.3.0", "_resolved": "https://registry.npmmirror.com/jsesc/-/jsesc-1.3.0.tgz"}