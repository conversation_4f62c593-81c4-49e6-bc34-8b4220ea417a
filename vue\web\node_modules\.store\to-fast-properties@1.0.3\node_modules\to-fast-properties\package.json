{"name": "to-fast-properties", "version": "1.0.3", "description": "Force V8 to use fast properties for an object", "license": "MIT", "repository": "sindresorhus/to-fast-properties", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node --allow-natives-syntax test.js"}, "files": ["index.js"], "keywords": ["object", "obj", "properties", "props", "v8", "optimize", "fast", "convert", "mode"], "devDependencies": {"ava": "0.0.4"}, "__npminstall_done": true, "_from": "to-fast-properties@1.0.3", "_resolved": "https://registry.npmmirror.com/to-fast-properties/-/to-fast-properties-1.0.3.tgz"}