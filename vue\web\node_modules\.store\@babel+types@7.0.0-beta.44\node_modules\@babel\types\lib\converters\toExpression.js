"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = toExpression;

var _generated = require("../validators/generated");

function toExpression(node) {
  if ((0, _generated.isExpressionStatement)(node)) {
    node = node.expression;
  }

  if ((0, _generated.isExpression)(node)) {
    return node;
  }

  if ((0, _generated.isClass)(node)) {
    node.type = "ClassExpression";
  } else if ((0, _generated.isFunction)(node)) {
    node.type = "FunctionExpression";
  }

  if (!(0, _generated.isExpression)(node)) {
    throw new Error("cannot turn " + node.type + " to an expression");
  }

  return node;
}