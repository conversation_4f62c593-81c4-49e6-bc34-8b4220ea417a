{"name": "cyclist", "version": "1.0.2", "repository": {"type": "git", "url": "https://github.com/mafintosh/cyclist"}, "description": "Cyclist is an efficient cyclic list implemention.", "devDependencies": {"brittle": "^3.3.0", "standard": "^17.0.0"}, "keywords": ["circular", "buffer", "ring", "cyclic", "data"], "author": "<PERSON> <<EMAIL>>", "bugs": {"url": "https://github.com/mafintosh/cyclist/issues"}, "homepage": "https://github.com/mafintosh/cyclist", "main": "index.js", "scripts": {"test": "standard && brittle test.js"}, "license": "MIT", "__npminstall_done": true, "_from": "cyclist@1.0.2", "_resolved": "https://registry.npmmirror.com/cyclist/-/cyclist-1.0.2.tgz"}