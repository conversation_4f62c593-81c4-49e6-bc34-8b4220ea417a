{"name": "longbing_back", "version": "1.0.0", "description": "A Vue.js project", "author": "akang <<EMAIL>>", "private": true, "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "start": "npm run dev", "lint": "eslint --ext .js,.vue src", "build": "node build/build.js"}, "dependencies": {"ali-oss": "^6.22.0", "axios": "^0.19.0", "babel-polyfill": "^6.26.0", "cos-js-sdk-v5": "^1.2.15", "crypto-js": "^4.0.0", "element-china-area-data": "^5.0.2", "element-ui": "^2.10.1", "file-saver": "^2.0.5", "fundebug-javascript": "^2.0.0", "fundebug-vue": "0.0.1", "html2canvas": "^1.4.1", "jszip": "^3.10.1", "moment": "^2.24.0", "qiniu-js": "^3.3.1", "vue": "^2.5.2", "vue-clipboard2": "^0.3.1", "vue-i18n": "^8.12.0", "vue-router": "^3.0.1", "vuedraggable": "^2.23.2", "vuex": "^3.1.1", "watermarkjs": "^2.1.1"}, "devDependencies": {"ansi-styles": "^3.2.1", "autoprefixer": "^7.1.2", "babel-core": "^6.22.1", "babel-eslint": "^8.2.1", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-loader": "^7.1.1", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-plugin-transform-vue-jsx": "^3.5.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "chalk": "^2.0.1", "copy-webpack-plugin": "^4.0.1", "css-loader": "^3.2.0", "echarts": "^4.3.0", "eslint": "^4.19.1", "eslint-config-standard": "^10.2.1", "eslint-friendly-formatter": "^4.0.1", "eslint-loader": "^2.0.0", "eslint-plugin-html": "^4.0.3", "eslint-plugin-import": "^2.7.0", "eslint-plugin-node": "^5.2.0", "eslint-plugin-promise": "^3.4.0", "eslint-plugin-standard": "^3.0.1", "eslint-plugin-vue": "^4.0.0", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "glob": "^11.0.3", "html-webpack-plugin": "^2.30.1", "moment": "^2.24.0", "node-notifier": "^5.1.2", "node-sass": "^4.12.0", "nprogress": "^0.2.0", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "rimraf": "^2.6.0", "sass-loader": "^7.1.0", "sass-resources-loader": "^2.0.1", "semver": "^5.3.0", "shelljs": "^0.7.6", "supports-color": "^5.5.0", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^2.2.0", "uuid": "^3.3.2", "vue-echarts": "^4.0.4", "vue-loader": "^13.3.0", "vue-style-loader": "^3.0.1", "vue-template-compiler": "^2.5.2", "vuescroll": "^4.13.1", "webpack": "^3.6.0", "webpack-bundle-analyzer": "^3.6.0", "webpack-cli": "^6.0.1", "webpack-dev-server": "^5.2.2", "webpack-merge": "^4.1.0", "xlsx": "^0.15.1"}, "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}