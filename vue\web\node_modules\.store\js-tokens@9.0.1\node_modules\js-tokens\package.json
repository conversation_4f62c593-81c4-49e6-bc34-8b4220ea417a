{"name": "js-tokens", "version": "9.0.1", "author": "<PERSON>", "license": "MIT", "description": "Tiny JavaScript tokenizer.", "repository": "lydell/js-tokens", "type": "commonjs", "exports": "./index.js", "keywords": ["JavaScript", "js", "ECMAScript", "es", "token", "tokens", "tokenize", "tokenizer", "regex", "regexp"], "__npminstall_done": true, "_from": "js-tokens@9.0.1", "_resolved": "https://registry.npmmirror.com/js-tokens/-/js-tokens-9.0.1.tgz"}