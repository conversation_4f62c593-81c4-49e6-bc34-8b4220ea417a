#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="$basedir/C:/Users/<USER>/Desktop/p/vue/web/node_modules/.store/glob@11.0.3/node_modules"
else
  export NODE_PATH="$NODE_PATH:$basedir/C:/Users/<USER>/Desktop/p/vue/web/node_modules/.store/glob@11.0.3/node_modules"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.store/glob@11.0.3/node_modules/glob/dist/esm/bin.mjs" "$@"
else
  exec node  "$basedir/../.store/glob@11.0.3/node_modules/glob/dist/esm/bin.mjs" "$@"
fi
