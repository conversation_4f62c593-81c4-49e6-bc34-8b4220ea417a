/***********************************************************************

  A JavaScript tokenizer / parser / beautifier / compressor.
  https://github.com/mishoo/UglifyJS2

  -------------------------------- (C) ---------------------------------

                           Author: <PERSON><PERSON>
                         <<EMAIL>>
                       http://mihai.bazon.net/blog

  Distributed under the BSD license:

    Copyright 2012 (c) <PERSON><PERSON> <<EMAIL>>

    Redistribution and use in source and binary forms, with or without
    modification, are permitted provided that the following conditions
    are met:

        * Redistributions of source code must retain the above
          copyright notice, this list of conditions and the following
          disclaimer.

        * Redistributions in binary form must reproduce the above
          copyright notice, this list of conditions and the following
          disclaimer in the documentation and/or other materials
          provided with the distribution.

    THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDER “AS IS” AND ANY
    EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
    IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
    PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER BE
    LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY,
    OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
    PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
    PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
    THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR
    TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
    THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF
    SUCH DAMAGE.

 ***********************************************************************/

"use strict";

// a small wrapper around fitzgen's source-map library
function SourceMap(options) {
    options = defaults(options, {
        file: null,
        root: null,
        orig: null,
        orig_line_diff: 0,
        dest_line_diff: 0,
    }, true);
    var generator = new MOZ_SourceMap.SourceMapGenerator({
        file: options.file,
        sourceRoot: options.root
    });
    var maps = options.orig && Object.create(null);
    if (maps) for (var source in options.orig) {
        var map = new MOZ_SourceMap.SourceMapConsumer(options.orig[source]);
        if (Array.isArray(options.orig[source].sources)) {
            map._sources.toArray().forEach(function(source) {
                var sourceContent = map.sourceContentFor(source, true);
                if (sourceContent) generator.setSourceContent(source, sourceContent);
            });
        }
        maps[source] = map;
    }
    return {
        add: function(source, gen_line, gen_col, orig_line, orig_col, name) {
            var map = maps && maps[source];
            if (map) {
                var info = map.originalPositionFor({
                    line: orig_line,
                    column: orig_col
                });
                if (info.source === null) return;
                source = info.source;
                orig_line = info.line;
                orig_col = info.column;
                name = info.name || name;
            }
            generator.addMapping({
                name: name,
                source: source,
                generated: {
                    line: gen_line + options.dest_line_diff,
                    column: gen_col
                },
                original: {
                    line: orig_line + options.orig_line_diff,
                    column: orig_col
                }
            });
        },
        get: function() {
            return generator;
        },
        toString: function() {
            return JSON.stringify(generator.toJSON());
        }
    };
}
