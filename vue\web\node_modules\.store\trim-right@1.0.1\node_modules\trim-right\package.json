{"name": "trim-right", "version": "1.0.1", "description": "Similar to String#trim() but removes only whitespace on the right", "license": "MIT", "repository": "sindresorhus/trim-right", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "node test.js"}, "files": ["index.js"], "keywords": ["trim", "right", "string", "str", "util", "utils", "utility", "whitespace", "space", "remove", "delete"], "devDependencies": {"ava": "0.0.4"}, "__npminstall_done": true, "_from": "trim-right@1.0.1", "_resolved": "https://registry.npmmirror.com/trim-right/-/trim-right-1.0.1.tgz"}