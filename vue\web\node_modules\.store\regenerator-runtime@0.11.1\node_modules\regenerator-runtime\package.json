{"name": "regenerator-runtime", "author": "<PERSON> <<EMAIL>>", "description": "Runtime for Regenerator-compiled generator and async functions.", "version": "0.11.1", "main": "runtime-module.js", "keywords": ["regenerator", "runtime", "generator", "async"], "repository": {"type": "git", "url": "https://github.com/facebook/regenerator/tree/master/packages/regenerator-runtime"}, "license": "MIT", "__npminstall_done": true, "_from": "regenerator-runtime@0.11.1", "_resolved": "https://registry.npmmirror.com/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz"}