{"name": "tapable", "version": "0.2.9", "author": "<PERSON> @sokra", "description": "Just a little module for plugins.", "license": "MIT", "repository": {"type": "git", "url": "http://github.com/webpack/tapable.git"}, "devDependencies": {"mocha": "^2.2.4", "should": "^5.2.0"}, "engines": {"node": ">=0.6"}, "files": ["lib"], "homepage": "https://github.com/webpack/tapable", "main": "lib/Tapable.js", "scripts": {"test": "mocha --reporter spec"}, "__npminstall_done": true, "_from": "tapable@0.2.9", "_resolved": "https://registry.npmmirror.com/tapable/-/tapable-0.2.9.tgz"}