{"name": "semver", "version": "5.3.0", "description": "The semantic version parser used by npm.", "main": "semver.js", "scripts": {"test": "tap test/*.js"}, "devDependencies": {"tap": "^2.0.0"}, "license": "ISC", "repository": "https://github.com/npm/node-semver", "bin": {"semver": "./bin/semver"}, "files": ["bin", "range.bnf", "semver.js"], "__npminstall_done": true, "_from": "semver@5.3.0", "_resolved": "https://registry.npmmirror.com/semver/-/semver-5.3.0.tgz"}