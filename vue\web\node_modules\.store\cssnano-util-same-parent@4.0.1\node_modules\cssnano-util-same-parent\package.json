{"name": "cssnano-util-same-parent", "version": "4.0.1", "main": "dist/index.js", "description": "Check that two PostCSS nodes share the same parent.", "scripts": {"prepublish": "cross-env BABEL_ENV=publish babel src --out-dir dist --ignore /__tests__/"}, "files": ["LICENSE-MIT", "dist"], "license": "MIT", "devDependencies": {"babel-cli": "^6.0.0", "cross-env": "^5.0.0", "postcss": "^7.0.0"}, "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://beneb.info"}, "repository": "cssnano/cssnano", "homepage": "https://github.com/cssnano/cssnano", "bugs": {"url": "https://github.com/cssnano/cssnano/issues"}, "engines": {"node": ">=6.9.0"}, "__npminstall_done": true, "_from": "cssnano-util-same-parent@4.0.1", "_resolved": "https://registry.npmmirror.com/cssnano-util-same-parent/-/cssnano-util-same-parent-4.0.1.tgz"}