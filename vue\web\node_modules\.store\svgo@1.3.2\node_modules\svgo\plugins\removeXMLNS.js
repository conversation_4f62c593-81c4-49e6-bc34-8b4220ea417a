'use strict';

exports.type = 'perItem';

exports.active = false;

exports.description = 'removes xmlns attribute (for inline svg, disabled by default)';

/**
 * Remove the xmlns attribute when present.
 *
 * @example
 * <svg viewBox="0 0 100 50" xmlns="http://www.w3.org/2000/svg">
 *   ↓
 * <svg viewBox="0 0 100 50">
 *
 * @param {Object} item current iteration item
 * @return {Boolean} if true, xmlns will be filtered out
 *
 * <AUTHOR>
 */
exports.fn = function(item) {

    if (item.isElem('svg') && item.hasAttr('xmlns')) {
        item.removeAttr('xmlns');
    }

};