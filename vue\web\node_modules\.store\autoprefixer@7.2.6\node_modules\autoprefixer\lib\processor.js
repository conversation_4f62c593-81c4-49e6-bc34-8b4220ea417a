'use strict';

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

var Value = require('./value');

var OLD_LINEAR = /(^|[^-])linear-gradient\(\s*(top|left|right|bottom)/i;
var OLD_RADIAL = /(^|[^-])radial-gradient\(\s*\d+(\w*|%)\s+\d+(\w*|%)\s*,/i;

var SIZES = ['width', 'height', 'min-width', 'max-width', 'min-height', 'max-height', 'inline-size', 'min-inline-size', 'max-inline-size', 'block-size', 'min-block-size', 'max-block-size'];

var Processor = function () {
    function Processor(prefixes) {
        _classCallCheck(this, Processor);

        this.prefixes = prefixes;
    }

    /**
     * Add necessary prefixes
     */


    Processor.prototype.add = function add(css, result) {
        var _this = this;

        // At-rules
        var resolution = this.prefixes.add['@resolution'];
        var keyframes = this.prefixes.add['@keyframes'];
        var viewport = this.prefixes.add['@viewport'];
        var supports = this.prefixes.add['@supports'];

        css.walkAtRules(function (rule) {
            if (rule.name === 'keyframes') {
                if (!_this.disabled(rule, result)) {
                    return keyframes && keyframes.process(rule);
                }
            } else if (rule.name === 'viewport') {
                if (!_this.disabled(rule, result)) {
                    return viewport && viewport.process(rule);
                }
            } else if (rule.name === 'supports') {
                if (_this.prefixes.options.supports !== false && !_this.disabled(rule, result)) {
                    return supports.process(rule);
                }
            } else if (rule.name === 'media' && rule.params.indexOf('-resolution') !== -1) {
                if (!_this.disabled(rule, result)) {
                    return resolution && resolution.process(rule);
                }
            }

            return undefined;
        });

        // Selectors
        css.walkRules(function (rule) {
            if (_this.disabled(rule, result)) return undefined;

            return _this.prefixes.add.selectors.map(function (selector) {
                return selector.process(rule, result);
            });
        });

        css.walkDecls(function (decl) {
            if (_this.disabledDecl(decl, result)) return undefined;

            if (decl.prop === 'display' && decl.value === 'box') {
                result.warn('You should write display: flex by final spec ' + 'instead of display: box', { node: decl });
                return undefined;
            }
            if (decl.value.indexOf('linear-gradient') !== -1) {
                if (OLD_LINEAR.test(decl.value)) {
                    result.warn('Gradient has outdated direction syntax. ' + 'New syntax is like `to left` instead of `right`.', { node: decl });
                }
            }
            if (decl.value.indexOf('radial-gradient') !== -1) {
                if (OLD_RADIAL.test(decl.value)) {
                    result.warn('Gradient has outdated direction syntax. ' + 'New syntax is like `closest-side at 0 0` ' + 'instead of `0 0, closest-side`.', { node: decl });
                } else if (/[^-]cover/.test(decl.value)) {
                    result.warn('Gradient has outdated direction syntax. ' + 'Replace `cover` to `farthest-corner`.', { node: decl });
                } else if (/[^-]contain/.test(decl.value)) {
                    result.warn('Gradient has outdated direction syntax. ' + 'Replace `contain` to `closest-side`.', { node: decl });
                }
            }
            if (decl.prop === 'text-emphasis-position') {
                if (decl.value === 'under' || decl.value === 'over') {
                    result.warn('You should use 2 values for text-emphasis-position ' + 'For example, `under left` instead of just `under`.', { node: decl });
                }
            }

            if (SIZES.indexOf(decl.prop) !== -1) {
                if (decl.value.indexOf('fill-available') !== -1) {
                    result.warn('Replace fill-available to stretch, ' + 'because spec had been changed', { node: decl });
                } else if (decl.value.indexOf('fill') !== -1) {
                    result.warn('Replace fill to stretch, ' + 'because spec had been changed', { node: decl });
                }
            }

            var prefixer = void 0;

            if (decl.prop === 'transition' || decl.prop === 'transition-property') {
                // Transition
                return _this.prefixes.transition.add(decl, result);
            } else if (decl.prop === 'align-self') {
                // align-self flexbox or grid
                var display = _this.displayType(decl);
                if (display !== 'grid' && _this.prefixes.options.flexbox !== false) {
                    prefixer = _this.prefixes.add['align-self'];
                    if (prefixer && prefixer.prefixes) {
                        prefixer.process(decl);
                    }
                }
                if (display !== 'flex' && _this.prefixes.options.grid !== false) {
                    prefixer = _this.prefixes.add['grid-row-align'];
                    if (prefixer && prefixer.prefixes) {
                        return prefixer.process(decl, result);
                    }
                }
            } else if (decl.prop === 'justify-self') {
                // justify-self flexbox or grid
                var _display = _this.displayType(decl);
                if (_display !== 'flex' && _this.prefixes.options.grid !== false) {
                    prefixer = _this.prefixes.add['grid-column-align'];
                    if (prefixer && prefixer.prefixes) {
                        return prefixer.process(decl, result);
                    }
                }
            } else {
                // Properties
                prefixer = _this.prefixes.add[decl.prop];
                if (prefixer && prefixer.prefixes) {
                    return prefixer.process(decl, result);
                }
            }

            return undefined;
        });

        // Values
        return css.walkDecls(function (decl) {
            if (_this.disabledValue(decl, result)) return;

            var unprefixed = _this.prefixes.unprefixed(decl.prop);
            for (var _iterator = _this.prefixes.values('add', unprefixed), _isArray = Array.isArray(_iterator), _i = 0, _iterator = _isArray ? _iterator : _iterator[Symbol.iterator]();;) {
                var _ref;

                if (_isArray) {
                    if (_i >= _iterator.length) break;
                    _ref = _iterator[_i++];
                } else {
                    _i = _iterator.next();
                    if (_i.done) break;
                    _ref = _i.value;
                }

                var value = _ref;

                value.process(decl, result);
            }
            Value.save(_this.prefixes, decl);
        });
    };

    /**
     * Remove unnecessary pefixes
     */


    Processor.prototype.remove = function remove(css, result) {
        var _this2 = this;

        // At-rules
        var resolution = this.prefixes.remove['@resolution'];

        css.walkAtRules(function (rule, i) {
            if (_this2.prefixes.remove['@' + rule.name]) {
                if (!_this2.disabled(rule, result)) {
                    rule.parent.removeChild(i);
                }
            } else if (rule.name === 'media' && rule.params.indexOf('-resolution') !== -1 && resolution) {
                resolution.clean(rule);
            }
        });

        // Selectors

        var _loop = function _loop(checker) {
            css.walkRules(function (rule, i) {
                if (checker.check(rule)) {
                    if (!_this2.disabled(rule, result)) {
                        rule.parent.removeChild(i);
                    }
                }
            });
        };

        for (var _iterator2 = this.prefixes.remove.selectors, _isArray2 = Array.isArray(_iterator2), _i2 = 0, _iterator2 = _isArray2 ? _iterator2 : _iterator2[Symbol.iterator]();;) {
            var _ref2;

            if (_isArray2) {
                if (_i2 >= _iterator2.length) break;
                _ref2 = _iterator2[_i2++];
            } else {
                _i2 = _iterator2.next();
                if (_i2.done) break;
                _ref2 = _i2.value;
            }

            var checker = _ref2;

            _loop(checker);
        }

        return css.walkDecls(function (decl, i) {
            if (_this2.disabled(decl, result)) return;

            var rule = decl.parent;
            var unprefixed = _this2.prefixes.unprefixed(decl.prop);

            // Transition
            if (decl.prop === 'transition' || decl.prop === 'transition-property') {
                _this2.prefixes.transition.remove(decl);
            }

            // Properties
            if (_this2.prefixes.remove[decl.prop] && _this2.prefixes.remove[decl.prop].remove) {
                var notHack = _this2.prefixes.group(decl).down(function (other) {
                    return _this2.prefixes.normalize(other.prop) === unprefixed;
                });

                if (unprefixed === 'flex-flow') {
                    notHack = true;
                }

                if (notHack && !_this2.withHackValue(decl)) {
                    if (decl.raw('before').indexOf('\n') > -1) {
                        _this2.reduceSpaces(decl);
                    }
                    rule.removeChild(i);
                    return;
                }
            }

            // Values
            for (var _iterator3 = _this2.prefixes.values('remove', unprefixed), _isArray3 = Array.isArray(_iterator3), _i3 = 0, _iterator3 = _isArray3 ? _iterator3 : _iterator3[Symbol.iterator]();;) {
                var _ref3;

                if (_isArray3) {
                    if (_i3 >= _iterator3.length) break;
                    _ref3 = _iterator3[_i3++];
                } else {
                    _i3 = _iterator3.next();
                    if (_i3.done) break;
                    _ref3 = _i3.value;
                }

                var checker = _ref3;

                if (!checker.check(decl.value)) {
                    continue;
                }

                unprefixed = checker.unprefixed;
                var _notHack = _this2.prefixes.group(decl).down(function (other) {
                    return other.value.indexOf(unprefixed) !== -1;
                });

                if (_notHack) {
                    rule.removeChild(i);
                    return;
                }
            }
        });
    };

    /**
     * Some rare old values, which is not in standard
     */


    Processor.prototype.withHackValue = function withHackValue(decl) {
        return decl.prop === '-webkit-background-clip' && decl.value === 'text';
    };

    /**
     * Check for grid/flexbox options.
     */


    Processor.prototype.disabledValue = function disabledValue(node, result) {
        if (this.prefixes.options.grid === false && node.type === 'decl') {
            if (node.prop === 'display' && node.value.indexOf('grid') !== -1) {
                return true;
            }
        }
        if (this.prefixes.options.flexbox === false && node.type === 'decl') {
            if (node.prop === 'display' && node.value.indexOf('flex') !== -1) {
                return true;
            }
        }

        return this.disabled(node, result);
    };

    /**
     * Check for grid/flexbox options.
     */


    Processor.prototype.disabledDecl = function disabledDecl(node, result) {
        if (this.prefixes.options.grid === false && node.type === 'decl') {
            if (node.prop.indexOf('grid') !== -1 || node.prop === 'justify-items') {
                return true;
            }
        }
        if (this.prefixes.options.flexbox === false && node.type === 'decl') {
            var other = ['order', 'justify-content', 'align-items', 'align-content'];
            if (node.prop.indexOf('flex') !== -1 || other.indexOf(node.prop) !== -1) {
                return true;
            }
        }

        return this.disabled(node, result);
    };

    /**
     * Check for control comment and global options
     */


    Processor.prototype.disabled = function disabled(node, result) {
        if (!node) return false;
        if (node._autoprefixerDisabled !== undefined) {
            return node._autoprefixerDisabled;
        }

        if (node.nodes) {
            var status = undefined;
            node.each(function (i) {
                if (i.type !== 'comment') return;
                if (/(!\s*)?autoprefixer:\s*(off|on)/i.test(i.text)) {
                    if (typeof status !== 'undefined') {
                        result.warn('Second Autoprefixer control comment ' + 'was ignored. Autoprefixer applies control ' + 'comment to whole block, not to next rules.', { node: i });
                    } else {
                        status = /on/i.test(i.text);
                    }
                }
            });

            var value = false;
            if (status !== undefined) {
                value = !status;
            } else if (node.parent) {
                value = this.disabled(node.parent, result);
            }

            node._autoprefixerDisabled = value;
            return node._autoprefixerDisabled;
        } else {
            node._autoprefixerDisabled = this.disabled(node.parent, result);
            return node._autoprefixerDisabled;
        }
    };

    /**
     * Normalize spaces in cascade declaration group
     */


    Processor.prototype.reduceSpaces = function reduceSpaces(decl) {
        var stop = false;
        this.prefixes.group(decl).up(function () {
            stop = true;
            return true;
        });
        if (stop) {
            return;
        }

        var parts = decl.raw('before').split('\n');
        var prevMin = parts[parts.length - 1].length;
        var diff = false;

        this.prefixes.group(decl).down(function (other) {
            parts = other.raw('before').split('\n');
            var last = parts.length - 1;

            if (parts[last].length > prevMin) {
                if (diff === false) {
                    diff = parts[last].length - prevMin;
                }

                parts[last] = parts[last].slice(0, -diff);
                other.raws.before = parts.join('\n');
            }
        });
    };

    /**
     * Is it flebox or grid rule
     */


    Processor.prototype.displayType = function displayType(decl) {
        for (var _iterator4 = decl.parent.nodes, _isArray4 = Array.isArray(_iterator4), _i4 = 0, _iterator4 = _isArray4 ? _iterator4 : _iterator4[Symbol.iterator]();;) {
            var _ref4;

            if (_isArray4) {
                if (_i4 >= _iterator4.length) break;
                _ref4 = _iterator4[_i4++];
            } else {
                _i4 = _iterator4.next();
                if (_i4.done) break;
                _ref4 = _i4.value;
            }

            var i = _ref4;

            if (i.prop !== 'display') {
                continue;
            }

            if (i.value.indexOf('flex') !== -1) {
                return 'flex';
            }

            if (i.value.indexOf('grid') !== -1) {
                return 'grid';
            }
        }

        return false;
    };

    return Processor;
}();

module.exports = Processor;