{"name": "espree", "description": "An Esprima-compatible JavaScript parser built on Acorn", "author": "<PERSON> <<EMAIL>>", "homepage": "https://github.com/eslint/espree", "main": "espree.js", "version": "3.5.4", "files": ["lib", "espree.js"], "engines": {"node": ">=0.10.0"}, "repository": "eslint/espree", "bugs": {"url": "http://github.com/eslint/espree.git"}, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^5.5.0", "acorn-jsx": "^3.0.0"}, "devDependencies": {"browserify": "^7.0.0", "chai": "^1.10.0", "eslint": "^2.13.1", "eslint-config-eslint": "^3.0.0", "eslint-release": "^0.10.0", "esprima": "latest", "esprima-fb": "^8001.2001.0-dev-harmony-fb", "istanbul": "~0.2.6", "json-diff": "~0.3.1", "leche": "^1.0.1", "mocha": "^2.0.1", "regenerate": "~0.5.4", "shelljs": "^0.3.0", "shelljs-nodecli": "^0.1.1", "unicode-6.3.0": "~0.1.0"}, "keywords": ["ast", "ecmascript", "javascript", "parser", "syntax", "acorn"], "scripts": {"generate-regex": "node tools/generate-identifier-regex.js", "test": "npm run-script lint && node Makefile.js test", "lint": "node Makefile.js lint", "release": "eslint-release", "ci-release": "eslint-ci-release", "gh-release": "eslint-gh-release", "alpharelease": "eslint-prelease alpha", "betarelease": "eslint-prelease beta", "browserify": "node Makefile.js browserify"}, "__npminstall_done": true, "_from": "espree@3.5.4", "_resolved": "https://registry.npmmirror.com/espree/-/espree-3.5.4.tgz"}