{"name": "has-flag", "version": "2.0.0", "description": "Check if argv has a specific flag", "license": "MIT", "repository": "sindresorhus/has-flag", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "maintainers": ["Sindre Sorhus <<EMAIL>> (sindresorhus.com)", "<PERSON> <<EMAIL>> (jbnicolai.com)", "<PERSON><PERSON> <<EMAIL>> (github.com/qix-)"], "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["has", "check", "detect", "contains", "find", "flag", "cli", "command-line", "argv", "process", "arg", "args", "argument", "arguments", "getopt", "minimist", "optimist"], "devDependencies": {"ava": "*", "xo": "*"}, "__npminstall_done": true, "_from": "has-flag@2.0.0", "_resolved": "https://registry.npmmirror.com/has-flag/-/has-flag-2.0.0.tgz"}