{"name": "postcss-calc", "version": "7.0.5", "description": "PostCSS plugin to reduce calc()", "keywords": ["css", "postcss", "postcss-plugin", "calculation", "calc"], "main": "dist/index.js", "files": ["dist", "LICENSE"], "scripts": {"prepublish": "npm run build", "build": "del-cli dist && cross-env BABEL_ENV=publish babel src --out-dir dist --ignore src/__tests__/**/*.js && jison src/parser.jison -o dist/parser.js", "pretest": "npm run build && eslint src", "test": "ava"}, "author": "<PERSON>", "license": "MIT", "repository": "https://github.com/postcss/postcss-calc.git", "eslintConfig": {"parser": "babel-es<PERSON>", "extends": "eslint-config-i-am-meticulous", "rules": {"curly": "error"}}, "devDependencies": {"@babel/cli": "^7.1.2", "@babel/core": "^7.1.2", "@babel/polyfill": "^7.0.0", "@babel/preset-env": "^7.1.0", "@babel/register": "^7.0.0", "ava": "^1.4.1", "babel-eslint": "^10.0.1", "babel-plugin-add-module-exports": "^1.0.0", "cross-env": "^5.2.0", "del-cli": "^1.1.0", "eslint": "^5.7.0", "eslint-config-i-am-meticulous": "^11.0.0", "eslint-plugin-babel": "^5.2.1", "eslint-plugin-import": "^2.14.0", "jison-gho": "^0.6.1-215"}, "dependencies": {"postcss": "^7.0.27", "postcss-selector-parser": "^6.0.2", "postcss-value-parser": "^4.0.2"}, "ava": {"require": ["@babel/register", "@babel/polyfill"]}, "__npminstall_done": true, "_from": "postcss-calc@7.0.5", "_resolved": "https://registry.npmmirror.com/postcss-calc/-/postcss-calc-7.0.5.tgz"}