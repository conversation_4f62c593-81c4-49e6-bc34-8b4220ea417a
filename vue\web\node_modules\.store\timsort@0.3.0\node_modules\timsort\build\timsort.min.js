!function(a,b){if("function"==typeof define&&define.amd)define("timsort",["exports"],b);else if("undefined"!=typeof exports)b(exports);else{var c={exports:{}};b(c.exports),a.timsort=c.exports}}(this,function(a){"use strict";function b(a,b){if(!(a instanceof b))throw new TypeError("Cannot call a class as a function")}function c(a){return a<1e5?a<100?a<10?0:1:a<1e4?a<1e3?2:3:4:a<1e7?a<1e6?5:6:a<1e9?a<1e8?7:8:9}function d(a,b){if(a===b)return 0;if(~~a===a&&~~b===b){if(0===a||0===b)return a<b?-1:1;if(a<0||b<0){if(b>=0)return-1;if(a>=0)return 1;a=-a,b=-b}var d=c(a),e=c(b),f=0;return d<e?(a*=o[e-d-1],b/=10,f=-1):d>e&&(b*=o[d-e-1],a/=10,f=1),a===b?f:a<b?-1:1}var g=String(a),h=String(b);return g===h?0:g<h?-1:1}function e(a){for(var b=0;a>=l;)b|=1&a,a>>=1;return a+b}function f(a,b,c,d){var e=b+1;if(e===c)return 1;if(d(a[e++],a[b])<0){for(;e<c&&d(a[e],a[e-1])<0;)e++;g(a,b,e)}else for(;e<c&&d(a[e],a[e-1])>=0;)e++;return e-b}function g(a,b,c){for(c--;b<c;){var d=a[b];a[b++]=a[c],a[c--]=d}}function h(a,b,c,d,e){for(d===b&&d++;d<c;d++){for(var f=a[d],g=b,h=d;g<h;){var i=g+h>>>1;e(f,a[i])<0?h=i:g=i+1}var j=d-g;switch(j){case 3:a[g+3]=a[g+2];case 2:a[g+2]=a[g+1];case 1:a[g+1]=a[g];break;default:for(;j>0;)a[g+j]=a[g+j-1],j--}a[g]=f}}function i(a,b,c,d,e,f){var g=0,h=0,i=1;if(f(a,b[c+e])>0){for(h=d-e;i<h&&f(a,b[c+e+i])>0;)g=i,i=(i<<1)+1,i<=0&&(i=h);i>h&&(i=h),g+=e,i+=e}else{for(h=e+1;i<h&&f(a,b[c+e-i])<=0;)g=i,i=(i<<1)+1,i<=0&&(i=h);i>h&&(i=h);var j=g;g=e-i,i=e-j}for(g++;g<i;){var k=g+(i-g>>>1);f(a,b[c+k])>0?g=k+1:i=k}return i}function j(a,b,c,d,e,f){var g=0,h=0,i=1;if(f(a,b[c+e])<0){for(h=e+1;i<h&&f(a,b[c+e-i])<0;)g=i,i=(i<<1)+1,i<=0&&(i=h);i>h&&(i=h);var j=g;g=e-i,i=e-j}else{for(h=d-e;i<h&&f(a,b[c+e+i])>=0;)g=i,i=(i<<1)+1,i<=0&&(i=h);i>h&&(i=h),g+=e,i+=e}for(g++;g<i;){var k=g+(i-g>>>1);f(a,b[c+k])<0?i=k:g=k+1}return i}function k(a,b,c,g){if(!Array.isArray(a))throw new TypeError("Can only sort arrays");b?"function"!=typeof b&&(g=c,c=b,b=d):b=d,c||(c=0),g||(g=a.length);var i=g-c;if(!(i<2)){var j=0;if(i<l)return j=f(a,c,g,b),void h(a,c,g,c+j,b);var k=new p(a,b),m=e(i);do{if(j=f(a,c,g,b),j<m){var n=i;n>m&&(n=m),h(a,c,c+n,c+j,b),j=n}k.pushRun(c,j),k.mergeRuns(),i-=j,c+=j}while(0!==i);k.forceMergeRuns()}}a.__esModule=!0,a.sort=k;var l=32,m=7,n=256,o=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9],p=function(){function a(c,d){b(this,a),this.array=null,this.compare=null,this.minGallop=m,this.length=0,this.tmpStorageLength=n,this.stackLength=0,this.runStart=null,this.runLength=null,this.stackSize=0,this.array=c,this.compare=d,this.length=c.length,this.length<2*n&&(this.tmpStorageLength=this.length>>>1),this.tmp=new Array(this.tmpStorageLength),this.stackLength=this.length<120?5:this.length<1542?10:this.length<119151?19:40,this.runStart=new Array(this.stackLength),this.runLength=new Array(this.stackLength)}return a.prototype.pushRun=function(a,b){this.runStart[this.stackSize]=a,this.runLength[this.stackSize]=b,this.stackSize+=1},a.prototype.mergeRuns=function(){for(;this.stackSize>1;){var a=this.stackSize-2;if(a>=1&&this.runLength[a-1]<=this.runLength[a]+this.runLength[a+1]||a>=2&&this.runLength[a-2]<=this.runLength[a]+this.runLength[a-1])this.runLength[a-1]<this.runLength[a+1]&&a--;else if(this.runLength[a]>this.runLength[a+1])break;this.mergeAt(a)}},a.prototype.forceMergeRuns=function(){for(;this.stackSize>1;){var a=this.stackSize-2;a>0&&this.runLength[a-1]<this.runLength[a+1]&&a--,this.mergeAt(a)}},a.prototype.mergeAt=function(a){var b=this.compare,c=this.array,d=this.runStart[a],e=this.runLength[a],f=this.runStart[a+1],g=this.runLength[a+1];this.runLength[a]=e+g,a===this.stackSize-3&&(this.runStart[a+1]=this.runStart[a+2],this.runLength[a+1]=this.runLength[a+2]),this.stackSize--;var h=j(c[f],c,d,e,0,b);d+=h,e-=h,0!==e&&(g=i(c[d+e-1],c,f,g,g-1,b),0!==g&&(e<=g?this.mergeLow(d,e,f,g):this.mergeHigh(d,e,f,g)))},a.prototype.mergeLow=function(a,b,c,d){var e=this.compare,f=this.array,g=this.tmp,h=0;for(h=0;h<b;h++)g[h]=f[a+h];var k=0,l=c,n=a;if(f[n++]=f[l++],0!==--d){if(1===b){for(h=0;h<d;h++)f[n+h]=f[l+h];return void(f[n+d]=g[k])}for(var o=this.minGallop;;){var p=0,q=0,r=!1;do if(e(f[l],g[k])<0){if(f[n++]=f[l++],q++,p=0,0===--d){r=!0;break}}else if(f[n++]=g[k++],p++,q=0,1===--b){r=!0;break}while((p|q)<o);if(r)break;do{if(p=j(f[l],g,k,b,0,e),0!==p){for(h=0;h<p;h++)f[n+h]=g[k+h];if(n+=p,k+=p,b-=p,b<=1){r=!0;break}}if(f[n++]=f[l++],0===--d){r=!0;break}if(q=i(g[k],f,l,d,0,e),0!==q){for(h=0;h<q;h++)f[n+h]=f[l+h];if(n+=q,l+=q,d-=q,0===d){r=!0;break}}if(f[n++]=g[k++],1===--b){r=!0;break}o--}while(p>=m||q>=m);if(r)break;o<0&&(o=0),o+=2}if(this.minGallop=o,o<1&&(this.minGallop=1),1===b){for(h=0;h<d;h++)f[n+h]=f[l+h];f[n+d]=g[k]}else{if(0===b)throw new Error("mergeLow preconditions were not respected");for(h=0;h<b;h++)f[n+h]=g[k+h]}}else for(h=0;h<b;h++)f[n+h]=g[k+h]},a.prototype.mergeHigh=function(a,b,c,d){var e=this.compare,f=this.array,g=this.tmp,h=0;for(h=0;h<d;h++)g[h]=f[c+h];var k=a+b-1,l=d-1,n=c+d-1,o=0,p=0;if(f[n--]=f[k--],0!==--b){if(1===d){for(n-=b,k-=b,p=n+1,o=k+1,h=b-1;h>=0;h--)f[p+h]=f[o+h];return void(f[n]=g[l])}for(var q=this.minGallop;;){var r=0,s=0,t=!1;do if(e(g[l],f[k])<0){if(f[n--]=f[k--],r++,s=0,0===--b){t=!0;break}}else if(f[n--]=g[l--],s++,r=0,1===--d){t=!0;break}while((r|s)<q);if(t)break;do{if(r=b-j(g[l],f,a,b,b-1,e),0!==r){for(n-=r,k-=r,b-=r,p=n+1,o=k+1,h=r-1;h>=0;h--)f[p+h]=f[o+h];if(0===b){t=!0;break}}if(f[n--]=g[l--],1===--d){t=!0;break}if(s=d-i(f[k],g,0,d,d-1,e),0!==s){for(n-=s,l-=s,d-=s,p=n+1,o=l+1,h=0;h<s;h++)f[p+h]=g[o+h];if(d<=1){t=!0;break}}if(f[n--]=f[k--],0===--b){t=!0;break}q--}while(r>=m||s>=m);if(t)break;q<0&&(q=0),q+=2}if(this.minGallop=q,q<1&&(this.minGallop=1),1===d){for(n-=b,k-=b,p=n+1,o=k+1,h=b-1;h>=0;h--)f[p+h]=f[o+h];f[n]=g[l]}else{if(0===d)throw new Error("mergeHigh preconditions were not respected");for(o=n-(d-1),h=0;h<d;h++)f[o+h]=g[h]}}else for(o=n-(d-1),h=0;h<d;h++)f[o+h]=g[h]},a}()});