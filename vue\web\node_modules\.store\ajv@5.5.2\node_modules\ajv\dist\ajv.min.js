/* ajv 5.5.2: Another JSON Schema Validator */
!function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).Ajv=e()}}(function(){return function e(r,t,a){function s(i,n){if(!t[i]){if(!r[i]){var l="function"==typeof require&&require;if(!n&&l)return l(i,!0);if(o)return o(i,!0);var h=new Error("Cannot find module '"+i+"'");throw h.code="MODULE_NOT_FOUND",h}var u=t[i]={exports:{}};r[i][0].call(u.exports,function(e){var t=r[i][1][e];return s(t||e)},u,u.exports,e,r,t,a)}return t[i].exports}for(var o="function"==typeof require&&require,i=0;i<a.length;i++)s(a[i]);return s}({1:[function(e,r,t){"use strict";var a=["multipleOf","maximum","exclusiveMaximum","minimum","exclusiveMinimum","maxLength","minLength","pattern","additionalItems","maxItems","minItems","uniqueItems","maxProperties","minProperties","required","additionalProperties","enum","format","const"];r.exports=function(e,r){for(var t=0;t<r.length;t++){e=JSON.parse(JSON.stringify(e));var s,o=r[t].split("/"),i=e;for(s=1;s<o.length;s++)i=i[o[s]];for(s=0;s<a.length;s++){var n=a[s],l=i[n];l&&(i[n]={anyOf:[l,{$ref:"https://raw.githubusercontent.com/epoberezkin/ajv/master/lib/refs/$data.json#"}]})}}return e}},{}],2:[function(e,r,t){"use strict";var a=r.exports=function(){this._cache={}};a.prototype.put=function(e,r){this._cache[e]=r},a.prototype.get=function(e){return this._cache[e]},a.prototype.del=function(e){delete this._cache[e]},a.prototype.clear=function(){this._cache={}}},{}],3:[function(e,r,t){"use strict";r.exports={$ref:e("../dotjs/ref"),allOf:e("../dotjs/allOf"),anyOf:e("../dotjs/anyOf"),const:e("../dotjs/const"),contains:e("../dotjs/contains"),dependencies:e("../dotjs/dependencies"),enum:e("../dotjs/enum"),format:e("../dotjs/format"),items:e("../dotjs/items"),maximum:e("../dotjs/_limit"),minimum:e("../dotjs/_limit"),maxItems:e("../dotjs/_limitItems"),minItems:e("../dotjs/_limitItems"),maxLength:e("../dotjs/_limitLength"),minLength:e("../dotjs/_limitLength"),maxProperties:e("../dotjs/_limitProperties"),minProperties:e("../dotjs/_limitProperties"),multipleOf:e("../dotjs/multipleOf"),not:e("../dotjs/not"),oneOf:e("../dotjs/oneOf"),pattern:e("../dotjs/pattern"),properties:e("../dotjs/properties"),propertyNames:e("../dotjs/propertyNames"),required:e("../dotjs/required"),uniqueItems:e("../dotjs/uniqueItems"),validate:e("../dotjs/validate")}},{"../dotjs/_limit":13,"../dotjs/_limitItems":14,"../dotjs/_limitLength":15,"../dotjs/_limitProperties":16,"../dotjs/allOf":17,"../dotjs/anyOf":18,"../dotjs/const":19,"../dotjs/contains":20,"../dotjs/dependencies":22,"../dotjs/enum":23,"../dotjs/format":24,"../dotjs/items":25,"../dotjs/multipleOf":26,"../dotjs/not":27,"../dotjs/oneOf":28,"../dotjs/pattern":29,"../dotjs/properties":30,"../dotjs/propertyNames":31,"../dotjs/ref":32,"../dotjs/required":33,"../dotjs/uniqueItems":34,"../dotjs/validate":35}],4:[function(e,r,t){"use strict";function a(e,r,t){function o(e){var r=e.$schema;return r&&!n.getSchema(r)?a.call(n,{$ref:r},!0):Promise.resolve()}function i(e){try{return n._compile(e)}catch(t){if(t instanceof s)return function(t){function a(){delete n._loadingSchemas[l]}function s(e){return n._refs[e]||n._schemas[e]}var l=t.missingSchema;if(s(l))throw new Error("Schema "+l+" is loaded but "+t.missingRef+" cannot be resolved");var h=n._loadingSchemas[l];return h||(h=n._loadingSchemas[l]=n._opts.loadSchema(l)).then(a,a),h.then(function(e){if(!s(l))return o(e).then(function(){s(l)||n.addSchema(e,l,void 0,r)})}).then(function(){return i(e)})}(t);throw t}}var n=this;if("function"!=typeof this._opts.loadSchema)throw new Error("options.loadSchema should be a function");"function"==typeof r&&(t=r,r=void 0);var l=o(e).then(function(){var t=n._addSchema(e,void 0,r);return t.validate||i(t)});return t&&l.then(function(e){t(null,e)},t),l}var s=e("./error_classes").MissingRef;r.exports=a},{"./error_classes":5}],5:[function(e,r,t){"use strict";function a(e,r,t){this.message=t||a.message(e,r),this.missingRef=o.url(e,r),this.missingSchema=o.normalizeId(o.fullPath(this.missingRef))}function s(e){return e.prototype=Object.create(Error.prototype),e.prototype.constructor=e,e}var o=e("./resolve");r.exports={Validation:s(function(e){this.message="validation failed",this.errors=e,this.ajv=this.validation=!0}),MissingRef:s(a)},a.message=function(e,r){return"can't resolve reference "+r+" from id "+e}},{"./resolve":8}],6:[function(e,r,t){"use strict";function a(e){return e="full"==e?"full":"fast",n.copy(a[e])}function s(e){var r=e.match(l);if(!r)return!1;var t=+r[1],a=+r[2];return t>=1&&t<=12&&a>=1&&a<=h[t]}function o(e,r){var t=e.match(u);if(!t)return!1;return t[1]<=23&&t[2]<=59&&t[3]<=59&&(!r||t[5])}function i(e){if(E.test(e))return!1;try{return new RegExp(e),!0}catch(e){return!1}}var n=e("./util"),l=/^\d\d\d\d-(\d\d)-(\d\d)$/,h=[0,31,29,31,30,31,30,31,31,30,31,30,31],u=/^(\d\d):(\d\d):(\d\d)(\.\d+)?(z|[+-]\d\d:\d\d)?$/i,c=/^[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[-0-9a-z]{0,61}[0-9a-z])?)*$/i,d=/^(?:[a-z][a-z0-9+\-.]*:)(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'()*+,;=:@]|%[0-9a-f]{2})*)*)(?:\?(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i,f=/^(?:(?:[^\x00-\x20"'<>%\\^`{|}]|%[0-9a-f]{2})|\{[+#./;?&=,!@|]?(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?(?:,(?:[a-z0-9_]|%[0-9a-f]{2})+(?::[1-9][0-9]{0,3}|\*)?)*\})*$/i,p=/^(?:(?:http[s\u017F]?|ftp):\/\/)(?:(?:[\0-\x08\x0E-\x1F!-\x9F\xA1-\u167F\u1681-\u1FFF\u200B-\u2027\u202A-\u202E\u2030-\u205E\u2060-\u2FFF\u3001-\uD7FF\uE000-\uFEFE\uFF00-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+(?::(?:[\0-\x08\x0E-\x1F!-\x9F\xA1-\u167F\u1681-\u1FFF\u200B-\u2027\u202A-\u202E\u2030-\u205E\u2060-\u2FFF\u3001-\uD7FF\uE000-\uFEFE\uFF00-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*)?@)?(?:(?!10(?:\.[0-9]{1,3}){3})(?!127(?:\.[0-9]{1,3}){3})(?!169\.254(?:\.[0-9]{1,3}){2})(?!192\.168(?:\.[0-9]{1,3}){2})(?!172\.(?:1[6-9]|2[0-9]|3[01])(?:\.[0-9]{1,3}){2})(?:[1-9][0-9]?|1[0-9][0-9]|2[01][0-9]|22[0-3])(?:\.(?:1?[0-9]{1,2}|2[0-4][0-9]|25[0-5])){2}(?:\.(?:[1-9][0-9]?|1[0-9][0-9]|2[0-4][0-9]|25[0-4]))|(?:(?:(?:[0-9KSa-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+-?)*(?:[0-9KSa-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+)(?:\.(?:(?:[0-9KSa-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+-?)*(?:[0-9KSa-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])+)*(?:\.(?:(?:[KSa-z\xA1-\uD7FF\uE000-\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]){2,})))(?::[0-9]{2,5})?(?:\/(?:[\0-\x08\x0E-\x1F!-\x9F\xA1-\u167F\u1681-\u1FFF\u200B-\u2027\u202A-\u202E\u2030-\u205E\u2060-\u2FFF\u3001-\uD7FF\uE000-\uFEFE\uFF00-\uFFFF]|[\uD800-\uDBFF][\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF])*)?$/i,m=/^(?:urn:uuid:)?[0-9a-f]{8}-(?:[0-9a-f]{4}-){3}[0-9a-f]{12}$/i,v=/^(?:\/(?:[^~/]|~0|~1)*)*$|^#(?:\/(?:[a-z0-9_\-.!$&'()*+,;:=@]|%[0-9a-f]{2}|~0|~1)*)*$/i,y=/^(?:0|[1-9][0-9]*)(?:#|(?:\/(?:[^~/]|~0|~1)*)*)$/;r.exports=a,a.fast={date:/^\d\d\d\d-[0-1]\d-[0-3]\d$/,time:/^[0-2]\d:[0-5]\d:[0-5]\d(?:\.\d+)?(?:z|[+-]\d\d:\d\d)?$/i,"date-time":/^\d\d\d\d-[0-1]\d-[0-3]\d[t\s][0-2]\d:[0-5]\d:[0-5]\d(?:\.\d+)?(?:z|[+-]\d\d:\d\d)$/i,uri:/^(?:[a-z][a-z0-9+-.]*)(?::|\/)\/?[^\s]*$/i,"uri-reference":/^(?:(?:[a-z][a-z0-9+-.]*:)?\/\/)?[^\s]*$/i,"uri-template":f,url:p,email:/^[a-z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?(?:\.[a-z0-9](?:[a-z0-9-]{0,61}[a-z0-9])?)*$/i,hostname:c,ipv4:/^(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)$/,ipv6:/^\s*(?:(?:(?:[0-9a-f]{1,4}:){7}(?:[0-9a-f]{1,4}|:))|(?:(?:[0-9a-f]{1,4}:){6}(?::[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){5}(?:(?:(?::[0-9a-f]{1,4}){1,2})|:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){4}(?:(?:(?::[0-9a-f]{1,4}){1,3})|(?:(?::[0-9a-f]{1,4})?:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){3}(?:(?:(?::[0-9a-f]{1,4}){1,4})|(?:(?::[0-9a-f]{1,4}){0,2}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){2}(?:(?:(?::[0-9a-f]{1,4}){1,5})|(?:(?::[0-9a-f]{1,4}){0,3}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){1}(?:(?:(?::[0-9a-f]{1,4}){1,6})|(?:(?::[0-9a-f]{1,4}){0,4}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?::(?:(?:(?::[0-9a-f]{1,4}){1,7})|(?:(?::[0-9a-f]{1,4}){0,5}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(?:%.+)?\s*$/i,regex:i,uuid:m,"json-pointer":v,"relative-json-pointer":y},a.full={date:s,time:o,"date-time":function(e){var r=e.split(g);return 2==r.length&&s(r[0])&&o(r[1],!0)},uri:function(e){return P.test(e)&&d.test(e)},"uri-reference":/^(?:[a-z][a-z0-9+\-.]*:)?(?:\/?\/(?:(?:[a-z0-9\-._~!$&'()*+,;=:]|%[0-9a-f]{2})*@)?(?:\[(?:(?:(?:(?:[0-9a-f]{1,4}:){6}|::(?:[0-9a-f]{1,4}:){5}|(?:[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){4}|(?:(?:[0-9a-f]{1,4}:){0,1}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){3}|(?:(?:[0-9a-f]{1,4}:){0,2}[0-9a-f]{1,4})?::(?:[0-9a-f]{1,4}:){2}|(?:(?:[0-9a-f]{1,4}:){0,3}[0-9a-f]{1,4})?::[0-9a-f]{1,4}:|(?:(?:[0-9a-f]{1,4}:){0,4}[0-9a-f]{1,4})?::)(?:[0-9a-f]{1,4}:[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))|(?:(?:[0-9a-f]{1,4}:){0,5}[0-9a-f]{1,4})?::[0-9a-f]{1,4}|(?:(?:[0-9a-f]{1,4}:){0,6}[0-9a-f]{1,4})?::)|[Vv][0-9a-f]+\.[a-z0-9\-._~!$&'()*+,;=:]+)\]|(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)|(?:[a-z0-9\-._~!$&'"()*+,;=]|%[0-9a-f]{2})*)(?::\d*)?(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*|\/(?:(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?|(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})+(?:\/(?:[a-z0-9\-._~!$&'"()*+,;=:@]|%[0-9a-f]{2})*)*)?(?:\?(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?(?:#(?:[a-z0-9\-._~!$&'"()*+,;=:@/?]|%[0-9a-f]{2})*)?$/i,"uri-template":f,url:p,email:/^[a-z0-9!#$%&'*+/=?^_`{|}~-]+(?:\.[a-z0-9!#$%&''*+/=?^_`{|}~-]+)*@(?:[a-z0-9](?:[a-z0-9-]*[a-z0-9])?\.)+[a-z0-9](?:[a-z0-9-]*[a-z0-9])?$/i,hostname:function(e){return e.length<=255&&c.test(e)},ipv4:/^(?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?)$/,ipv6:/^\s*(?:(?:(?:[0-9a-f]{1,4}:){7}(?:[0-9a-f]{1,4}|:))|(?:(?:[0-9a-f]{1,4}:){6}(?::[0-9a-f]{1,4}|(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){5}(?:(?:(?::[0-9a-f]{1,4}){1,2})|:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3})|:))|(?:(?:[0-9a-f]{1,4}:){4}(?:(?:(?::[0-9a-f]{1,4}){1,3})|(?:(?::[0-9a-f]{1,4})?:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){3}(?:(?:(?::[0-9a-f]{1,4}){1,4})|(?:(?::[0-9a-f]{1,4}){0,2}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){2}(?:(?:(?::[0-9a-f]{1,4}){1,5})|(?:(?::[0-9a-f]{1,4}){0,3}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?:(?:[0-9a-f]{1,4}:){1}(?:(?:(?::[0-9a-f]{1,4}){1,6})|(?:(?::[0-9a-f]{1,4}){0,4}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:))|(?::(?:(?:(?::[0-9a-f]{1,4}){1,7})|(?:(?::[0-9a-f]{1,4}){0,5}:(?:(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)(?:\.(?:25[0-5]|2[0-4]\d|1\d\d|[1-9]?\d)){3}))|:)))(?:%.+)?\s*$/i,regex:i,uuid:m,"json-pointer":v,"relative-json-pointer":y};var g=/t|\s/i,P=/\/|:/,E=/[^\\]\\Z/},{"./util":12}],7:[function(e,r,t){"use strict";function a(e,r,t,P){function E(){var e=C.validate,r=e.apply(null,arguments);return E.errors=e.errors,r}function w(e,t,s,f){var P=!t||t&&t.schema==e;if(t.schema!=r.schema)return a.call($,e,t,s,f);var E=!0===e.$async,w=p({isTop:!0,schema:e,isRoot:P,baseId:f,root:t,schemaPath:"",errSchemaPath:"#",errorPath:'""',MissingRefError:d.MissingRef,RULES:U,validate:p,util:c,resolve:u,resolveRef:b,usePattern:_,useDefault:x,useCustomRule:F,opts:R,formats:Q,logger:$.logger,self:$});w=h(O,n)+h(I,o)+h(k,i)+h(L,l)+w,R.processCode&&(w=R.processCode(w));var S;try{S=new Function("self","RULES","formats","root","refVal","defaults","customRules","co","equal","ucs2length","ValidationError",w)($,U,Q,r,O,k,L,m,y,v,g),O[0]=S}catch(e){throw $.logger.error("Error compiling schema, function code:",w),e}return S.schema=e,S.errors=null,S.refs=D,S.refVal=O,S.root=P?S:t,E&&(S.$async=!0),!0===R.sourceCode&&(S.source={code:w,patterns:I,defaults:k}),S}function b(e,s,o){s=u.url(e,s);var i,n,l=D[s];if(void 0!==l)return i=O[l],n="refVal["+l+"]",j(i,n);if(!o&&r.refs){var h=r.refs[s];if(void 0!==h)return i=r.refVal[h],n=S(s,i),j(i,n)}n=S(s);var c=u.call($,w,r,s);if(void 0===c){var d=t&&t[s];d&&(c=u.inlineRef(d,R.inlineRefs)?d:a.call($,d,r,t,e))}if(void 0!==c)return function(e,r){O[D[e]]=r}(s,c),j(c,n);!function(e){delete D[e]}(s)}function S(e,r){var t=O.length;return O[t]=r,D[e]=t,"refVal"+t}function j(e,r){return"object"==typeof e||"boolean"==typeof e?{code:r,schema:e,inline:!0}:{code:r,$async:e&&e.$async}}function _(e){var r=A[e];return void 0===r&&(r=A[e]=I.length,I[r]=e),"pattern"+r}function x(e){switch(typeof e){case"boolean":case"number":return""+e;case"string":return c.toQuotedString(e);case"object":if(null===e)return"null";var r=f(e),t=q[r];return void 0===t&&(t=q[r]=k.length,k[t]=e),"default"+t}}function F(e,r,t,a){var s=e.definition.validateSchema;if(s&&!1!==$._opts.validateSchema){if(!s(r)){var o="keyword schema is invalid: "+$.errorsText(s.errors);if("log"!=$._opts.validateSchema)throw new Error(o);$.logger.error(o)}}var i,n=e.definition.compile,l=e.definition.inline,h=e.definition.macro;if(n)i=n.call($,r,t,a);else if(h)i=h.call($,r,t,a),!1!==R.validateSchema&&$.validateSchema(i,!0);else if(l)i=l.call($,a,e.keyword,r,t);else if(!(i=e.definition.validate))return;if(void 0===i)throw new Error('custom keyword "'+e.keyword+'"failed to compile');var u=L.length;return L[u]=i,{code:"customRule"+u,validate:i}}var $=this,R=this._opts,O=[void 0],D={},I=[],A={},k=[],q={},L=[],z=function(e,r,t){var a=s.call(this,e,r,t);return a>=0?{index:a,compiling:!0}:(a=this._compilations.length,this._compilations[a]={schema:e,root:r,baseId:t},{index:a,compiling:!1})}.call(this,e,r=r||{schema:e,refVal:O,refs:D},P),C=this._compilations[z.index];if(z.compiling)return C.callValidate=E;var Q=this._formats,U=this.RULES;try{var V=w(e,r,t,P);C.validate=V;var N=C.callValidate;return N&&(N.schema=V.schema,N.errors=null,N.refs=V.refs,N.refVal=V.refVal,N.root=V.root,N.$async=V.$async,R.sourceCode&&(N.source=V.source)),V}finally{(function(e,r,t){var a=s.call(this,e,r,t);a>=0&&this._compilations.splice(a,1)}).call(this,e,r,P)}}function s(e,r,t){for(var a=0;a<this._compilations.length;a++){var s=this._compilations[a];if(s.schema==e&&s.root==r&&s.baseId==t)return a}return-1}function o(e,r){return"var pattern"+e+" = new RegExp("+c.toQuotedString(r[e])+");"}function i(e){return"var default"+e+" = defaults["+e+"];"}function n(e,r){return void 0===r[e]?"":"var refVal"+e+" = refVal["+e+"];"}function l(e){return"var customRule"+e+" = customRules["+e+"];"}function h(e,r){if(!e.length)return"";for(var t="",a=0;a<e.length;a++)t+=r(a,e);return t}var u=e("./resolve"),c=e("./util"),d=e("./error_classes"),f=e("fast-json-stable-stringify"),p=e("../dotjs/validate"),m=e("co"),v=c.ucs2length,y=e("fast-deep-equal"),g=d.Validation;r.exports=a},{"../dotjs/validate":35,"./error_classes":5,"./resolve":8,"./util":12,co:40,"fast-deep-equal":41,"fast-json-stable-stringify":42}],8:[function(e,r,t){"use strict";function a(e,r,t){var o=this._refs[t];if("string"==typeof o){if(!this._refs[o])return a.call(this,e,r,o);o=this._refs[o]}if((o=o||this._schemas[t])instanceof v)return i(o.schema,this._opts.inlineRefs)?o.schema:o.validate||this._compile(o);var n,l,h,u=s.call(this,r,t);return u&&(n=u.schema,r=u.root,h=u.baseId),n instanceof v?l=n.validate||e.call(this,n.schema,r,void 0,h):void 0!==n&&(l=i(n,this._opts.inlineRefs)?n:e.call(this,n,r,void 0,h)),l}function s(e,r){var t=f.parse(r,!1,!0),a=u(t),i=h(this._getId(e.schema));if(a!==i){var n=c(a),l=this._refs[n];if("string"==typeof l)return function(e,r,t){var a=s.call(this,e,r);if(a){var i=a.schema,n=a.baseId;e=a.root;var l=this._getId(i);return l&&(n=d(n,l)),o.call(this,t,n,i,e)}}.call(this,e,l,t);if(l instanceof v)l.validate||this._compile(l),e=l;else{if(!((l=this._schemas[n])instanceof v))return;if(l.validate||this._compile(l),n==c(r))return{schema:l,root:e,baseId:i};e=l}if(!e.schema)return;i=h(this._getId(e.schema))}return o.call(this,t,i,e.schema,e)}function o(e,r,t,a){if(e.hash=e.hash||"","#/"==e.hash.slice(0,2)){for(var o=e.hash.split("/"),i=1;i<o.length;i++){var n=o[i];if(n){if(n=m.unescapeFragment(n),void 0===(t=t[n]))break;var l;if(!g[n]&&((l=this._getId(t))&&(r=d(r,l)),t.$ref)){var h=d(r,t.$ref),u=s.call(this,a,h);u&&(t=u.schema,a=u.root,r=u.baseId)}}}return void 0!==t&&t!==a.schema?{schema:t,root:a,baseId:r}:void 0}}function i(e,r){return!1!==r&&(void 0===r||!0===r?n(e):r?l(e)<=r:void 0)}function n(e){var r;if(Array.isArray(e)){for(var t=0;t<e.length;t++)if("object"==typeof(r=e[t])&&!n(r))return!1}else for(var a in e){if("$ref"==a)return!1;if("object"==typeof(r=e[a])&&!n(r))return!1}return!0}function l(e){var r,t=0;if(Array.isArray(e)){for(var a=0;a<e.length;a++)if("object"==typeof(r=e[a])&&(t+=l(r)),t==1/0)return 1/0}else for(var s in e){if("$ref"==s)return 1/0;if(P[s])t++;else if("object"==typeof(r=e[s])&&(t+=l(r)+1),t==1/0)return 1/0}return t}function h(e,r){!1!==r&&(e=c(e));return u(f.parse(e,!1,!0))}function u(e){var r=e.protocol||"//"==e.href.slice(0,2)?"//":"";return(e.protocol||"")+r+(e.host||"")+(e.path||"")+"#"}function c(e){return e?e.replace(E,""):""}function d(e,r){return r=c(r),f.resolve(e,r)}var f=e("url"),p=e("fast-deep-equal"),m=e("./util"),v=e("./schema_obj"),y=e("json-schema-traverse");r.exports=a,a.normalizeId=c,a.fullPath=h,a.url=d,a.ids=function(e){var r=c(this._getId(e)),t={"":r},a={"":h(r,!1)},s={},o=this;return y(e,{allKeys:!0},function(e,r,i,n,l,h,u){if(""!==r){var d=o._getId(e),v=t[n],y=a[n]+"/"+l;if(void 0!==u&&(y+="/"+("number"==typeof u?u:m.escapeFragment(u))),"string"==typeof d){d=v=c(v?f.resolve(v,d):d);var g=o._refs[d];if("string"==typeof g&&(g=o._refs[g]),g&&g.schema){if(!p(e,g.schema))throw new Error('id "'+d+'" resolves to more than one schema')}else if(d!=c(y))if("#"==d[0]){if(s[d]&&!p(e,s[d]))throw new Error('id "'+d+'" resolves to more than one schema');s[d]=e}else o._refs[d]=y}t[r]=v,a[r]=y}}),s},a.inlineRef=i,a.schema=s;var g=m.toHash(["properties","patternProperties","enum","dependencies","definitions"]),P=m.toHash(["type","format","pattern","maxLength","minLength","maxProperties","minProperties","maxItems","minItems","maximum","minimum","uniqueItems","multipleOf","required","enum"]),E=/#\/?$/},{"./schema_obj":10,"./util":12,"fast-deep-equal":41,"json-schema-traverse":43,url:48}],9:[function(e,r,t){"use strict";var a=e("./_rules"),s=e("./util").toHash;r.exports=function(){var e=[{type:"number",rules:[{maximum:["exclusiveMaximum"]},{minimum:["exclusiveMinimum"]},"multipleOf","format"]},{type:"string",rules:["maxLength","minLength","pattern","format"]},{type:"array",rules:["maxItems","minItems","uniqueItems","contains","items"]},{type:"object",rules:["maxProperties","minProperties","required","dependencies","propertyNames",{properties:["additionalProperties","patternProperties"]}]},{rules:["$ref","const","enum","not","anyOf","oneOf","allOf"]}],r=["type"];return e.all=s(r),e.types=s(["number","integer","string","array","object","boolean","null"]),e.forEach(function(t){t.rules=t.rules.map(function(t){var s;if("object"==typeof t){var o=Object.keys(t)[0];s=t[o],t=o,s.forEach(function(t){r.push(t),e.all[t]=!0})}r.push(t);return e.all[t]={keyword:t,code:a[t],implements:s}}),t.type&&(e.types[t.type]=t)}),e.keywords=s(r.concat(["additionalItems","$schema","$id","id","title","description","default","definitions"])),e.custom={},e}},{"./_rules":3,"./util":12}],10:[function(e,r,t){"use strict";var a=e("./util");r.exports=function(e){a.copy(e,this)}},{"./util":12}],11:[function(e,r,t){"use strict";r.exports=function(e){for(var r,t=0,a=e.length,s=0;s<a;)t++,(r=e.charCodeAt(s++))>=55296&&r<=56319&&s<a&&56320==(64512&(r=e.charCodeAt(s)))&&s++;return t}},{}],12:[function(e,r,t){"use strict";function a(e,r,t){var a=t?" !== ":" === ",s=t?" || ":" && ",o=t?"!":"",i=t?"":"!";switch(e){case"null":return r+a+"null";case"array":return o+"Array.isArray("+r+")";case"object":return"("+o+r+s+"typeof "+r+a+'"object"'+s+i+"Array.isArray("+r+"))";case"integer":return"(typeof "+r+a+'"number"'+s+i+"("+r+" % 1)"+s+r+a+r+")";default:return"typeof "+r+a+'"'+e+'"'}}function s(e){for(var r={},t=0;t<e.length;t++)r[e[t]]=!0;return r}function o(e){return"number"==typeof e?"["+e+"]":d.test(e)?"."+e:"['"+i(e)+"']"}function i(e){return e.replace(f,"\\$&").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\f/g,"\\f").replace(/\t/g,"\\t")}function n(e){return"'"+i(e)+"'"}function l(e,r){return'""'==e?r:(e+" + "+r).replace(/' \+ '/g,"")}function h(e){return e.replace(/~/g,"~0").replace(/\//g,"~1")}function u(e){return e.replace(/~1/g,"/").replace(/~0/g,"~")}r.exports={copy:function(e,r){r=r||{};for(var t in e)r[t]=e[t];return r},checkDataType:a,checkDataTypes:function(e,r){switch(e.length){case 1:return a(e[0],r,!0);default:var t="",o=s(e);o.array&&o.object&&(t=o.null?"(":"(!"+r+" || ",t+="typeof "+r+' !== "object")',delete o.null,delete o.array,delete o.object),o.number&&delete o.integer;for(var i in o)t+=(t?" && ":"")+a(i,r,!0);return t}},coerceToTypes:function(e,r){if(Array.isArray(r)){for(var t=[],a=0;a<r.length;a++){var s=r[a];c[s]?t[t.length]=s:"array"===e&&"array"===s&&(t[t.length]=s)}if(t.length)return t}else{if(c[r])return[r];if("array"===e&&"array"===r)return["array"]}},toHash:s,getProperty:o,escapeQuotes:i,equal:e("fast-deep-equal"),ucs2length:e("./ucs2length"),varOccurences:function(e,r){r+="[^0-9]";var t=e.match(new RegExp(r,"g"));return t?t.length:0},varReplace:function(e,r,t){return r+="([^0-9])",t=t.replace(/\$/g,"$$$$"),e.replace(new RegExp(r,"g"),t+"$1")},cleanUpCode:function(e){return e.replace(p,"").replace(m,"").replace(v,"if (!($1))")},finalCleanUpCode:function(e,r){var t=e.match(y);return t&&2==t.length&&(e=r?e.replace(P,"").replace(b,S):e.replace(g,"").replace(E,w)),(t=e.match(j))&&3===t.length?e.replace(_,""):e},schemaHasRules:function(e,r){if("boolean"==typeof e)return!e;for(var t in e)if(r[t])return!0},schemaHasRulesExcept:function(e,r,t){if("boolean"==typeof e)return!e&&"not"!=t;for(var a in e)if(a!=t&&r[a])return!0},toQuotedString:n,getPathExpr:function(e,r,t,a){return l(e,t?"'/' + "+r+(a?"":".replace(/~/g, '~0').replace(/\\//g, '~1')"):a?"'[' + "+r+" + ']'":"'[\\'' + "+r+" + '\\']'")},getPath:function(e,r,t){return l(e,n(t?"/"+h(r):o(r)))},getData:function(e,r,t){var a,s,i,n;if(""===e)return"rootData";if("/"==e[0]){if(!x.test(e))throw new Error("Invalid JSON-pointer: "+e);s=e,i="rootData"}else{if(!(n=e.match(F)))throw new Error("Invalid JSON-pointer: "+e);if(a=+n[1],"#"==(s=n[2])){if(a>=r)throw new Error("Cannot access property/index "+a+" levels up, current level is "+r);return t[r-a]}if(a>r)throw new Error("Cannot access data "+a+" levels up, current level is "+r);if(i="data"+(r-a||""),!s)return i}for(var l=i,h=s.split("/"),c=0;c<h.length;c++){var d=h[c];d&&(l+=" && "+(i+=o(u(d))))}return l},unescapeFragment:function(e){return u(decodeURIComponent(e))},unescapeJsonPointer:u,escapeFragment:function(e){return encodeURIComponent(h(e))},escapeJsonPointer:h};var c=s(["string","number","integer","boolean","null"]),d=/^[a-z$_][a-z$_0-9]*$/i,f=/'|\\/g,p=/else\s*{\s*}/g,m=/if\s*\([^)]+\)\s*\{\s*\}(?!\s*else)/g,v=/if\s*\(([^)]+)\)\s*\{\s*\}\s*else(?!\s*if)/g,y=/[^v.]errors/g,g=/var errors = 0;|var vErrors = null;|validate.errors = vErrors;/g,P=/var errors = 0;|var vErrors = null;/g,E="return errors === 0;",w="validate.errors = null; return true;",b=/if \(errors === 0\) return data;\s*else throw new ValidationError\(vErrors\);/,S="return data;",j=/[^A-Za-z_$]rootData[^A-Za-z0-9_$]/g,_=/if \(rootData === undefined\) rootData = data;/,x=/^\/(?:[^~]|~0|~1)*$/,F=/^([0-9]+)(#|\/(?:[^~]|~0|~1)*)?$/},{"./ucs2length":11,"fast-deep-equal":41}],13:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a,s=" ",o=e.level,i=e.dataLevel,n=e.schema[r],l=e.schemaPath+e.util.getProperty(r),h=e.errSchemaPath+"/"+r,u=!e.opts.allErrors,c="data"+(i||""),d=e.opts.$data&&n&&n.$data;d?(s+=" var schema"+o+" = "+e.util.getData(n.$data,i,e.dataPathArr)+"; ",a="schema"+o):a=n;var f="maximum"==r,p=f?"exclusiveMaximum":"exclusiveMinimum",m=e.schema[p],v=f?"<":">",y=f?">":"<",g=void 0;if(e.opts.$data&&m&&m.$data){var P=e.util.getData(m.$data,i,e.dataPathArr),E="exclusive"+o,w="exclType"+o,b="exclIsNumber"+o,S="' + "+(_="op"+o)+" + '";s+=" var schemaExcl"+o+" = "+P+"; ",s+=" var "+E+"; var "+w+" = typeof "+(P="schemaExcl"+o)+"; if ("+w+" != 'boolean' && "+w+" != 'undefined' && "+w+" != 'number') { ";g=p;(x=x||[]).push(s),s="",!1!==e.createErrors?(s+=" { keyword: '"+(g||"_exclusiveLimit")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(h)+" , params: {} ",!1!==e.opts.messages&&(s+=" , message: '"+p+" should be boolean' "),e.opts.verbose&&(s+=" , schema: validate.schema"+l+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "),s+=" } "):s+=" {} ";var j=s;s=x.pop(),s+=!e.compositeRule&&u?e.async?" throw new ValidationError(["+j+"]); ":" validate.errors = ["+j+"]; return false; ":" var err = "+j+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",s+=" } else if ( ",d&&(s+=" ("+a+" !== undefined && typeof "+a+" != 'number') || "),s+=" "+w+" == 'number' ? ( ("+E+" = "+a+" === undefined || "+P+" "+v+"= "+a+") ? "+c+" "+y+"= "+P+" : "+c+" "+y+" "+a+" ) : ( ("+E+" = "+P+" === true) ? "+c+" "+y+"= "+a+" : "+c+" "+y+" "+a+" ) || "+c+" !== "+c+") { var op"+o+" = "+E+" ? '"+v+"' : '"+v+"=';"}else{S=v;if((b="number"==typeof m)&&d){var _="'"+S+"'";s+=" if ( ",d&&(s+=" ("+a+" !== undefined && typeof "+a+" != 'number') || "),s+=" ( "+a+" === undefined || "+m+" "+v+"= "+a+" ? "+c+" "+y+"= "+m+" : "+c+" "+y+" "+a+" ) || "+c+" !== "+c+") { "}else{b&&void 0===n?(E=!0,g=p,h=e.errSchemaPath+"/"+p,a=m,y+="="):(b&&(a=Math[f?"min":"max"](m,n)),m===(!b||a)?(E=!0,g=p,h=e.errSchemaPath+"/"+p,y+="="):(E=!1,S+="="));_="'"+S+"'";s+=" if ( ",d&&(s+=" ("+a+" !== undefined && typeof "+a+" != 'number') || "),s+=" "+c+" "+y+" "+a+" || "+c+" !== "+c+") { "}}g=g||r;var x;(x=x||[]).push(s),s="",!1!==e.createErrors?(s+=" { keyword: '"+(g||"_limit")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(h)+" , params: { comparison: "+_+", limit: "+a+", exclusive: "+E+" } ",!1!==e.opts.messages&&(s+=" , message: 'should be "+S+" ",s+=d?"' + "+a:a+"'"),e.opts.verbose&&(s+=" , schema:  ",s+=d?"validate.schema"+l:""+n,s+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "),s+=" } "):s+=" {} ";j=s;return s=x.pop(),s+=!e.compositeRule&&u?e.async?" throw new ValidationError(["+j+"]); ":" validate.errors = ["+j+"]; return false; ":" var err = "+j+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",s+=" } ",u&&(s+=" else { "),s}},{}],14:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a,s=" ",o=e.level,i=e.dataLevel,n=e.schema[r],l=e.schemaPath+e.util.getProperty(r),h=e.errSchemaPath+"/"+r,u=!e.opts.allErrors,c="data"+(i||""),d=e.opts.$data&&n&&n.$data;d?(s+=" var schema"+o+" = "+e.util.getData(n.$data,i,e.dataPathArr)+"; ",a="schema"+o):a=n;s+="if ( ",d&&(s+=" ("+a+" !== undefined && typeof "+a+" != 'number') || "),s+=" "+c+".length "+("maxItems"==r?">":"<")+" "+a+") { ";var f=r,p=p||[];p.push(s),s="",!1!==e.createErrors?(s+=" { keyword: '"+(f||"_limitItems")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(h)+" , params: { limit: "+a+" } ",!1!==e.opts.messages&&(s+=" , message: 'should NOT have ",s+="maxItems"==r?"more":"less",s+=" than ",s+=d?"' + "+a+" + '":""+n,s+=" items' "),e.opts.verbose&&(s+=" , schema:  ",s+=d?"validate.schema"+l:""+n,s+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "),s+=" } "):s+=" {} ";var m=s;return s=p.pop(),s+=!e.compositeRule&&u?e.async?" throw new ValidationError(["+m+"]); ":" validate.errors = ["+m+"]; return false; ":" var err = "+m+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",s+="} ",u&&(s+=" else { "),s}},{}],15:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a,s=" ",o=e.level,i=e.dataLevel,n=e.schema[r],l=e.schemaPath+e.util.getProperty(r),h=e.errSchemaPath+"/"+r,u=!e.opts.allErrors,c="data"+(i||""),d=e.opts.$data&&n&&n.$data;d?(s+=" var schema"+o+" = "+e.util.getData(n.$data,i,e.dataPathArr)+"; ",a="schema"+o):a=n;s+="if ( ",d&&(s+=" ("+a+" !== undefined && typeof "+a+" != 'number') || "),s+=!1===e.opts.unicode?" "+c+".length ":" ucs2length("+c+") ",s+=" "+("maxLength"==r?">":"<")+" "+a+") { ";var f=r,p=p||[];p.push(s),s="",!1!==e.createErrors?(s+=" { keyword: '"+(f||"_limitLength")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(h)+" , params: { limit: "+a+" } ",!1!==e.opts.messages&&(s+=" , message: 'should NOT be ",s+="maxLength"==r?"longer":"shorter",s+=" than ",s+=d?"' + "+a+" + '":""+n,s+=" characters' "),e.opts.verbose&&(s+=" , schema:  ",s+=d?"validate.schema"+l:""+n,s+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "),s+=" } "):s+=" {} ";var m=s;return s=p.pop(),s+=!e.compositeRule&&u?e.async?" throw new ValidationError(["+m+"]); ":" validate.errors = ["+m+"]; return false; ":" var err = "+m+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",s+="} ",u&&(s+=" else { "),s}},{}],16:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a,s=" ",o=e.level,i=e.dataLevel,n=e.schema[r],l=e.schemaPath+e.util.getProperty(r),h=e.errSchemaPath+"/"+r,u=!e.opts.allErrors,c="data"+(i||""),d=e.opts.$data&&n&&n.$data;d?(s+=" var schema"+o+" = "+e.util.getData(n.$data,i,e.dataPathArr)+"; ",a="schema"+o):a=n;s+="if ( ",d&&(s+=" ("+a+" !== undefined && typeof "+a+" != 'number') || "),s+=" Object.keys("+c+").length "+("maxProperties"==r?">":"<")+" "+a+") { ";var f=r,p=p||[];p.push(s),s="",!1!==e.createErrors?(s+=" { keyword: '"+(f||"_limitProperties")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(h)+" , params: { limit: "+a+" } ",!1!==e.opts.messages&&(s+=" , message: 'should NOT have ",s+="maxProperties"==r?"more":"less",s+=" than ",s+=d?"' + "+a+" + '":""+n,s+=" properties' "),e.opts.verbose&&(s+=" , schema:  ",s+=d?"validate.schema"+l:""+n,s+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "),s+=" } "):s+=" {} ";var m=s;return s=p.pop(),s+=!e.compositeRule&&u?e.async?" throw new ValidationError(["+m+"]); ":" validate.errors = ["+m+"]; return false; ":" var err = "+m+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",s+="} ",u&&(s+=" else { "),s}},{}],17:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a=" ",s=e.schema[r],o=e.schemaPath+e.util.getProperty(r),i=e.errSchemaPath+"/"+r,n=!e.opts.allErrors,l=e.util.copy(e),h="";l.level++;var u="valid"+l.level,c=l.baseId,d=!0,f=s;if(f)for(var p,m=-1,v=f.length-1;m<v;)p=f[m+=1],e.util.schemaHasRules(p,e.RULES.all)&&(d=!1,l.schema=p,l.schemaPath=o+"["+m+"]",l.errSchemaPath=i+"/"+m,a+="  "+e.validate(l)+" ",l.baseId=c,n&&(a+=" if ("+u+") { ",h+="}"));return n&&(a+=d?" if (true) { ":" "+h.slice(0,-1)+" "),a=e.util.cleanUpCode(a)}},{}],18:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a=" ",s=e.level,o=e.dataLevel,i=e.schema[r],n=e.schemaPath+e.util.getProperty(r),l=e.errSchemaPath+"/"+r,h=!e.opts.allErrors,u="data"+(o||""),c="valid"+s,d="errs__"+s,f=e.util.copy(e),p="";f.level++;var m="valid"+f.level;if(i.every(function(r){return e.util.schemaHasRules(r,e.RULES.all)})){var v=f.baseId;a+=" var "+d+" = errors; var "+c+" = false;  ";var y=e.compositeRule;e.compositeRule=f.compositeRule=!0;var g=i;if(g)for(var P,E=-1,w=g.length-1;E<w;)P=g[E+=1],f.schema=P,f.schemaPath=n+"["+E+"]",f.errSchemaPath=l+"/"+E,a+="  "+e.validate(f)+" ",f.baseId=v,a+=" "+c+" = "+c+" || "+m+"; if (!"+c+") { ",p+="}";e.compositeRule=f.compositeRule=y,a+=" "+p+" if (!"+c+") {   var err =   ",!1!==e.createErrors?(a+=" { keyword: 'anyOf' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: {} ",!1!==e.opts.messages&&(a+=" , message: 'should match some schema in anyOf' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ",a+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",!e.compositeRule&&h&&(a+=e.async?" throw new ValidationError(vErrors); ":" validate.errors = vErrors; return false; "),a+=" } else {  errors = "+d+"; if (vErrors !== null) { if ("+d+") vErrors.length = "+d+"; else vErrors = null; } ",e.opts.allErrors&&(a+=" } "),a=e.util.cleanUpCode(a)}else h&&(a+=" if (true) { ");return a}},{}],19:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a=" ",s=e.level,o=e.dataLevel,i=e.schema[r],n=e.schemaPath+e.util.getProperty(r),l=e.errSchemaPath+"/"+r,h=!e.opts.allErrors,u="data"+(o||""),c="valid"+s,d=e.opts.$data&&i&&i.$data;d&&(a+=" var schema"+s+" = "+e.util.getData(i.$data,o,e.dataPathArr)+"; "),d||(a+=" var schema"+s+" = validate.schema"+n+";"),a+="var "+c+" = equal("+u+", schema"+s+"); if (!"+c+") {   ";var f=f||[];f.push(a),a="",!1!==e.createErrors?(a+=" { keyword: 'const' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: {} ",!1!==e.opts.messages&&(a+=" , message: 'should be equal to constant' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ";var p=a;return a=f.pop(),a+=!e.compositeRule&&h?e.async?" throw new ValidationError(["+p+"]); ":" validate.errors = ["+p+"]; return false; ":" var err = "+p+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",a+=" }",h&&(a+=" else { "),a}},{}],20:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a=" ",s=e.level,o=e.dataLevel,i=e.schema[r],n=e.schemaPath+e.util.getProperty(r),l=e.errSchemaPath+"/"+r,h=!e.opts.allErrors,u="data"+(o||""),c="valid"+s,d="errs__"+s,f=e.util.copy(e);f.level++;var p="valid"+f.level,m="i"+s,v=f.dataLevel=e.dataLevel+1,y="data"+v,g=e.baseId,P=e.util.schemaHasRules(i,e.RULES.all);if(a+="var "+d+" = errors;var "+c+";",P){var E=e.compositeRule;e.compositeRule=f.compositeRule=!0,f.schema=i,f.schemaPath=n,f.errSchemaPath=l,a+=" var "+p+" = false; for (var "+m+" = 0; "+m+" < "+u+".length; "+m+"++) { ",f.errorPath=e.util.getPathExpr(e.errorPath,m,e.opts.jsonPointers,!0);var w=u+"["+m+"]";f.dataPathArr[v]=m;var b=e.validate(f);f.baseId=g,e.util.varOccurences(b,y)<2?a+=" "+e.util.varReplace(b,y,w)+" ":a+=" var "+y+" = "+w+"; "+b+" ",a+=" if ("+p+") break; }  ",e.compositeRule=f.compositeRule=E,a+="  if (!"+p+") {"}else a+=" if ("+u+".length == 0) {";var S=S||[];S.push(a),a="",!1!==e.createErrors?(a+=" { keyword: 'contains' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: {} ",!1!==e.opts.messages&&(a+=" , message: 'should contain a valid item' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ";var j=a;return a=S.pop(),a+=!e.compositeRule&&h?e.async?" throw new ValidationError(["+j+"]); ":" validate.errors = ["+j+"]; return false; ":" var err = "+j+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",a+=" } else { ",P&&(a+="  errors = "+d+"; if (vErrors !== null) { if ("+d+") vErrors.length = "+d+"; else vErrors = null; } "),e.opts.allErrors&&(a+=" } "),a=e.util.cleanUpCode(a)}},{}],21:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a,s,o=" ",i=e.level,n=e.dataLevel,l=e.schema[r],h=e.schemaPath+e.util.getProperty(r),u=e.errSchemaPath+"/"+r,c=!e.opts.allErrors,d="data"+(n||""),f="valid"+i,p="errs__"+i,m=e.opts.$data&&l&&l.$data;m?(o+=" var schema"+i+" = "+e.util.getData(l.$data,n,e.dataPathArr)+"; ",s="schema"+i):s=l;var v,y,g,P,E,w="definition"+i,b=this.definition,S="";if(m&&b.$data){var j=b.validateSchema;o+=" var "+w+" = RULES.custom['"+r+"'].definition; var "+(E="keywordValidate"+i)+" = "+w+".validate;"}else{if(!(P=e.useCustomRule(this,l,e.schema,e)))return;s="validate.schema"+h,E=P.code,v=b.compile,y=b.inline,g=b.macro}var _=E+".errors",x="i"+i,F="ruleErr"+i,$=b.async;if($&&!e.async)throw new Error("async keyword in sync schema");if(y||g||(o+=_+" = null;"),o+="var "+p+" = errors;var "+f+";",m&&b.$data&&(S+="}",o+=" if ("+s+" === undefined) { "+f+" = true; } else { ",j&&(S+="}",o+=" "+f+" = "+w+".validateSchema("+s+"); if ("+f+") { ")),y)o+=b.statements?" "+P.validate+" ":" "+f+" = "+P.validate+"; ";else if(g){var R=e.util.copy(e);S="";R.level++;var O="valid"+R.level;R.schema=P.validate,R.schemaPath="";var D=e.compositeRule;e.compositeRule=R.compositeRule=!0;var I=e.validate(R).replace(/validate\.schema/g,E);e.compositeRule=R.compositeRule=D,o+=" "+I}else{(L=L||[]).push(o),o="",o+="  "+E+".call( ",o+=e.opts.passContext?"this":"self",o+=v||!1===b.schema?" , "+d+" ":" , "+s+" , "+d+" , validate.schema"+e.schemaPath+" ",o+=" , (dataPath || '')",'""'!=e.errorPath&&(o+=" + "+e.errorPath);var A=n?"data"+(n-1||""):"parentData",k=n?e.dataPathArr[n]:"parentDataProperty",q=o+=" , "+A+" , "+k+" , rootData )  ";o=L.pop(),!1===b.errors?(o+=" "+f+" = ",$&&(o+=""+e.yieldAwait),o+=q+"; "):o+=$?" var "+(_="customErrors"+i)+" = null; try { "+f+" = "+e.yieldAwait+q+"; } catch (e) { "+f+" = false; if (e instanceof ValidationError) "+_+" = e.errors; else throw e; } ":" "+_+" = null; "+f+" = "+q+"; "}if(b.modifying&&(o+=" if ("+A+") "+d+" = "+A+"["+k+"];"),o+=""+S,b.valid)c&&(o+=" if (true) { ");else{o+=" if ( ",void 0===b.valid?(o+=" !",o+=g?""+O:""+f):o+=" "+!b.valid+" ",o+=") { ",a=this.keyword;(L=L||[]).push(o),o="";var L;(L=L||[]).push(o),o="",!1!==e.createErrors?(o+=" { keyword: '"+(a||"custom")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(u)+" , params: { keyword: '"+this.keyword+"' } ",!1!==e.opts.messages&&(o+=" , message: 'should pass \""+this.keyword+"\" keyword validation' "),e.opts.verbose&&(o+=" , schema: validate.schema"+h+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+d+" "),o+=" } "):o+=" {} ";var z=o;o=L.pop();var C=o+=!e.compositeRule&&c?e.async?" throw new ValidationError(["+z+"]); ":" validate.errors = ["+z+"]; return false; ":" var err = "+z+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ";o=L.pop(),y?b.errors?"full"!=b.errors&&(o+="  for (var "+x+"="+p+"; "+x+"<errors; "+x+"++) { var "+F+" = vErrors["+x+"]; if ("+F+".dataPath === undefined) "+F+".dataPath = (dataPath || '') + "+e.errorPath+"; if ("+F+".schemaPath === undefined) { "+F+'.schemaPath = "'+u+'"; } ',e.opts.verbose&&(o+=" "+F+".schema = "+s+"; "+F+".data = "+d+"; "),o+=" } "):!1===b.errors?o+=" "+C+" ":(o+=" if ("+p+" == errors) { "+C+" } else {  for (var "+x+"="+p+"; "+x+"<errors; "+x+"++) { var "+F+" = vErrors["+x+"]; if ("+F+".dataPath === undefined) "+F+".dataPath = (dataPath || '') + "+e.errorPath+"; if ("+F+".schemaPath === undefined) { "+F+'.schemaPath = "'+u+'"; } ',e.opts.verbose&&(o+=" "+F+".schema = "+s+"; "+F+".data = "+d+"; "),o+=" } } "):g?(o+="   var err =   ",!1!==e.createErrors?(o+=" { keyword: '"+(a||"custom")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(u)+" , params: { keyword: '"+this.keyword+"' } ",!1!==e.opts.messages&&(o+=" , message: 'should pass \""+this.keyword+"\" keyword validation' "),e.opts.verbose&&(o+=" , schema: validate.schema"+h+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+d+" "),o+=" } "):o+=" {} ",o+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",!e.compositeRule&&c&&(o+=e.async?" throw new ValidationError(vErrors); ":" validate.errors = vErrors; return false; ")):!1===b.errors?o+=" "+C+" ":(o+=" if (Array.isArray("+_+")) { if (vErrors === null) vErrors = "+_+"; else vErrors = vErrors.concat("+_+"); errors = vErrors.length;  for (var "+x+"="+p+"; "+x+"<errors; "+x+"++) { var "+F+" = vErrors["+x+"]; if ("+F+".dataPath === undefined) "+F+".dataPath = (dataPath || '') + "+e.errorPath+";  "+F+'.schemaPath = "'+u+'";  ',e.opts.verbose&&(o+=" "+F+".schema = "+s+"; "+F+".data = "+d+"; "),o+=" } } else { "+C+" } "),o+=" } ",c&&(o+=" else { ")}return o}},{}],22:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a=" ",s=e.level,o=e.dataLevel,i=e.schema[r],n=e.schemaPath+e.util.getProperty(r),l=e.errSchemaPath+"/"+r,h=!e.opts.allErrors,u="data"+(o||""),c="errs__"+s,d=e.util.copy(e),f="";d.level++;var p="valid"+d.level,m={},v={},y=e.opts.ownProperties;for(w in i){var g=i[w],P=Array.isArray(g)?v:m;P[w]=g}a+="var "+c+" = errors;";var E=e.errorPath;a+="var missing"+s+";";for(var w in v)if((P=v[w]).length){if(a+=" if ( "+u+e.util.getProperty(w)+" !== undefined ",y&&(a+=" && Object.prototype.hasOwnProperty.call("+u+", '"+e.util.escapeQuotes(w)+"') "),h){a+=" && ( ";var b=P;if(b)for(var S=-1,j=b.length-1;S<j;){O=b[S+=1],S&&(a+=" || ");a+=" ( ( "+(k=u+(A=e.util.getProperty(O)))+" === undefined ",y&&(a+=" || ! Object.prototype.hasOwnProperty.call("+u+", '"+e.util.escapeQuotes(O)+"') "),a+=") && (missing"+s+" = "+e.util.toQuotedString(e.opts.jsonPointers?O:A)+") ) "}a+=")) {  ";var _="missing"+s,x="' + "+_+" + '";e.opts._errorDataPathProperty&&(e.errorPath=e.opts.jsonPointers?e.util.getPathExpr(E,_,!0):E+" + "+_);var F=F||[];F.push(a),a="",!1!==e.createErrors?(a+=" { keyword: 'dependencies' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { property: '"+e.util.escapeQuotes(w)+"', missingProperty: '"+x+"', depsCount: "+P.length+", deps: '"+e.util.escapeQuotes(1==P.length?P[0]:P.join(", "))+"' } ",!1!==e.opts.messages&&(a+=" , message: 'should have ",a+=1==P.length?"property "+e.util.escapeQuotes(P[0]):"properties "+e.util.escapeQuotes(P.join(", ")),a+=" when property "+e.util.escapeQuotes(w)+" is present' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ";var $=a;a=F.pop(),a+=!e.compositeRule&&h?e.async?" throw new ValidationError(["+$+"]); ":" validate.errors = ["+$+"]; return false; ":" var err = "+$+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}else{a+=" ) { ";var R=P;if(R)for(var O,D=-1,I=R.length-1;D<I;){O=R[D+=1];var A=e.util.getProperty(O),k=(x=e.util.escapeQuotes(O),u+A);e.opts._errorDataPathProperty&&(e.errorPath=e.util.getPath(E,O,e.opts.jsonPointers)),a+=" if ( "+k+" === undefined ",y&&(a+=" || ! Object.prototype.hasOwnProperty.call("+u+", '"+e.util.escapeQuotes(O)+"') "),a+=") {  var err =   ",!1!==e.createErrors?(a+=" { keyword: 'dependencies' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { property: '"+e.util.escapeQuotes(w)+"', missingProperty: '"+x+"', depsCount: "+P.length+", deps: '"+e.util.escapeQuotes(1==P.length?P[0]:P.join(", "))+"' } ",!1!==e.opts.messages&&(a+=" , message: 'should have ",a+=1==P.length?"property "+e.util.escapeQuotes(P[0]):"properties "+e.util.escapeQuotes(P.join(", ")),a+=" when property "+e.util.escapeQuotes(w)+" is present' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ",a+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } "}}a+=" }   ",h&&(f+="}",a+=" else { ")}e.errorPath=E;var q=d.baseId;for(var w in m){e.util.schemaHasRules(g=m[w],e.RULES.all)&&(a+=" "+p+" = true; if ( "+u+e.util.getProperty(w)+" !== undefined ",y&&(a+=" && Object.prototype.hasOwnProperty.call("+u+", '"+e.util.escapeQuotes(w)+"') "),a+=") { ",d.schema=g,d.schemaPath=n+e.util.getProperty(w),d.errSchemaPath=l+"/"+e.util.escapeFragment(w),a+="  "+e.validate(d)+" ",d.baseId=q,a+=" }  ",h&&(a+=" if ("+p+") { ",f+="}"))}return h&&(a+="   "+f+" if ("+c+" == errors) {"),a=e.util.cleanUpCode(a)}},{}],23:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a=" ",s=e.level,o=e.dataLevel,i=e.schema[r],n=e.schemaPath+e.util.getProperty(r),l=e.errSchemaPath+"/"+r,h=!e.opts.allErrors,u="data"+(o||""),c="valid"+s,d=e.opts.$data&&i&&i.$data;d&&(a+=" var schema"+s+" = "+e.util.getData(i.$data,o,e.dataPathArr)+"; ");var f="i"+s,p="schema"+s;d||(a+=" var "+p+" = validate.schema"+n+";"),a+="var "+c+";",d&&(a+=" if (schema"+s+" === undefined) "+c+" = true; else if (!Array.isArray(schema"+s+")) "+c+" = false; else {"),a+=c+" = false;for (var "+f+"=0; "+f+"<"+p+".length; "+f+"++) if (equal("+u+", "+p+"["+f+"])) { "+c+" = true; break; }",d&&(a+="  }  "),a+=" if (!"+c+") {   ";var m=m||[];m.push(a),a="",!1!==e.createErrors?(a+=" { keyword: 'enum' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { allowedValues: schema"+s+" } ",!1!==e.opts.messages&&(a+=" , message: 'should be equal to one of the allowed values' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ";var v=a;return a=m.pop(),a+=!e.compositeRule&&h?e.async?" throw new ValidationError(["+v+"]); ":" validate.errors = ["+v+"]; return false; ":" var err = "+v+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",a+=" }",h&&(a+=" else { "),a}},{}],24:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a=" ",s=e.level,o=e.dataLevel,i=e.schema[r],n=e.schemaPath+e.util.getProperty(r),l=e.errSchemaPath+"/"+r,h=!e.opts.allErrors,u="data"+(o||"");if(!1===e.opts.format)return h&&(a+=" if (true) { "),a;var c,d=e.opts.$data&&i&&i.$data;d?(a+=" var schema"+s+" = "+e.util.getData(i.$data,o,e.dataPathArr)+"; ",c="schema"+s):c=i;var f=e.opts.unknownFormats,p=Array.isArray(f);if(d){a+=" var "+(m="format"+s)+" = formats["+c+"]; var "+(v="isObject"+s)+" = typeof "+m+" == 'object' && !("+m+" instanceof RegExp) && "+m+".validate; var "+(y="formatType"+s)+" = "+v+" && "+m+".type || 'string'; if ("+v+") { ",e.async&&(a+=" var async"+s+" = "+m+".async; "),a+=" "+m+" = "+m+".validate; } if (  ",d&&(a+=" ("+c+" !== undefined && typeof "+c+" != 'string') || "),a+=" (","ignore"!=f&&(a+=" ("+c+" && !"+m+" ",p&&(a+=" && self._opts.unknownFormats.indexOf("+c+") == -1 "),a+=") || "),a+=" ("+m+" && "+y+" == '"+t+"' && !(typeof "+m+" == 'function' ? ",a+=e.async?" (async"+s+" ? "+e.yieldAwait+" "+m+"("+u+") : "+m+"("+u+")) ":" "+m+"("+u+") ",a+=" : "+m+".test("+u+"))))) {"}else{var m;if(!(m=e.formats[i])){if("ignore"==f)return e.logger.warn('unknown format "'+i+'" ignored in schema at path "'+e.errSchemaPath+'"'),h&&(a+=" if (true) { "),a;if(p&&f.indexOf(i)>=0)return h&&(a+=" if (true) { "),a;throw new Error('unknown format "'+i+'" is used in schema at path "'+e.errSchemaPath+'"')}var v,y=(v="object"==typeof m&&!(m instanceof RegExp)&&m.validate)&&m.type||"string";if(v){var g=!0===m.async;m=m.validate}if(y!=t)return h&&(a+=" if (true) { "),a;if(g){if(!e.async)throw new Error("async format in sync schema");var P="formats"+e.util.getProperty(i)+".validate";a+=" if (!("+e.yieldAwait+" "+P+"("+u+"))) { "}else{a+=" if (! ";P="formats"+e.util.getProperty(i);v&&(P+=".validate"),a+="function"==typeof m?" "+P+"("+u+") ":" "+P+".test("+u+") ",a+=") { "}}var E=E||[];E.push(a),a="",!1!==e.createErrors?(a+=" { keyword: 'format' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { format:  ",a+=d?""+c:""+e.util.toQuotedString(i),a+="  } ",!1!==e.opts.messages&&(a+=" , message: 'should match format \"",a+=d?"' + "+c+" + '":""+e.util.escapeQuotes(i),a+="\"' "),e.opts.verbose&&(a+=" , schema:  ",a+=d?"validate.schema"+n:""+e.util.toQuotedString(i),a+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ";var w=a;return a=E.pop(),a+=!e.compositeRule&&h?e.async?" throw new ValidationError(["+w+"]); ":" validate.errors = ["+w+"]; return false; ":" var err = "+w+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",a+=" } ",h&&(a+=" else { "),a}},{}],25:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a=" ",s=e.level,o=e.dataLevel,i=e.schema[r],n=e.schemaPath+e.util.getProperty(r),l=e.errSchemaPath+"/"+r,h=!e.opts.allErrors,u="data"+(o||""),c="valid"+s,d="errs__"+s,f=e.util.copy(e),p="";f.level++;var m="valid"+f.level,v="i"+s,y=f.dataLevel=e.dataLevel+1,g="data"+y,P=e.baseId;if(a+="var "+d+" = errors;var "+c+";",Array.isArray(i)){var E=e.schema.additionalItems;if(!1===E){a+=" "+c+" = "+u+".length <= "+i.length+"; ";var w=l;l=e.errSchemaPath+"/additionalItems",a+="  if (!"+c+") {   ";var b=b||[];b.push(a),a="",!1!==e.createErrors?(a+=" { keyword: 'additionalItems' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { limit: "+i.length+" } ",!1!==e.opts.messages&&(a+=" , message: 'should NOT have more than "+i.length+" items' "),e.opts.verbose&&(a+=" , schema: false , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ";var S=a;a=b.pop(),a+=!e.compositeRule&&h?e.async?" throw new ValidationError(["+S+"]); ":" validate.errors = ["+S+"]; return false; ":" var err = "+S+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",a+=" } ",l=w,h&&(p+="}",a+=" else { ")}var j=i;if(j)for(var _,x=-1,F=j.length-1;x<F;)if(_=j[x+=1],e.util.schemaHasRules(_,e.RULES.all)){a+=" "+m+" = true; if ("+u+".length > "+x+") { ";var $=u+"["+x+"]";f.schema=_,f.schemaPath=n+"["+x+"]",f.errSchemaPath=l+"/"+x,f.errorPath=e.util.getPathExpr(e.errorPath,x,e.opts.jsonPointers,!0),f.dataPathArr[y]=x;var R=e.validate(f);f.baseId=P,e.util.varOccurences(R,g)<2?a+=" "+e.util.varReplace(R,g,$)+" ":a+=" var "+g+" = "+$+"; "+R+" ",a+=" }  ",h&&(a+=" if ("+m+") { ",p+="}")}if("object"==typeof E&&e.util.schemaHasRules(E,e.RULES.all)){f.schema=E,f.schemaPath=e.schemaPath+".additionalItems",f.errSchemaPath=e.errSchemaPath+"/additionalItems",a+=" "+m+" = true; if ("+u+".length > "+i.length+") {  for (var "+v+" = "+i.length+"; "+v+" < "+u+".length; "+v+"++) { ",f.errorPath=e.util.getPathExpr(e.errorPath,v,e.opts.jsonPointers,!0);$=u+"["+v+"]";f.dataPathArr[y]=v;R=e.validate(f);f.baseId=P,e.util.varOccurences(R,g)<2?a+=" "+e.util.varReplace(R,g,$)+" ":a+=" var "+g+" = "+$+"; "+R+" ",h&&(a+=" if (!"+m+") break; "),a+=" } }  ",h&&(a+=" if ("+m+") { ",p+="}")}}else if(e.util.schemaHasRules(i,e.RULES.all)){f.schema=i,f.schemaPath=n,f.errSchemaPath=l,a+="  for (var "+v+" = 0; "+v+" < "+u+".length; "+v+"++) { ",f.errorPath=e.util.getPathExpr(e.errorPath,v,e.opts.jsonPointers,!0);$=u+"["+v+"]";f.dataPathArr[y]=v;R=e.validate(f);f.baseId=P,e.util.varOccurences(R,g)<2?a+=" "+e.util.varReplace(R,g,$)+" ":a+=" var "+g+" = "+$+"; "+R+" ",h&&(a+=" if (!"+m+") break; "),a+=" }"}return h&&(a+=" "+p+" if ("+d+" == errors) {"),a=e.util.cleanUpCode(a)}},{}],26:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a,s=" ",o=e.level,i=e.dataLevel,n=e.schema[r],l=e.schemaPath+e.util.getProperty(r),h=e.errSchemaPath+"/"+r,u=!e.opts.allErrors,c="data"+(i||""),d=e.opts.$data&&n&&n.$data;d?(s+=" var schema"+o+" = "+e.util.getData(n.$data,i,e.dataPathArr)+"; ",a="schema"+o):a=n,s+="var division"+o+";if (",d&&(s+=" "+a+" !== undefined && ( typeof "+a+" != 'number' || "),s+=" (division"+o+" = "+c+" / "+a+", ",s+=e.opts.multipleOfPrecision?" Math.abs(Math.round(division"+o+") - division"+o+") > 1e-"+e.opts.multipleOfPrecision+" ":" division"+o+" !== parseInt(division"+o+") ",s+=" ) ",d&&(s+="  )  "),s+=" ) {   ";var f=f||[];f.push(s),s="",!1!==e.createErrors?(s+=" { keyword: 'multipleOf' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(h)+" , params: { multipleOf: "+a+" } ",!1!==e.opts.messages&&(s+=" , message: 'should be multiple of ",s+=d?"' + "+a:a+"'"),e.opts.verbose&&(s+=" , schema:  ",s+=d?"validate.schema"+l:""+n,s+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "),s+=" } "):s+=" {} ";var p=s;return s=f.pop(),s+=!e.compositeRule&&u?e.async?" throw new ValidationError(["+p+"]); ":" validate.errors = ["+p+"]; return false; ":" var err = "+p+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",s+="} ",u&&(s+=" else { "),s}},{}],27:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a=" ",s=e.level,o=e.dataLevel,i=e.schema[r],n=e.schemaPath+e.util.getProperty(r),l=e.errSchemaPath+"/"+r,h=!e.opts.allErrors,u="data"+(o||""),c="errs__"+s,d=e.util.copy(e);d.level++;var f="valid"+d.level;if(e.util.schemaHasRules(i,e.RULES.all)){d.schema=i,d.schemaPath=n,d.errSchemaPath=l,a+=" var "+c+" = errors;  ";var p=e.compositeRule;e.compositeRule=d.compositeRule=!0,d.createErrors=!1;var m;d.opts.allErrors&&(m=d.opts.allErrors,d.opts.allErrors=!1),a+=" "+e.validate(d)+" ",d.createErrors=!0,m&&(d.opts.allErrors=m),e.compositeRule=d.compositeRule=p,a+=" if ("+f+") {   ";var v=v||[];v.push(a),a="",!1!==e.createErrors?(a+=" { keyword: 'not' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: {} ",!1!==e.opts.messages&&(a+=" , message: 'should NOT be valid' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ";var y=a;a=v.pop(),a+=!e.compositeRule&&h?e.async?" throw new ValidationError(["+y+"]); ":" validate.errors = ["+y+"]; return false; ":" var err = "+y+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",a+=" } else {  errors = "+c+"; if (vErrors !== null) { if ("+c+") vErrors.length = "+c+"; else vErrors = null; } ",e.opts.allErrors&&(a+=" } ")}else a+="  var err =   ",!1!==e.createErrors?(a+=" { keyword: 'not' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: {} ",!1!==e.opts.messages&&(a+=" , message: 'should NOT be valid' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ",a+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",h&&(a+=" if (false) { ");return a}},{}],28:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a=" ",s=e.level,o=e.dataLevel,i=e.schema[r],n=e.schemaPath+e.util.getProperty(r),l=e.errSchemaPath+"/"+r,h=!e.opts.allErrors,u="data"+(o||""),c="valid"+s,d="errs__"+s,f=e.util.copy(e),p="";f.level++;var m="valid"+f.level;a+="var "+d+" = errors;var prevValid"+s+" = false;var "+c+" = false;";var v=f.baseId,y=e.compositeRule;e.compositeRule=f.compositeRule=!0;var g=i;if(g)for(var P,E=-1,w=g.length-1;E<w;)P=g[E+=1],e.util.schemaHasRules(P,e.RULES.all)?(f.schema=P,f.schemaPath=n+"["+E+"]",f.errSchemaPath=l+"/"+E,a+="  "+e.validate(f)+" ",f.baseId=v):a+=" var "+m+" = true; ",E&&(a+=" if ("+m+" && prevValid"+s+") "+c+" = false; else { ",p+="}"),a+=" if ("+m+") "+c+" = prevValid"+s+" = true;";return e.compositeRule=f.compositeRule=y,a+=p+"if (!"+c+") {   var err =   ",!1!==e.createErrors?(a+=" { keyword: 'oneOf' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: {} ",!1!==e.opts.messages&&(a+=" , message: 'should match exactly one schema in oneOf' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ",a+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",!e.compositeRule&&h&&(a+=e.async?" throw new ValidationError(vErrors); ":" validate.errors = vErrors; return false; "),a+="} else {  errors = "+d+"; if (vErrors !== null) { if ("+d+") vErrors.length = "+d+"; else vErrors = null; }",e.opts.allErrors&&(a+=" } "),a}},{}],29:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a,s=" ",o=e.level,i=e.dataLevel,n=e.schema[r],l=e.schemaPath+e.util.getProperty(r),h=e.errSchemaPath+"/"+r,u=!e.opts.allErrors,c="data"+(i||""),d=e.opts.$data&&n&&n.$data;d?(s+=" var schema"+o+" = "+e.util.getData(n.$data,i,e.dataPathArr)+"; ",a="schema"+o):a=n;var f=d?"(new RegExp("+a+"))":e.usePattern(n);s+="if ( ",d&&(s+=" ("+a+" !== undefined && typeof "+a+" != 'string') || "),s+=" !"+f+".test("+c+") ) {   ";var p=p||[];p.push(s),s="",!1!==e.createErrors?(s+=" { keyword: 'pattern' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(h)+" , params: { pattern:  ",s+=d?""+a:""+e.util.toQuotedString(n),s+="  } ",!1!==e.opts.messages&&(s+=" , message: 'should match pattern \"",s+=d?"' + "+a+" + '":""+e.util.escapeQuotes(n),s+="\"' "),e.opts.verbose&&(s+=" , schema:  ",s+=d?"validate.schema"+l:""+e.util.toQuotedString(n),s+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "),s+=" } "):s+=" {} ";var m=s;return s=p.pop(),s+=!e.compositeRule&&u?e.async?" throw new ValidationError(["+m+"]); ":" validate.errors = ["+m+"]; return false; ":" var err = "+m+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",s+="} ",u&&(s+=" else { "),s}},{}],30:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a=" ",s=e.level,o=e.dataLevel,i=e.schema[r],n=e.schemaPath+e.util.getProperty(r),l=e.errSchemaPath+"/"+r,h=!e.opts.allErrors,u="data"+(o||""),c="valid"+s,d="errs__"+s,f=e.util.copy(e),p="";f.level++;var m="valid"+f.level,v="key"+s,y="idx"+s,g=f.dataLevel=e.dataLevel+1,P="data"+g,E="dataProperties"+s,w=Object.keys(i||{}),b=e.schema.patternProperties||{},S=Object.keys(b),j=e.schema.additionalProperties,_=w.length||S.length,x=!1===j,F="object"==typeof j&&Object.keys(j).length,$=e.opts.removeAdditional,R=x||F||$,O=e.opts.ownProperties,D=e.baseId,I=e.schema.required;if(I&&(!e.opts.v5||!I.$data)&&I.length<e.opts.loopRequired)var A=e.util.toHash(I);if(e.opts.patternGroups)var k=e.schema.patternGroups||{},q=Object.keys(k);if(a+="var "+d+" = errors;var "+m+" = true;",O&&(a+=" var "+E+" = undefined;"),R){if(a+=O?" "+E+" = "+E+" || Object.keys("+u+"); for (var "+y+"=0; "+y+"<"+E+".length; "+y+"++) { var "+v+" = "+E+"["+y+"]; ":" for (var "+v+" in "+u+") { ",_){if(a+=" var isAdditional"+s+" = !(false ",w.length)if(w.length>5)a+=" || validate.schema"+n+"["+v+"] ";else{var L=w;if(L)for(var z=-1,C=L.length-1;z<C;)X=L[z+=1],a+=" || "+v+" == "+e.util.toQuotedString(X)+" "}if(S.length){var Q=S;if(Q)for(var U=-1,V=Q.length-1;U<V;)ne=Q[U+=1],a+=" || "+e.usePattern(ne)+".test("+v+") "}if(e.opts.patternGroups&&q.length){var N=q;if(N){U=-1;for(var T=N.length-1;U<T;)ce=N[U+=1],a+=" || "+e.usePattern(ce)+".test("+v+") "}}a+=" ); if (isAdditional"+s+") { "}if("all"==$)a+=" delete "+u+"["+v+"]; ";else{var M=e.errorPath,H="' + "+v+" + '";if(e.opts._errorDataPathProperty&&(e.errorPath=e.util.getPathExpr(e.errorPath,v,e.opts.jsonPointers)),x)if($)a+=" delete "+u+"["+v+"]; ";else{a+=" "+m+" = false; ";var K=l;l=e.errSchemaPath+"/additionalProperties";(we=we||[]).push(a),a="",!1!==e.createErrors?(a+=" { keyword: 'additionalProperties' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { additionalProperty: '"+H+"' } ",!1!==e.opts.messages&&(a+=" , message: 'should NOT have additional properties' "),e.opts.verbose&&(a+=" , schema: false , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ";var B=a;a=we.pop(),a+=!e.compositeRule&&h?e.async?" throw new ValidationError(["+B+"]); ":" validate.errors = ["+B+"]; return false; ":" var err = "+B+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",l=K,h&&(a+=" break; ")}else if(F)if("failing"==$){a+=" var "+d+" = errors;  ";var G=e.compositeRule;e.compositeRule=f.compositeRule=!0,f.schema=j,f.schemaPath=e.schemaPath+".additionalProperties",f.errSchemaPath=e.errSchemaPath+"/additionalProperties",f.errorPath=e.opts._errorDataPathProperty?e.errorPath:e.util.getPathExpr(e.errorPath,v,e.opts.jsonPointers);var J=u+"["+v+"]";f.dataPathArr[g]=v;var Z=e.validate(f);f.baseId=D,e.util.varOccurences(Z,P)<2?a+=" "+e.util.varReplace(Z,P,J)+" ":a+=" var "+P+" = "+J+"; "+Z+" ",a+=" if (!"+m+") { errors = "+d+"; if (validate.errors !== null) { if (errors) validate.errors.length = errors; else validate.errors = null; } delete "+u+"["+v+"]; }  ",e.compositeRule=f.compositeRule=G}else{f.schema=j,f.schemaPath=e.schemaPath+".additionalProperties",f.errSchemaPath=e.errSchemaPath+"/additionalProperties",f.errorPath=e.opts._errorDataPathProperty?e.errorPath:e.util.getPathExpr(e.errorPath,v,e.opts.jsonPointers);J=u+"["+v+"]";f.dataPathArr[g]=v;Z=e.validate(f);f.baseId=D,e.util.varOccurences(Z,P)<2?a+=" "+e.util.varReplace(Z,P,J)+" ":a+=" var "+P+" = "+J+"; "+Z+" ",h&&(a+=" if (!"+m+") break; ")}e.errorPath=M}_&&(a+=" } "),a+=" }  ",h&&(a+=" if ("+m+") { ",p+="}")}var Y=e.opts.useDefaults&&!e.compositeRule;if(w.length){var W=w;if(W)for(var X,ee=-1,re=W.length-1;ee<re;){X=W[ee+=1];if(e.util.schemaHasRules(pe=i[X],e.RULES.all)){var te=e.util.getProperty(X),ae=(J=u+te,Y&&void 0!==pe.default);f.schema=pe,f.schemaPath=n+te,f.errSchemaPath=l+"/"+e.util.escapeFragment(X),f.errorPath=e.util.getPath(e.errorPath,X,e.opts.jsonPointers),f.dataPathArr[g]=e.util.toQuotedString(X);Z=e.validate(f);if(f.baseId=D,e.util.varOccurences(Z,P)<2){Z=e.util.varReplace(Z,P,J);var se=J}else{se=P;a+=" var "+P+" = "+J+"; "}if(ae)a+=" "+Z+" ";else{if(A&&A[X]){a+=" if ( "+se+" === undefined ",O&&(a+=" || ! Object.prototype.hasOwnProperty.call("+u+", '"+e.util.escapeQuotes(X)+"') "),a+=") { "+m+" = false; ";M=e.errorPath,K=l;var oe=e.util.escapeQuotes(X);e.opts._errorDataPathProperty&&(e.errorPath=e.util.getPath(M,X,e.opts.jsonPointers)),l=e.errSchemaPath+"/required";(we=we||[]).push(a),a="",!1!==e.createErrors?(a+=" { keyword: 'required' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { missingProperty: '"+oe+"' } ",!1!==e.opts.messages&&(a+=" , message: '",a+=e.opts._errorDataPathProperty?"is a required property":"should have required property \\'"+oe+"\\'",a+="' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ";B=a;a=we.pop(),a+=!e.compositeRule&&h?e.async?" throw new ValidationError(["+B+"]); ":" validate.errors = ["+B+"]; return false; ":" var err = "+B+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",l=K,e.errorPath=M,a+=" } else { "}else h?(a+=" if ( "+se+" === undefined ",O&&(a+=" || ! Object.prototype.hasOwnProperty.call("+u+", '"+e.util.escapeQuotes(X)+"') "),a+=") { "+m+" = true; } else { "):(a+=" if ("+se+" !== undefined ",O&&(a+=" &&   Object.prototype.hasOwnProperty.call("+u+", '"+e.util.escapeQuotes(X)+"') "),a+=" ) { ");a+=" "+Z+" } "}}h&&(a+=" if ("+m+") { ",p+="}")}}if(S.length){var ie=S;if(ie)for(var ne,le=-1,he=ie.length-1;le<he;){ne=ie[le+=1];if(e.util.schemaHasRules(pe=b[ne],e.RULES.all)){f.schema=pe,f.schemaPath=e.schemaPath+".patternProperties"+e.util.getProperty(ne),f.errSchemaPath=e.errSchemaPath+"/patternProperties/"+e.util.escapeFragment(ne),a+=O?" "+E+" = "+E+" || Object.keys("+u+"); for (var "+y+"=0; "+y+"<"+E+".length; "+y+"++) { var "+v+" = "+E+"["+y+"]; ":" for (var "+v+" in "+u+") { ",a+=" if ("+e.usePattern(ne)+".test("+v+")) { ",f.errorPath=e.util.getPathExpr(e.errorPath,v,e.opts.jsonPointers);J=u+"["+v+"]";f.dataPathArr[g]=v;Z=e.validate(f);f.baseId=D,e.util.varOccurences(Z,P)<2?a+=" "+e.util.varReplace(Z,P,J)+" ":a+=" var "+P+" = "+J+"; "+Z+" ",h&&(a+=" if (!"+m+") break; "),a+=" } ",h&&(a+=" else "+m+" = true; "),a+=" }  ",h&&(a+=" if ("+m+") { ",p+="}")}}}if(e.opts.patternGroups&&q.length){var ue=q;if(ue)for(var ce,de=-1,fe=ue.length-1;de<fe;){var pe,me=k[ce=ue[de+=1]];if(e.util.schemaHasRules(pe=me.schema,e.RULES.all)){f.schema=pe,f.schemaPath=e.schemaPath+".patternGroups"+e.util.getProperty(ce)+".schema",f.errSchemaPath=e.errSchemaPath+"/patternGroups/"+e.util.escapeFragment(ce)+"/schema",a+=" var pgPropCount"+s+" = 0;  ",a+=O?" "+E+" = "+E+" || Object.keys("+u+"); for (var "+y+"=0; "+y+"<"+E+".length; "+y+"++) { var "+v+" = "+E+"["+y+"]; ":" for (var "+v+" in "+u+") { ",a+=" if ("+e.usePattern(ce)+".test("+v+")) { pgPropCount"+s+"++; ",f.errorPath=e.util.getPathExpr(e.errorPath,v,e.opts.jsonPointers);J=u+"["+v+"]";f.dataPathArr[g]=v;Z=e.validate(f);f.baseId=D,e.util.varOccurences(Z,P)<2?a+=" "+e.util.varReplace(Z,P,J)+" ":a+=" var "+P+" = "+J+"; "+Z+" ",h&&(a+=" if (!"+m+") break; "),a+=" } ",h&&(a+=" else "+m+" = true; "),a+=" }  ",h&&(a+=" if ("+m+") { ",p+="}");var ve=me.minimum,ye=me.maximum;if(void 0!==ve||void 0!==ye){a+=" var "+c+" = true; ";K=l;if(void 0!==ve){var ge=ve,Pe="minimum",Ee="less";a+=" "+c+" = pgPropCount"+s+" >= "+ve+"; ",l=e.errSchemaPath+"/patternGroups/minimum",a+="  if (!"+c+") {   ";(we=we||[]).push(a),a="",!1!==e.createErrors?(a+=" { keyword: 'patternGroups' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { reason: '"+Pe+"', limit: "+ge+", pattern: '"+e.util.escapeQuotes(ce)+"' } ",!1!==e.opts.messages&&(a+=" , message: 'should NOT have "+Ee+" than "+ge+' properties matching pattern "'+e.util.escapeQuotes(ce)+"\"' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ";B=a;a=we.pop(),a+=!e.compositeRule&&h?e.async?" throw new ValidationError(["+B+"]); ":" validate.errors = ["+B+"]; return false; ":" var err = "+B+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",a+=" } ",void 0!==ye&&(a+=" else ")}if(void 0!==ye){ge=ye,Pe="maximum",Ee="more";a+=" "+c+" = pgPropCount"+s+" <= "+ye+"; ",l=e.errSchemaPath+"/patternGroups/maximum",a+="  if (!"+c+") {   ";var we;(we=we||[]).push(a),a="",!1!==e.createErrors?(a+=" { keyword: 'patternGroups' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { reason: '"+Pe+"', limit: "+ge+", pattern: '"+e.util.escapeQuotes(ce)+"' } ",!1!==e.opts.messages&&(a+=" , message: 'should NOT have "+Ee+" than "+ge+' properties matching pattern "'+e.util.escapeQuotes(ce)+"\"' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ";B=a;a=we.pop(),a+=!e.compositeRule&&h?e.async?" throw new ValidationError(["+B+"]); ":" validate.errors = ["+B+"]; return false; ":" var err = "+B+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",a+=" } "}l=K,h&&(a+=" if ("+c+") { ",p+="}")}}}}return h&&(a+=" "+p+" if ("+d+" == errors) {"),a=e.util.cleanUpCode(a)}},{}],31:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a=" ",s=e.level,o=e.dataLevel,i=e.schema[r],n=e.schemaPath+e.util.getProperty(r),l=e.errSchemaPath+"/"+r,h=!e.opts.allErrors,u="data"+(o||""),c="errs__"+s,d=e.util.copy(e);d.level++;var f="valid"+d.level;if(e.util.schemaHasRules(i,e.RULES.all)){d.schema=i,d.schemaPath=n,d.errSchemaPath=l;var p="key"+s,m="idx"+s,v="i"+s,y="' + "+p+" + '",g="data"+(d.dataLevel=e.dataLevel+1),P="dataProperties"+s,E=e.opts.ownProperties,w=e.baseId;a+=" var "+c+" = errors; ",E&&(a+=" var "+P+" = undefined; "),a+=E?" "+P+" = "+P+" || Object.keys("+u+"); for (var "+m+"=0; "+m+"<"+P+".length; "+m+"++) { var "+p+" = "+P+"["+m+"]; ":" for (var "+p+" in "+u+") { ",a+=" var startErrs"+s+" = errors; ";var b=p,S=e.compositeRule;e.compositeRule=d.compositeRule=!0;var j=e.validate(d);d.baseId=w,e.util.varOccurences(j,g)<2?a+=" "+e.util.varReplace(j,g,b)+" ":a+=" var "+g+" = "+b+"; "+j+" ",e.compositeRule=d.compositeRule=S,a+=" if (!"+f+") { for (var "+v+"=startErrs"+s+"; "+v+"<errors; "+v+"++) { vErrors["+v+"].propertyName = "+p+"; }   var err =   ",!1!==e.createErrors?(a+=" { keyword: 'propertyNames' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { propertyName: '"+y+"' } ",!1!==e.opts.messages&&(a+=" , message: 'property name \\'"+y+"\\' is invalid' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ",a+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",!e.compositeRule&&h&&(a+=e.async?" throw new ValidationError(vErrors); ":" validate.errors = vErrors; return false; "),h&&(a+=" break; "),a+=" } }"}return h&&(a+="  if ("+c+" == errors) {"),a=e.util.cleanUpCode(a)}},{}],32:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a,s,o=" ",i=e.dataLevel,n=e.schema[r],l=e.errSchemaPath+"/"+r,h=!e.opts.allErrors,u="data"+(i||""),c="valid"+e.level;if("#"==n||"#/"==n)e.isRoot?(a=e.async,s="validate"):(a=!0===e.root.schema.$async,s="root.refVal[0]");else{var d=e.resolveRef(e.baseId,n,e.isRoot);if(void 0===d){var f=e.MissingRefError.message(e.baseId,n);if("fail"==e.opts.missingRefs){e.logger.error(f);(y=y||[]).push(o),o="",!1!==e.createErrors?(o+=" { keyword: '$ref' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { ref: '"+e.util.escapeQuotes(n)+"' } ",!1!==e.opts.messages&&(o+=" , message: 'can\\'t resolve reference "+e.util.escapeQuotes(n)+"' "),e.opts.verbose&&(o+=" , schema: "+e.util.toQuotedString(n)+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),o+=" } "):o+=" {} ";var p=o;o=y.pop(),o+=!e.compositeRule&&h?e.async?" throw new ValidationError(["+p+"]); ":" validate.errors = ["+p+"]; return false; ":" var err = "+p+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",h&&(o+=" if (false) { ")}else{if("ignore"!=e.opts.missingRefs)throw new e.MissingRefError(e.baseId,n,f);e.logger.warn(f),h&&(o+=" if (true) { ")}}else if(d.inline){var m=e.util.copy(e);m.level++;var v="valid"+m.level;m.schema=d.schema,m.schemaPath="",m.errSchemaPath=n;o+=" "+e.validate(m).replace(/validate\.schema/g,d.code)+" ",h&&(o+=" if ("+v+") { ")}else a=!0===d.$async,s=d.code}if(s){var y;(y=y||[]).push(o),o="",o+=e.opts.passContext?" "+s+".call(this, ":" "+s+"( ",o+=" "+u+", (dataPath || '')",'""'!=e.errorPath&&(o+=" + "+e.errorPath);var g=o+=" , "+(i?"data"+(i-1||""):"parentData")+" , "+(i?e.dataPathArr[i]:"parentDataProperty")+", rootData)  ";if(o=y.pop(),a){if(!e.async)throw new Error("async schema referenced by sync schema");h&&(o+=" var "+c+"; "),o+=" try { "+e.yieldAwait+" "+g+"; ",h&&(o+=" "+c+" = true; "),o+=" } catch (e) { if (!(e instanceof ValidationError)) throw e; if (vErrors === null) vErrors = e.errors; else vErrors = vErrors.concat(e.errors); errors = vErrors.length; ",h&&(o+=" "+c+" = false; "),o+=" } ",h&&(o+=" if ("+c+") { ")}else o+=" if (!"+g+") { if (vErrors === null) vErrors = "+s+".errors; else vErrors = vErrors.concat("+s+".errors); errors = vErrors.length; } ",h&&(o+=" else { ")}return o}},{}],33:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a=" ",s=e.level,o=e.dataLevel,i=e.schema[r],n=e.schemaPath+e.util.getProperty(r),l=e.errSchemaPath+"/"+r,h=!e.opts.allErrors,u="data"+(o||""),c="valid"+s,d=e.opts.$data&&i&&i.$data;d&&(a+=" var schema"+s+" = "+e.util.getData(i.$data,o,e.dataPathArr)+"; ");var f="schema"+s;if(!d)if(i.length<e.opts.loopRequired&&e.schema.properties&&Object.keys(e.schema.properties).length){var p=[],m=i;if(m)for(var v,y=-1,g=m.length-1;y<g;){v=m[y+=1];var P=e.schema.properties[v];P&&e.util.schemaHasRules(P,e.RULES.all)||(p[p.length]=v)}}else p=i;if(d||p.length){var E=e.errorPath,w=d||p.length>=e.opts.loopRequired,b=e.opts.ownProperties;if(h)if(a+=" var missing"+s+"; ",w){d||(a+=" var "+f+" = validate.schema"+n+"; ");var S="' + "+(R="schema"+s+"["+(x="i"+s)+"]")+" + '";e.opts._errorDataPathProperty&&(e.errorPath=e.util.getPathExpr(E,R,e.opts.jsonPointers)),a+=" var "+c+" = true; ",d&&(a+=" if (schema"+s+" === undefined) "+c+" = true; else if (!Array.isArray(schema"+s+")) "+c+" = false; else {"),a+=" for (var "+x+" = 0; "+x+" < "+f+".length; "+x+"++) { "+c+" = "+u+"["+f+"["+x+"]] !== undefined ",b&&(a+=" &&   Object.prototype.hasOwnProperty.call("+u+", "+f+"["+x+"]) "),a+="; if (!"+c+") break; } ",d&&(a+="  }  "),a+="  if (!"+c+") {   ";($=$||[]).push(a),a="",!1!==e.createErrors?(a+=" { keyword: 'required' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { missingProperty: '"+S+"' } ",!1!==e.opts.messages&&(a+=" , message: '",a+=e.opts._errorDataPathProperty?"is a required property":"should have required property \\'"+S+"\\'",a+="' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ";var j=a;a=$.pop(),a+=!e.compositeRule&&h?e.async?" throw new ValidationError(["+j+"]); ":" validate.errors = ["+j+"]; return false; ":" var err = "+j+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",a+=" } else { "}else{a+=" if ( ";var _=p;if(_)for(var x=-1,F=_.length-1;x<F;){D=_[x+=1],x&&(a+=" || ");a+=" ( ( "+(q=u+(k=e.util.getProperty(D)))+" === undefined ",b&&(a+=" || ! Object.prototype.hasOwnProperty.call("+u+", '"+e.util.escapeQuotes(D)+"') "),a+=") && (missing"+s+" = "+e.util.toQuotedString(e.opts.jsonPointers?D:k)+") ) "}a+=") {  ";S="' + "+(R="missing"+s)+" + '";e.opts._errorDataPathProperty&&(e.errorPath=e.opts.jsonPointers?e.util.getPathExpr(E,R,!0):E+" + "+R);var $;($=$||[]).push(a),a="",!1!==e.createErrors?(a+=" { keyword: 'required' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { missingProperty: '"+S+"' } ",!1!==e.opts.messages&&(a+=" , message: '",a+=e.opts._errorDataPathProperty?"is a required property":"should have required property \\'"+S+"\\'",a+="' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ";j=a;a=$.pop(),a+=!e.compositeRule&&h?e.async?" throw new ValidationError(["+j+"]); ":" validate.errors = ["+j+"]; return false; ":" var err = "+j+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",a+=" } else { "}else if(w){d||(a+=" var "+f+" = validate.schema"+n+"; ");var R;S="' + "+(R="schema"+s+"["+(x="i"+s)+"]")+" + '";e.opts._errorDataPathProperty&&(e.errorPath=e.util.getPathExpr(E,R,e.opts.jsonPointers)),d&&(a+=" if ("+f+" && !Array.isArray("+f+")) {  var err =   ",!1!==e.createErrors?(a+=" { keyword: 'required' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { missingProperty: '"+S+"' } ",!1!==e.opts.messages&&(a+=" , message: '",a+=e.opts._errorDataPathProperty?"is a required property":"should have required property \\'"+S+"\\'",a+="' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ",a+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } else if ("+f+" !== undefined) { "),a+=" for (var "+x+" = 0; "+x+" < "+f+".length; "+x+"++) { if ("+u+"["+f+"["+x+"]] === undefined ",b&&(a+=" || ! Object.prototype.hasOwnProperty.call("+u+", "+f+"["+x+"]) "),a+=") {  var err =   ",!1!==e.createErrors?(a+=" { keyword: 'required' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { missingProperty: '"+S+"' } ",!1!==e.opts.messages&&(a+=" , message: '",a+=e.opts._errorDataPathProperty?"is a required property":"should have required property \\'"+S+"\\'",a+="' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ",a+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } } ",d&&(a+="  }  ")}else{var O=p;if(O)for(var D,I=-1,A=O.length-1;I<A;){D=O[I+=1];var k=e.util.getProperty(D),q=(S=e.util.escapeQuotes(D),u+k);e.opts._errorDataPathProperty&&(e.errorPath=e.util.getPath(E,D,e.opts.jsonPointers)),a+=" if ( "+q+" === undefined ",b&&(a+=" || ! Object.prototype.hasOwnProperty.call("+u+", '"+e.util.escapeQuotes(D)+"') "),a+=") {  var err =   ",!1!==e.createErrors?(a+=" { keyword: 'required' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(l)+" , params: { missingProperty: '"+S+"' } ",!1!==e.opts.messages&&(a+=" , message: '",a+=e.opts._errorDataPathProperty?"is a required property":"should have required property \\'"+S+"\\'",a+="' "),e.opts.verbose&&(a+=" , schema: validate.schema"+n+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+u+" "),a+=" } "):a+=" {} ",a+=";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; } "}}e.errorPath=E}else h&&(a+=" if (true) {");return a}},{}],34:[function(e,r,t){"use strict";r.exports=function(e,r,t){var a,s=" ",o=e.level,i=e.dataLevel,n=e.schema[r],l=e.schemaPath+e.util.getProperty(r),h=e.errSchemaPath+"/"+r,u=!e.opts.allErrors,c="data"+(i||""),d="valid"+o,f=e.opts.$data&&n&&n.$data;if(f?(s+=" var schema"+o+" = "+e.util.getData(n.$data,i,e.dataPathArr)+"; ",a="schema"+o):a=n,(n||f)&&!1!==e.opts.uniqueItems){f&&(s+=" var "+d+"; if ("+a+" === false || "+a+" === undefined) "+d+" = true; else if (typeof "+a+" != 'boolean') "+d+" = false; else { "),s+=" var "+d+" = true; if ("+c+".length > 1) { var i = "+c+".length, j; outer: for (;i--;) { for (j = i; j--;) { if (equal("+c+"[i], "+c+"[j])) { "+d+" = false; break outer; } } } } ",f&&(s+="  }  "),s+=" if (!"+d+") {   ";var p=p||[];p.push(s),s="",!1!==e.createErrors?(s+=" { keyword: 'uniqueItems' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(h)+" , params: { i: i, j: j } ",!1!==e.opts.messages&&(s+=" , message: 'should NOT have duplicate items (items ## ' + j + ' and ' + i + ' are identical)' "),e.opts.verbose&&(s+=" , schema:  ",s+=f?"validate.schema"+l:""+n,s+="         , parentSchema: validate.schema"+e.schemaPath+" , data: "+c+" "),s+=" } "):s+=" {} ";var m=s;s=p.pop(),s+=!e.compositeRule&&u?e.async?" throw new ValidationError(["+m+"]); ":" validate.errors = ["+m+"]; return false; ":" var err = "+m+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",s+=" } ",u&&(s+=" else { ")}else u&&(s+=" if (true) { ");return s}},{}],35:[function(e,r,t){"use strict";r.exports=function(e,r,t){function a(e){for(var r=e.rules,t=0;t<r.length;t++)if(s(r[t]))return!0}function s(r){return void 0!==e.schema[r.keyword]||r.implements&&function(r){for(var t=r.implements,a=0;a<t.length;a++)if(void 0!==e.schema[t[a]])return!0}(r)}var o="",i=!0===e.schema.$async,n=e.util.schemaHasRulesExcept(e.schema,e.RULES.all,"$ref"),l=e.self._getId(e.schema);if(e.isTop){if(i){e.async=!0;var h="es7"==e.opts.async;e.yieldAwait=h?"await":"yield"}o+=" var validate = ",i?h?o+=" (async function ":("*"!=e.opts.async&&(o+="co.wrap"),o+="(function* "):o+=" (function ",o+=" (data, dataPath, parentData, parentDataProperty, rootData) { 'use strict'; ",l&&(e.opts.sourceCode||e.opts.processCode)&&(o+=" /*# sourceURL="+l+" */ ")}if("boolean"==typeof e.schema||!n&&!e.schema.$ref){var u=e.level,c=e.dataLevel,d=e.schema[r="false schema"],f=e.schemaPath+e.util.getProperty(r),p=e.errSchemaPath+"/"+r,m=!e.opts.allErrors,v="data"+(c||""),y="valid"+u;if(!1===e.schema){e.isTop?m=!0:o+=" var "+y+" = false; ";(Y=Y||[]).push(o),o="",!1!==e.createErrors?(o+=" { keyword: '"+(E||"false schema")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(p)+" , params: {} ",!1!==e.opts.messages&&(o+=" , message: 'boolean schema is false' "),e.opts.verbose&&(o+=" , schema: false , parentSchema: validate.schema"+e.schemaPath+" , data: "+v+" "),o+=" } "):o+=" {} ";var g=o;o=Y.pop(),o+=!e.compositeRule&&m?e.async?" throw new ValidationError(["+g+"]); ":" validate.errors = ["+g+"]; return false; ":" var err = "+g+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}else o+=e.isTop?i?" return data; ":" validate.errors = null; return true; ":" var "+y+" = true; ";return e.isTop&&(o+=" }); return validate; "),o}if(e.isTop){var P=e.isTop;u=e.level=0,c=e.dataLevel=0,v="data";e.rootId=e.resolve.fullPath(e.self._getId(e.root.schema)),e.baseId=e.baseId||e.rootId,delete e.isTop,e.dataPathArr=[void 0],o+=" var vErrors = null; ",o+=" var errors = 0;     ",o+=" if (rootData === undefined) rootData = data; "}else{u=e.level,v="data"+((c=e.dataLevel)||"");if(l&&(e.baseId=e.resolve.url(e.baseId,l)),i&&!e.async)throw new Error("async schema in sync schema");o+=" var errs_"+u+" = errors;"}y="valid"+u,m=!e.opts.allErrors;var E,w="",b="",S=e.schema.type,j=Array.isArray(S);if(j&&1==S.length&&(S=S[0],j=!1),e.schema.$ref&&n){if("fail"==e.opts.extendRefs)throw new Error('$ref: validation keywords used in schema at path "'+e.errSchemaPath+'" (see option extendRefs)');!0!==e.opts.extendRefs&&(n=!1,e.logger.warn('$ref: keywords ignored in schema at path "'+e.errSchemaPath+'"'))}if(S){if(e.opts.coerceTypes)var _=e.util.coerceToTypes(e.opts.coerceTypes,S);var x=e.RULES.types[S];if(_||j||!0===x||x&&!a(x)){f=e.schemaPath+".type",p=e.errSchemaPath+"/type",f=e.schemaPath+".type",p=e.errSchemaPath+"/type";if(o+=" if ("+e.util[j?"checkDataTypes":"checkDataType"](S,v,!0)+") { ",_){var F="dataType"+u,$="coerced"+u;o+=" var "+F+" = typeof "+v+"; ","array"==e.opts.coerceTypes&&(o+=" if ("+F+" == 'object' && Array.isArray("+v+")) "+F+" = 'array'; "),o+=" var "+$+" = undefined; ";var R="",O=_;if(O)for(var D,I=-1,A=O.length-1;I<A;)D=O[I+=1],I&&(o+=" if ("+$+" === undefined) { ",R+="}"),"array"==e.opts.coerceTypes&&"array"!=D&&(o+=" if ("+F+" == 'array' && "+v+".length == 1) { "+$+" = "+v+" = "+v+"[0]; "+F+" = typeof "+v+";  } "),"string"==D?o+=" if ("+F+" == 'number' || "+F+" == 'boolean') "+$+" = '' + "+v+"; else if ("+v+" === null) "+$+" = ''; ":"number"==D||"integer"==D?(o+=" if ("+F+" == 'boolean' || "+v+" === null || ("+F+" == 'string' && "+v+" && "+v+" == +"+v+" ","integer"==D&&(o+=" && !("+v+" % 1)"),o+=")) "+$+" = +"+v+"; "):"boolean"==D?o+=" if ("+v+" === 'false' || "+v+" === 0 || "+v+" === null) "+$+" = false; else if ("+v+" === 'true' || "+v+" === 1) "+$+" = true; ":"null"==D?o+=" if ("+v+" === '' || "+v+" === 0 || "+v+" === false) "+$+" = null; ":"array"==e.opts.coerceTypes&&"array"==D&&(o+=" if ("+F+" == 'string' || "+F+" == 'number' || "+F+" == 'boolean' || "+v+" == null) "+$+" = ["+v+"]; ");o+=" "+R+" if ("+$+" === undefined) {   ";(Y=Y||[]).push(o),o="",!1!==e.createErrors?(o+=" { keyword: '"+(E||"type")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(p)+" , params: { type: '",o+=j?""+S.join(","):""+S,o+="' } ",!1!==e.opts.messages&&(o+=" , message: 'should be ",o+=j?""+S.join(","):""+S,o+="' "),e.opts.verbose&&(o+=" , schema: validate.schema"+f+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+v+" "),o+=" } "):o+=" {} ";g=o;o=Y.pop(),o+=!e.compositeRule&&m?e.async?" throw new ValidationError(["+g+"]); ":" validate.errors = ["+g+"]; return false; ":" var err = "+g+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",o+=" } else {  ";var k=c?"data"+(c-1||""):"parentData";o+=" "+v+" = "+$+"; ",c||(o+="if ("+k+" !== undefined)"),o+=" "+k+"["+(c?e.dataPathArr[c]:"parentDataProperty")+"] = "+$+"; } "}else{(Y=Y||[]).push(o),o="",!1!==e.createErrors?(o+=" { keyword: '"+(E||"type")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(p)+" , params: { type: '",o+=j?""+S.join(","):""+S,o+="' } ",!1!==e.opts.messages&&(o+=" , message: 'should be ",o+=j?""+S.join(","):""+S,o+="' "),e.opts.verbose&&(o+=" , schema: validate.schema"+f+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+v+" "),o+=" } "):o+=" {} ";g=o;o=Y.pop(),o+=!e.compositeRule&&m?e.async?" throw new ValidationError(["+g+"]); ":" validate.errors = ["+g+"]; return false; ":" var err = "+g+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; "}o+=" } "}}if(e.schema.$ref&&!n)o+=" "+e.RULES.all.$ref.code(e,"$ref")+" ",m&&(o+=" } if (errors === ",o+=P?"0":"errs_"+u,o+=") { ",b+="}");else{e.opts.v5&&e.schema.patternGroups&&e.logger.warn('keyword "patternGroups" is deprecated and disabled. Use option patternGroups: true to enable.');var q=e.RULES;if(q)for(var L=-1,z=q.length-1;L<z;)if(x=q[L+=1],a(x)){if(x.type&&(o+=" if ("+e.util.checkDataType(x.type,v)+") { "),e.opts.useDefaults&&!e.compositeRule)if("object"==x.type&&e.schema.properties){d=e.schema.properties;var C=Object.keys(d);if(C)for(var Q,U=-1,V=C.length-1;U<V;){if(void 0!==(T=d[Q=C[U+=1]]).default){o+="  if ("+(H=v+e.util.getProperty(Q))+" === undefined) "+H+" = ",o+="shared"==e.opts.useDefaults?" "+e.useDefault(T.default)+" ":" "+JSON.stringify(T.default)+" ",o+="; "}}}else if("array"==x.type&&Array.isArray(e.schema.items)){var N=e.schema.items;if(N){I=-1;for(var T,M=N.length-1;I<M;)if(void 0!==(T=N[I+=1]).default){var H;o+="  if ("+(H=v+"["+I+"]")+" === undefined) "+H+" = ",o+="shared"==e.opts.useDefaults?" "+e.useDefault(T.default)+" ":" "+JSON.stringify(T.default)+" ",o+="; "}}}var K=x.rules;if(K)for(var B,G=-1,J=K.length-1;G<J;)if(B=K[G+=1],s(B)){var Z=B.code(e,B.keyword,x.type);Z&&(o+=" "+Z+" ",m&&(w+="}"))}if(m&&(o+=" "+w+" ",w=""),x.type&&(o+=" } ",S&&S===x.type&&!_)){o+=" else { ";var Y;f=e.schemaPath+".type",p=e.errSchemaPath+"/type";(Y=Y||[]).push(o),o="",!1!==e.createErrors?(o+=" { keyword: '"+(E||"type")+"' , dataPath: (dataPath || '') + "+e.errorPath+" , schemaPath: "+e.util.toQuotedString(p)+" , params: { type: '",o+=j?""+S.join(","):""+S,o+="' } ",!1!==e.opts.messages&&(o+=" , message: 'should be ",o+=j?""+S.join(","):""+S,o+="' "),e.opts.verbose&&(o+=" , schema: validate.schema"+f+" , parentSchema: validate.schema"+e.schemaPath+" , data: "+v+" "),o+=" } "):o+=" {} ";g=o;o=Y.pop(),o+=!e.compositeRule&&m?e.async?" throw new ValidationError(["+g+"]); ":" validate.errors = ["+g+"]; return false; ":" var err = "+g+";  if (vErrors === null) vErrors = [err]; else vErrors.push(err); errors++; ",o+=" } "}m&&(o+=" if (errors === ",o+=P?"0":"errs_"+u,o+=") { ",b+="}")}}return m&&(o+=" "+b+" "),P?(i?(o+=" if (errors === 0) return data;           ",o+=" else throw new ValidationError(vErrors); "):(o+=" validate.errors = vErrors; ",o+=" return errors === 0;       "),o+=" }); return validate;"):o+=" var "+y+" = errors === errs_"+u+";",o=e.util.cleanUpCode(o),P&&(o=e.util.finalCleanUpCode(o,i)),o}},{}],36:[function(e,r,t){"use strict";var a=/^[a-z_$][a-z0-9_$-]*$/i,s=e("./dotjs/custom");r.exports={add:function(e,r){function t(e,r,t){for(var a,o=0;o<i.length;o++){var n=i[o];if(n.type==r){a=n;break}}a||i.push(a={type:r,rules:[]});var l={keyword:e,definition:t,custom:!0,code:s,implements:t.implements};a.rules.push(l),i.custom[e]=l}function o(e){if(!i.types[e])throw new Error("Unknown type "+e)}var i=this.RULES;if(i.keywords[e])throw new Error("Keyword "+e+" is already defined");if(!a.test(e))throw new Error("Keyword "+e+" is not a valid identifier");if(r){if(r.macro&&void 0!==r.valid)throw new Error('"valid" option cannot be used with macro keywords');var n=r.type;if(Array.isArray(n)){var l,h=n.length;for(l=0;l<h;l++)o(n[l]);for(l=0;l<h;l++)t(e,n[l],r)}else n&&o(n),t(e,n,r);var u=!0===r.$data&&this._opts.$data;if(u&&!r.validate)throw new Error('$data support: "validate" function is not defined');var c=r.metaSchema;c&&(u&&(c={anyOf:[c,{$ref:"https://raw.githubusercontent.com/epoberezkin/ajv/master/lib/refs/$data.json#"}]}),r.validateSchema=this.compile(c,!0))}return i.keywords[e]=i.all[e]=!0,this},get:function(e){var r=this.RULES.custom[e];return r?r.definition:this.RULES.keywords[e]||!1},remove:function(e){var r=this.RULES;delete r.keywords[e],delete r.all[e],delete r.custom[e];for(var t=0;t<r.length;t++)for(var a=r[t].rules,s=0;s<a.length;s++)if(a[s].keyword==e){a.splice(s,1);break}return this}}},{"./dotjs/custom":21}],37:[function(e,r,t){"use strict";var a="http://json-schema.org/draft-06/schema";r.exports=function(e){var r=e._opts.defaultMeta,t="string"==typeof r?{$ref:r}:e.getSchema(a)?{$ref:a}:{};e.addKeyword("patternGroups",{metaSchema:{type:"object",additionalProperties:{type:"object",required:["schema"],properties:{maximum:{type:"integer",minimum:0},minimum:{type:"integer",minimum:0},schema:t},additionalProperties:!1}}}),e.RULES.all.properties.implements.push("patternGroups")}},{}],38:[function(e,r,t){r.exports={$schema:"http://json-schema.org/draft-06/schema#",$id:"https://raw.githubusercontent.com/epoberezkin/ajv/master/lib/refs/$data.json#",description:"Meta-schema for $data reference (JSON-schema extension proposal)",type:"object",required:["$data"],properties:{$data:{type:"string",anyOf:[{format:"relative-json-pointer"},{format:"json-pointer"}]}},additionalProperties:!1}},{}],39:[function(e,r,t){r.exports={$schema:"http://json-schema.org/draft-06/schema#",$id:"http://json-schema.org/draft-06/schema#",title:"Core schema meta-schema",definitions:{schemaArray:{type:"array",minItems:1,items:{$ref:"#"}},nonNegativeInteger:{type:"integer",minimum:0},nonNegativeIntegerDefault0:{allOf:[{$ref:"#/definitions/nonNegativeInteger"},{default:0}]},simpleTypes:{enum:["array","boolean","integer","null","number","object","string"]},stringArray:{type:"array",items:{type:"string"},uniqueItems:!0,default:[]}},type:["object","boolean"],properties:{$id:{type:"string",format:"uri-reference"},$schema:{type:"string",format:"uri"},$ref:{type:"string",format:"uri-reference"},title:{type:"string"},description:{type:"string"},default:{},examples:{type:"array",items:{}},multipleOf:{type:"number",exclusiveMinimum:0},maximum:{type:"number"},exclusiveMaximum:{type:"number"},minimum:{type:"number"},exclusiveMinimum:{type:"number"},maxLength:{$ref:"#/definitions/nonNegativeInteger"},minLength:{$ref:"#/definitions/nonNegativeIntegerDefault0"},pattern:{type:"string",format:"regex"},additionalItems:{$ref:"#"},items:{anyOf:[{$ref:"#"},{$ref:"#/definitions/schemaArray"}],default:{}},maxItems:{$ref:"#/definitions/nonNegativeInteger"},minItems:{$ref:"#/definitions/nonNegativeIntegerDefault0"},uniqueItems:{type:"boolean",default:!1},contains:{$ref:"#"},maxProperties:{$ref:"#/definitions/nonNegativeInteger"},minProperties:{$ref:"#/definitions/nonNegativeIntegerDefault0"},required:{$ref:"#/definitions/stringArray"},additionalProperties:{$ref:"#"},definitions:{type:"object",additionalProperties:{$ref:"#"},default:{}},properties:{type:"object",additionalProperties:{$ref:"#"},default:{}},patternProperties:{type:"object",additionalProperties:{$ref:"#"},default:{}},dependencies:{type:"object",additionalProperties:{anyOf:[{$ref:"#"},{$ref:"#/definitions/stringArray"}]}},propertyNames:{$ref:"#"},const:{},enum:{type:"array",minItems:1,uniqueItems:!0},type:{anyOf:[{$ref:"#/definitions/simpleTypes"},{type:"array",items:{$ref:"#/definitions/simpleTypes"},minItems:1,uniqueItems:!0}]},format:{type:"string"},allOf:{$ref:"#/definitions/schemaArray"},anyOf:{$ref:"#/definitions/schemaArray"},oneOf:{$ref:"#/definitions/schemaArray"},not:{$ref:"#"}},default:{}}},{}],40:[function(e,r,t){function a(e){var r=this,t=n.call(arguments,1);return new Promise(function(a,i){function n(r){var t;try{t=e.next(r)}catch(e){return i(e)}h(t)}function l(r){var t;try{t=e.throw(r)}catch(e){return i(e)}h(t)}function h(e){if(e.done)return a(e.value);var t=s.call(r,e.value);return t&&o(t)?t.then(n,l):l(new TypeError('You may only yield a function, promise, generator, array, or object, but the following object was passed: "'+String(e.value)+'"'))}if("function"==typeof e&&(e=e.apply(r,t)),!e||"function"!=typeof e.next)return a(e);n()})}function s(e){return e?o(e)?e:function(e){var r=e.constructor;return!!r&&("GeneratorFunction"===r.name||"GeneratorFunction"===r.displayName||i(r.prototype))}(e)||i(e)?a.call(this,e):"function"==typeof e?function(e){var r=this;return new Promise(function(t,a){e.call(r,function(e,r){if(e)return a(e);arguments.length>2&&(r=n.call(arguments,1)),t(r)})})}.call(this,e):Array.isArray(e)?function(e){return Promise.all(e.map(s,this))}.call(this,e):function(e){return Object==e.constructor}(e)?function(e){for(var r=new e.constructor,t=Object.keys(e),a=[],i=0;i<t.length;i++){var n=t[i],l=s.call(this,e[n]);l&&o(l)?function(e,t){r[t]=void 0,a.push(e.then(function(e){r[t]=e}))}(l,n):r[n]=e[n]}return Promise.all(a).then(function(){return r})}.call(this,e):e:e}function o(e){return"function"==typeof e.then}function i(e){return"function"==typeof e.next&&"function"==typeof e.throw}var n=Array.prototype.slice;r.exports=a.default=a.co=a,a.wrap=function(e){function r(){return a.call(this,e.apply(this,arguments))}return r.__generatorFunction__=e,r}},{}],41:[function(e,r,t){"use strict";r.exports=function e(r,t){if(r===t)return!0;var a,s=Array.isArray(r),o=Array.isArray(t);if(s&&o){if(r.length!=t.length)return!1;for(a=0;a<r.length;a++)if(!e(r[a],t[a]))return!1;return!0}if(s!=o)return!1;if(r&&t&&"object"==typeof r&&"object"==typeof t){var i=Object.keys(r);if(i.length!==Object.keys(t).length)return!1;var n=r instanceof Date,l=t instanceof Date;if(n&&l)return r.getTime()==t.getTime();if(n!=l)return!1;var h=r instanceof RegExp,u=t instanceof RegExp;if(h&&u)return r.toString()==t.toString();if(h!=u)return!1;for(a=0;a<i.length;a++)if(!Object.prototype.hasOwnProperty.call(t,i[a]))return!1;for(a=0;a<i.length;a++)if(!e(r[i[a]],t[i[a]]))return!1;return!0}return!1}},{}],42:[function(e,r,t){"use strict";r.exports=function(e,r){r||(r={}),"function"==typeof r&&(r={cmp:r});var t="boolean"==typeof r.cycles&&r.cycles,a=r.cmp&&function(e){return function(r){return function(t,a){return e({key:t,value:r[t]},{key:a,value:r[a]})}}}(r.cmp),s=[];return function e(r){if(r&&r.toJSON&&"function"==typeof r.toJSON&&(r=r.toJSON()),void 0!==r){if("number"==typeof r)return isFinite(r)?""+r:"null";if("object"!=typeof r)return JSON.stringify(r);var o,i;if(Array.isArray(r)){for(i="[",o=0;o<r.length;o++)o&&(i+=","),i+=e(r[o])||"null";return i+"]"}if(null===r)return"null";if(-1!==s.indexOf(r)){if(t)return JSON.stringify("__cycle__");throw new TypeError("Converting circular structure to JSON")}var n=s.push(r)-1,l=Object.keys(r).sort(a&&a(r));for(i="",o=0;o<l.length;o++){var h=l[o],u=e(r[h]);u&&(i&&(i+=","),i+=JSON.stringify(h)+":"+u)}return s.splice(n,1),"{"+i+"}"}}(e)}},{}],43:[function(e,r,t){"use strict";function a(e,r,t,o,i,n,l,h,u){if(t&&"object"==typeof t&&!Array.isArray(t)){r(t,o,i,n,l,h,u);for(var c in t){var d=t[c];if(Array.isArray(d)){if(c in s.arrayKeywords)for(var f=0;f<d.length;f++)a(e,r,d[f],o+"/"+c+"/"+f,i,o,c,t,f)}else if(c in s.propsKeywords){if(d&&"object"==typeof d)for(var p in d)a(e,r,d[p],o+"/"+c+"/"+function(e){return e.replace(/~/g,"~0").replace(/\//g,"~1")}(p),i,o,c,t,p)}else(c in s.keywords||e.allKeys&&!(c in s.skipKeywords))&&a(e,r,d,o+"/"+c,i,o,c,t)}}}var s=r.exports=function(e,r,t){"function"==typeof r&&(t=r,r={}),a(r,t,e,"",e)};s.keywords={additionalItems:!0,items:!0,contains:!0,additionalProperties:!0,propertyNames:!0,not:!0},s.arrayKeywords={items:!0,allOf:!0,anyOf:!0,oneOf:!0},s.propsKeywords={definitions:!0,properties:!0,patternProperties:!0,dependencies:!0},s.skipKeywords={enum:!0,const:!0,required:!0,maximum:!0,minimum:!0,exclusiveMaximum:!0,exclusiveMinimum:!0,multipleOf:!0,maxLength:!0,minLength:!0,pattern:!0,format:!0,maxItems:!0,minItems:!0,uniqueItems:!0,maxProperties:!0,minProperties:!0}},{}],44:[function(e,r,t){(function(e){!function(a){function s(e){throw new RangeError(D[e])}function o(e,r){for(var t=e.length,a=[];t--;)a[t]=r(e[t]);return a}function i(e,r){var t=e.split("@"),a="";t.length>1&&(a=t[0]+"@",e=t[1]);return a+o((e=e.replace(O,".")).split("."),r).join(".")}function n(e){for(var r,t,a=[],s=0,o=e.length;s<o;)(r=e.charCodeAt(s++))>=55296&&r<=56319&&s<o?56320==(64512&(t=e.charCodeAt(s++)))?a.push(((1023&r)<<10)+(1023&t)+65536):(a.push(r),s--):a.push(r);return a}function l(e){return o(e,function(e){var r="";return e>65535&&(r+=k((e-=65536)>>>10&1023|55296),e=56320|1023&e),r+=k(e)}).join("")}function h(e){return e-48<10?e-22:e-65<26?e-65:e-97<26?e-97:E}function u(e,r){return e+22+75*(e<26)-((0!=r)<<5)}function c(e,r,t){var a=0;for(e=t?A(e/j):e>>1,e+=A(e/r);e>I*b>>1;a+=E)e=A(e/I);return A(a+(I+1)*e/(e+S))}function d(e){var r,t,a,o,i,n,u,d,f,p,m=[],v=e.length,y=0,g=x,S=_;for((t=e.lastIndexOf(F))<0&&(t=0),a=0;a<t;++a)e.charCodeAt(a)>=128&&s("not-basic"),m.push(e.charCodeAt(a));for(o=t>0?t+1:0;o<v;){for(i=y,n=1,u=E;o>=v&&s("invalid-input"),((d=h(e.charCodeAt(o++)))>=E||d>A((P-y)/n))&&s("overflow"),y+=d*n,f=u<=S?w:u>=S+b?b:u-S,!(d<f);u+=E)n>A(P/(p=E-f))&&s("overflow"),n*=p;S=c(y-i,r=m.length+1,0==i),A(y/r)>P-g&&s("overflow"),g+=A(y/r),y%=r,m.splice(y++,0,g)}return l(m)}function f(e){var r,t,a,o,i,l,h,d,f,p,m,v,y,g,S,j=[];for(v=(e=n(e)).length,r=x,t=0,i=_,l=0;l<v;++l)(m=e[l])<128&&j.push(k(m));for(a=o=j.length,o&&j.push(F);a<v;){for(h=P,l=0;l<v;++l)(m=e[l])>=r&&m<h&&(h=m);for(h-r>A((P-t)/(y=a+1))&&s("overflow"),t+=(h-r)*y,r=h,l=0;l<v;++l)if((m=e[l])<r&&++t>P&&s("overflow"),m==r){for(d=t,f=E;p=f<=i?w:f>=i+b?b:f-i,!(d<p);f+=E)j.push(k(u(p+(S=d-p)%(g=E-p),0))),d=A(S/g);j.push(k(u(d,0))),i=c(t,y,a==o),t=0,++a}++t,++r}return j.join("")}var p="object"==typeof t&&t&&!t.nodeType&&t,m="object"==typeof r&&r&&!r.nodeType&&r,v="object"==typeof e&&e;v.global!==v&&v.window!==v&&v.self!==v||(a=v);var y,g,P=2147483647,E=36,w=1,b=26,S=38,j=700,_=72,x=128,F="-",$=/^xn--/,R=/[^\x20-\x7E]/,O=/[\x2E\u3002\uFF0E\uFF61]/g,D={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},I=E-w,A=Math.floor,k=String.fromCharCode;if(y={version:"1.4.1",ucs2:{decode:n,encode:l},decode:d,encode:f,toASCII:function(e){return i(e,function(e){return R.test(e)?"xn--"+f(e):e})},toUnicode:function(e){return i(e,function(e){return $.test(e)?d(e.slice(4).toLowerCase()):e})}},p&&m)if(r.exports==p)m.exports=y;else for(g in y)y.hasOwnProperty(g)&&(p[g]=y[g]);else a.punycode=y}(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],45:[function(e,r,t){"use strict";function a(e,r){return Object.prototype.hasOwnProperty.call(e,r)}r.exports=function(e,r,t,o){r=r||"&",t=t||"=";var i={};if("string"!=typeof e||0===e.length)return i;var n=/\+/g;e=e.split(r);var l=1e3;o&&"number"==typeof o.maxKeys&&(l=o.maxKeys);var h=e.length;l>0&&h>l&&(h=l);for(var u=0;u<h;++u){var c,d,f,p,m=e[u].replace(n,"%20"),v=m.indexOf(t);v>=0?(c=m.substr(0,v),d=m.substr(v+1)):(c=m,d=""),f=decodeURIComponent(c),p=decodeURIComponent(d),a(i,f)?s(i[f])?i[f].push(p):i[f]=[i[f],p]:i[f]=p}return i};var s=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)}},{}],46:[function(e,r,t){"use strict";function a(e,r){if(e.map)return e.map(r);for(var t=[],a=0;a<e.length;a++)t.push(r(e[a],a));return t}var s=function(e){switch(typeof e){case"string":return e;case"boolean":return e?"true":"false";case"number":return isFinite(e)?e:"";default:return""}};r.exports=function(e,r,t,n){return r=r||"&",t=t||"=",null===e&&(e=void 0),"object"==typeof e?a(i(e),function(i){var n=encodeURIComponent(s(i))+t;return o(e[i])?a(e[i],function(e){return n+encodeURIComponent(s(e))}).join(r):n+encodeURIComponent(s(e[i]))}).join(r):n?encodeURIComponent(s(n))+t+encodeURIComponent(s(e)):""};var o=Array.isArray||function(e){return"[object Array]"===Object.prototype.toString.call(e)},i=Object.keys||function(e){var r=[];for(var t in e)Object.prototype.hasOwnProperty.call(e,t)&&r.push(t);return r}},{}],47:[function(e,r,t){"use strict";t.decode=t.parse=e("./decode"),t.encode=t.stringify=e("./encode")},{"./decode":45,"./encode":46}],48:[function(e,r,t){"use strict";function a(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}function s(e,r,t){if(e&&i.isObject(e)&&e instanceof a)return e;var s=new a;return s.parse(e,r,t),s}var o=e("punycode"),i=e("./util");t.parse=s,t.resolve=function(e,r){return s(e,!1,!0).resolve(r)},t.resolveObject=function(e,r){return e?s(e,!1,!0).resolveObject(r):r},t.format=function(e){return i.isString(e)&&(e=s(e)),e instanceof a?e.format():a.prototype.format.call(e)},t.Url=a;var n=/^([a-z0-9.+-]+:)/i,l=/:[0-9]*$/,h=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,u=["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r","\n","\t"]),c=["'"].concat(u),d=["%","/","?",";","#"].concat(c),f=["/","?","#"],p=/^[+a-z0-9A-Z_-]{0,63}$/,m=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,v={javascript:!0,"javascript:":!0},y={javascript:!0,"javascript:":!0},g={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0},P=e("querystring");a.prototype.parse=function(e,r,t){if(!i.isString(e))throw new TypeError("Parameter 'url' must be a string, not "+typeof e);var a=e.indexOf("?"),s=-1!==a&&a<e.indexOf("#")?"?":"#",l=e.split(s);l[0]=l[0].replace(/\\/g,"/");var u=e=l.join(s);if(u=u.trim(),!t&&1===e.split("#").length){var E=h.exec(u);if(E)return this.path=u,this.href=u,this.pathname=E[1],E[2]?(this.search=E[2],this.query=r?P.parse(this.search.substr(1)):this.search.substr(1)):r&&(this.search="",this.query={}),this}var w=n.exec(u);if(w){var b=(w=w[0]).toLowerCase();this.protocol=b,u=u.substr(w.length)}if(t||w||u.match(/^\/\/[^@\/]+@[^@\/]+/)){var S="//"===u.substr(0,2);!S||w&&y[w]||(u=u.substr(2),this.slashes=!0)}if(!y[w]&&(S||w&&!g[w])){for(var j=-1,_=0;_<f.length;_++){-1!==($=u.indexOf(f[_]))&&(-1===j||$<j)&&(j=$)}var x,F;-1!==(F=-1===j?u.lastIndexOf("@"):u.lastIndexOf("@",j))&&(x=u.slice(0,F),u=u.slice(F+1),this.auth=decodeURIComponent(x)),j=-1;for(_=0;_<d.length;_++){var $;-1!==($=u.indexOf(d[_]))&&(-1===j||$<j)&&(j=$)}-1===j&&(j=u.length),this.host=u.slice(0,j),u=u.slice(j),this.parseHost(),this.hostname=this.hostname||"";var R="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!R)for(var O=this.hostname.split(/\./),D=(_=0,O.length);_<D;_++){var I=O[_];if(I&&!I.match(p)){for(var A="",k=0,q=I.length;k<q;k++)I.charCodeAt(k)>127?A+="x":A+=I[k];if(!A.match(p)){var L=O.slice(0,_),z=O.slice(_+1),C=I.match(m);C&&(L.push(C[1]),z.unshift(C[2])),z.length&&(u="/"+z.join(".")+u),this.hostname=L.join(".");break}}}this.hostname=this.hostname.length>255?"":this.hostname.toLowerCase(),R||(this.hostname=o.toASCII(this.hostname));var Q=this.port?":"+this.port:"";this.host=(this.hostname||"")+Q,this.href+=this.host,R&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),"/"!==u[0]&&(u="/"+u))}if(!v[b])for(_=0,D=c.length;_<D;_++){var U=c[_];if(-1!==u.indexOf(U)){var V=encodeURIComponent(U);V===U&&(V=escape(U)),u=u.split(U).join(V)}}var N=u.indexOf("#");-1!==N&&(this.hash=u.substr(N),u=u.slice(0,N));var T=u.indexOf("?");if(-1!==T?(this.search=u.substr(T),this.query=u.substr(T+1),r&&(this.query=P.parse(this.query)),u=u.slice(0,T)):r&&(this.search="",this.query={}),u&&(this.pathname=u),g[b]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){this.path=(Q=this.pathname||"")+(this.search||"")}return this.href=this.format(),this},a.prototype.format=function(){var e=this.auth||"";e&&(e=(e=encodeURIComponent(e)).replace(/%3A/i,":"),e+="@");var r=this.protocol||"",t=this.pathname||"",a=this.hash||"",s=!1,o="";this.host?s=e+this.host:this.hostname&&(s=e+(-1===this.hostname.indexOf(":")?this.hostname:"["+this.hostname+"]"),this.port&&(s+=":"+this.port)),this.query&&i.isObject(this.query)&&Object.keys(this.query).length&&(o=P.stringify(this.query));var n=this.search||o&&"?"+o||"";return r&&":"!==r.substr(-1)&&(r+=":"),this.slashes||(!r||g[r])&&!1!==s?(s="//"+(s||""),t&&"/"!==t.charAt(0)&&(t="/"+t)):s||(s=""),a&&"#"!==a.charAt(0)&&(a="#"+a),n&&"?"!==n.charAt(0)&&(n="?"+n),t=t.replace(/[?#]/g,function(e){return encodeURIComponent(e)}),n=n.replace("#","%23"),r+s+t+n+a},a.prototype.resolve=function(e){return this.resolveObject(s(e,!1,!0)).format()},a.prototype.resolveObject=function(e){if(i.isString(e)){var r=new a;r.parse(e,!1,!0),e=r}for(var t=new a,s=Object.keys(this),o=0;o<s.length;o++){var n=s[o];t[n]=this[n]}if(t.hash=e.hash,""===e.href)return t.href=t.format(),t;if(e.slashes&&!e.protocol){for(var l=Object.keys(e),h=0;h<l.length;h++){var u=l[h];"protocol"!==u&&(t[u]=e[u])}return g[t.protocol]&&t.hostname&&!t.pathname&&(t.path=t.pathname="/"),t.href=t.format(),t}if(e.protocol&&e.protocol!==t.protocol){if(!g[e.protocol]){for(var c=Object.keys(e),d=0;d<c.length;d++){var f=c[d];t[f]=e[f]}return t.href=t.format(),t}if(t.protocol=e.protocol,e.host||y[e.protocol])t.pathname=e.pathname;else{for(var p=(e.pathname||"").split("/");p.length&&!(e.host=p.shift()););e.host||(e.host=""),e.hostname||(e.hostname=""),""!==p[0]&&p.unshift(""),p.length<2&&p.unshift(""),t.pathname=p.join("/")}if(t.search=e.search,t.query=e.query,t.host=e.host||"",t.auth=e.auth,t.hostname=e.hostname||e.host,t.port=e.port,t.pathname||t.search){t.path=(t.pathname||"")+(t.search||"")}return t.slashes=t.slashes||e.slashes,t.href=t.format(),t}var m=t.pathname&&"/"===t.pathname.charAt(0),v=e.host||e.pathname&&"/"===e.pathname.charAt(0),P=v||m||t.host&&e.pathname,E=P,w=t.pathname&&t.pathname.split("/")||[],b=(p=e.pathname&&e.pathname.split("/")||[],t.protocol&&!g[t.protocol]);if(b&&(t.hostname="",t.port=null,t.host&&(""===w[0]?w[0]=t.host:w.unshift(t.host)),t.host="",e.protocol&&(e.hostname=null,e.port=null,e.host&&(""===p[0]?p[0]=e.host:p.unshift(e.host)),e.host=null),P=P&&(""===p[0]||""===w[0])),v)t.host=e.host||""===e.host?e.host:t.host,t.hostname=e.hostname||""===e.hostname?e.hostname:t.hostname,t.search=e.search,t.query=e.query,w=p;else if(p.length)w||(w=[]),w.pop(),w=w.concat(p),t.search=e.search,t.query=e.query;else if(!i.isNullOrUndefined(e.search)){if(b){t.hostname=t.host=w.shift();($=!!(t.host&&t.host.indexOf("@")>0)&&t.host.split("@"))&&(t.auth=$.shift(),t.host=t.hostname=$.shift())}return t.search=e.search,t.query=e.query,i.isNull(t.pathname)&&i.isNull(t.search)||(t.path=(t.pathname?t.pathname:"")+(t.search?t.search:"")),t.href=t.format(),t}if(!w.length)return t.pathname=null,t.path=t.search?"/"+t.search:null,t.href=t.format(),t;for(var S=w.slice(-1)[0],j=(t.host||e.host||w.length>1)&&("."===S||".."===S)||""===S,_=0,x=w.length;x>=0;x--)"."===(S=w[x])?w.splice(x,1):".."===S?(w.splice(x,1),_++):_&&(w.splice(x,1),_--);if(!P&&!E)for(;_--;_)w.unshift("..");!P||""===w[0]||w[0]&&"/"===w[0].charAt(0)||w.unshift(""),j&&"/"!==w.join("/").substr(-1)&&w.push("");var F=""===w[0]||w[0]&&"/"===w[0].charAt(0);if(b){t.hostname=t.host=F?"":w.length?w.shift():"";var $;($=!!(t.host&&t.host.indexOf("@")>0)&&t.host.split("@"))&&(t.auth=$.shift(),t.host=t.hostname=$.shift())}return(P=P||t.host&&w.length)&&!F&&w.unshift(""),w.length?t.pathname=w.join("/"):(t.pathname=null,t.path=null),i.isNull(t.pathname)&&i.isNull(t.search)||(t.path=(t.pathname?t.pathname:"")+(t.search?t.search:"")),t.auth=e.auth||t.auth,t.slashes=t.slashes||e.slashes,t.href=t.format(),t},a.prototype.parseHost=function(){var e=this.host,r=l.exec(e);r&&(":"!==(r=r[0])&&(this.port=r.substr(1)),e=e.substr(0,e.length-r.length)),e&&(this.hostname=e)}},{"./util":49,punycode:44,querystring:47}],49:[function(e,r,t){"use strict";r.exports={isString:function(e){return"string"==typeof e},isObject:function(e){return"object"==typeof e&&null!==e},isNull:function(e){return null===e},isNullOrUndefined:function(e){return null==e}}},{}],ajv:[function(e,r,t){"use strict";function a(r){if(!(this instanceof a))return new a(r);r=this._opts=E.copy(r)||{},function(e){var r=e._opts.logger;if(!1===r)e.logger={log:u,warn:u,error:u};else{if(void 0===r&&(r=console),!("object"==typeof r&&r.log&&r.warn&&r.error))throw new Error("logger must implement log, warn and error methods");e.logger=r}}(this),this._schemas={},this._refs={},this._fragments={},this._formats=v(r.format);var t=this._schemaUriFormat=this._formats["uri-reference"];this._schemaUriFormatFunc=function(e){return t.test(e)},this._cache=r.cache||new f,this._loadingSchemas={},this._compilations=[],this.RULES=y(),this._getId=function(e){switch(e.schemaId){case"$id":return n;case"id":return i;default:return l}}(r),r.loopRequired=r.loopRequired||1/0,"property"==r.errorDataPath&&(r._errorDataPathProperty=!0),void 0===r.serialize&&(r.serialize=m),this._metaOpts=function(e){for(var r=E.copy(e._opts),t=0;t<_.length;t++)delete r[_[t]];return r}(this),r.formats&&function(e){for(var r in e._opts.formats){var t=e._opts.formats[r];e.addFormat(r,t)}}(this),function(r){var t;r._opts.$data&&(t=e("./refs/$data.json"),r.addMetaSchema(t,t.$id,!0));if(!1===r._opts.meta)return;var a=e("./refs/json-schema-draft-06.json");r._opts.$data&&(a=g(a,x));r.addMetaSchema(a,j,!0),r._refs["http://json-schema.org/schema"]=j}(this),"object"==typeof r.meta&&this.addMetaSchema(r.meta),function(e){var r=e._opts.schemas;if(!r)return;if(Array.isArray(r))e.addSchema(r);else for(var t in r)e.addSchema(r[t],t)}(this),r.patternGroups&&P(this)}function s(e,r){return r=d.normalizeId(r),e._schemas[r]||e._refs[r]||e._fragments[r]}function o(e,r,t){for(var a in r){var s=r[a];s.meta||t&&!t.test(a)||(e._cache.del(s.cacheKey),delete r[a])}}function i(e){return e.$id&&this.logger.warn("schema $id ignored",e.$id),e.id}function n(e){return e.id&&this.logger.warn("schema id ignored",e.id),e.$id}function l(e){if(e.$id&&e.id&&e.$id!=e.id)throw new Error("schema $id is different from id");return e.$id||e.id}function h(e,r){if(e._schemas[r]||e._refs[r])throw new Error('schema with key or id "'+r+'" already exists')}function u(){}var c=e("./compile"),d=e("./compile/resolve"),f=e("./cache"),p=e("./compile/schema_obj"),m=e("fast-json-stable-stringify"),v=e("./compile/formats"),y=e("./compile/rules"),g=e("./$data"),P=e("./patternGroups"),E=e("./compile/util"),w=e("co");r.exports=a,a.prototype.validate=function(e,r){var t;if("string"==typeof e){if(!(t=this.getSchema(e)))throw new Error('no schema with key or ref "'+e+'"')}else{var a=this._addSchema(e);t=a.validate||this._compile(a)}var s=t(r);return!0===t.$async?"*"==this._opts.async?w(s):s:(this.errors=t.errors,s)},a.prototype.compile=function(e,r){var t=this._addSchema(e,void 0,r);return t.validate||this._compile(t)},a.prototype.addSchema=function(e,r,t,a){if(Array.isArray(e)){for(var s=0;s<e.length;s++)this.addSchema(e[s],void 0,t,a);return this}var o=this._getId(e);if(void 0!==o&&"string"!=typeof o)throw new Error("schema id must be string");return r=d.normalizeId(r||o),h(this,r),this._schemas[r]=this._addSchema(e,t,a,!0),this},a.prototype.addMetaSchema=function(e,r,t){return this.addSchema(e,r,t,!0),this},a.prototype.validateSchema=function(e,r){var t=e.$schema;if(void 0!==t&&"string"!=typeof t)throw new Error("$schema must be a string");if(!(t=t||this._opts.defaultMeta||function(e){var r=e._opts.meta;return e._opts.defaultMeta="object"==typeof r?e._getId(r)||r:e.getSchema(j)?j:void 0,e._opts.defaultMeta}(this)))return this.logger.warn("meta-schema not available"),this.errors=null,!0;var a=this._formats.uri;this._formats.uri="function"==typeof a?this._schemaUriFormatFunc:this._schemaUriFormat;var s;try{s=this.validate(t,e)}finally{this._formats.uri=a}if(!s&&r){var o="schema is invalid: "+this.errorsText();if("log"!=this._opts.validateSchema)throw new Error(o);this.logger.error(o)}return s},a.prototype.getSchema=function(e){var r=s(this,e);switch(typeof r){case"object":return r.validate||this._compile(r);case"string":return this.getSchema(r);case"undefined":return function(e,r){var t=d.schema.call(e,{schema:{}},r);if(t){var a=t.schema,s=t.root,o=t.baseId,i=c.call(e,a,s,void 0,o);return e._fragments[r]=new p({ref:r,fragment:!0,schema:a,root:s,baseId:o,validate:i}),i}}(this,e)}},a.prototype.removeSchema=function(e){if(e instanceof RegExp)return o(this,this._schemas,e),o(this,this._refs,e),this;switch(typeof e){case"undefined":return o(this,this._schemas),o(this,this._refs),this._cache.clear(),this;case"string":var r=s(this,e);return r&&this._cache.del(r.cacheKey),delete this._schemas[e],delete this._refs[e],this;case"object":var t=this._opts.serialize,a=t?t(e):e;this._cache.del(a);var i=this._getId(e);i&&(i=d.normalizeId(i),delete this._schemas[i],delete this._refs[i])}return this},a.prototype.addFormat=function(e,r){return"string"==typeof r&&(r=new RegExp(r)),this._formats[e]=r,this},a.prototype.errorsText=function(e,r){if(!(e=e||this.errors))return"No errors";for(var t=void 0===(r=r||{}).separator?", ":r.separator,a=void 0===r.dataVar?"data":r.dataVar,s="",o=0;o<e.length;o++){var i=e[o];i&&(s+=a+i.dataPath+" "+i.message+t)}return s.slice(0,-t.length)},a.prototype._addSchema=function(e,r,t,a){if("object"!=typeof e&&"boolean"!=typeof e)throw new Error("schema should be object or boolean");var s=this._opts.serialize,o=s?s(e):e,i=this._cache.get(o);if(i)return i;a=a||!1!==this._opts.addUsedSchema;var n=d.normalizeId(this._getId(e));n&&a&&h(this,n);var l,u=!1!==this._opts.validateSchema&&!r;u&&!(l=n&&n==d.normalizeId(e.$schema))&&this.validateSchema(e,!0);var c=d.ids.call(this,e),f=new p({id:n,schema:e,localRefs:c,cacheKey:o,meta:t});return"#"!=n[0]&&a&&(this._refs[n]=f),this._cache.put(o,f),u&&l&&this.validateSchema(e,!0),f},a.prototype._compile=function(e,r){function t(){var r=e.validate,a=r.apply(null,arguments);return t.errors=r.errors,a}if(e.compiling)return e.validate=t,t.schema=e.schema,t.errors=null,t.root=r||t,!0===e.schema.$async&&(t.$async=!0),t;e.compiling=!0;var a;e.meta&&(a=this._opts,this._opts=this._metaOpts);var s;try{s=c.call(this,e.schema,r,e.localRefs)}finally{e.compiling=!1,e.meta&&(this._opts=a)}return e.validate=s,e.refs=s.refs,e.refVal=s.refVal,e.root=s.root,s},a.prototype.compileAsync=e("./compile/async");var b=e("./keyword");a.prototype.addKeyword=b.add,a.prototype.getKeyword=b.get,a.prototype.removeKeyword=b.remove;var S=e("./compile/error_classes");a.ValidationError=S.Validation,a.MissingRefError=S.MissingRef,a.$dataMetaSchema=g;var j="http://json-schema.org/draft-06/schema",_=["removeAdditional","useDefaults","coerceTypes"],x=["/properties"]},{"./$data":1,"./cache":2,"./compile":7,"./compile/async":4,"./compile/error_classes":5,"./compile/formats":6,"./compile/resolve":8,"./compile/rules":9,"./compile/schema_obj":10,"./compile/util":12,"./keyword":36,"./patternGroups":37,"./refs/$data.json":38,"./refs/json-schema-draft-06.json":39,co:40,"fast-json-stable-stringify":42}]},{},[])("ajv")});
//# sourceMappingURL=ajv.min.js.map