{"name": "is-absolute-url", "version": "2.1.0", "description": "Check if an URL is absolute", "license": "MIT", "repository": "sindresorhus/is-absolute-url", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js"], "keywords": ["url", "absolute", "relative", "uri", "is", "check"], "devDependencies": {"mocha": "*"}, "__npminstall_done": true, "_from": "is-absolute-url@2.1.0", "_resolved": "https://registry.npmmirror.com/is-absolute-url/-/is-absolute-url-2.1.0.tgz"}