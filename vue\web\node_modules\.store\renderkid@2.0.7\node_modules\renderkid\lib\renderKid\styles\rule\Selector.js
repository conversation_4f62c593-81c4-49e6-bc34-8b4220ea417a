"use strict";

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

function _defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if ("value" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } }

function _createClass(Constructor, protoProps, staticProps) { if (protoProps) _defineProperties(Constructor.prototype, protoProps); if (staticProps) _defineProperties(Constructor, staticProps); return Constructor; }

// Generated by CoffeeScript 2.5.1
var CSSSelect, Selector;
CSSSelect = require('css-select');

module.exports = Selector = function () {
  var self;

  var Selector = /*#__PURE__*/function () {
    function Selector(text1) {
      _classCallCheck(this, Selector);

      this.text = text1;
      this._fn = CSSSelect.compile(this.text);
      this.priority = self.calculatePriority(this.text);
    }

    _createClass(Selector, [{
      key: "matches",
      value: function matches(elem) {
        return CSSSelect.is(elem, this._fn);
      } // This stupid piece of code is supposed to calculate
      // selector priority, somehow according to
      // http://www.w3.org/wiki/CSS/Training/Priority_level_of_selector

    }], [{
      key: "calculatePriority",
      value: function calculatePriority(text) {
        var n, priotrity;
        priotrity = 0;

        if (n = text.match(/[\#]{1}/g)) {
          priotrity += 100 * n.length;
        }

        if (n = text.match(/[a-zA-Z]+/g)) {
          priotrity += 2 * n.length;
        }

        if (n = text.match(/\*/g)) {
          priotrity += 1 * n.length;
        }

        return priotrity;
      }
    }]);

    return Selector;
  }();

  ;
  self = Selector;
  return Selector;
}.call(void 0);