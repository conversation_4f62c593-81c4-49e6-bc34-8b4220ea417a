{"name": "cuint", "version": "0.2.2", "description": "Unsigned integers for Javascript", "main": "index.js", "scripts": {"test": "mocha", "prepublish": "node build"}, "repository": {"type": "git", "url": "https://github.com/pierrec/js-cuint"}, "keywords": ["C", "unsigned", "integer", "32bits", "64bits"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/pierrec/js-cuint/issues"}, "homepage": "https://github.com/pierrec/js-cuint", "devDependencies": {"minify": "0.2.x", "mocha": "^2.1.0"}, "__npminstall_done": true, "_from": "cuint@0.2.2", "_resolved": "https://registry.npmmirror.com/cuint/-/cuint-0.2.2.tgz"}