{"name": "globals", "version": "9.18.0", "description": "Global identifiers from different JavaScript environments", "license": "MIT", "repository": "sindresorhus/globals", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "mocha"}, "files": ["index.js", "globals.json"], "keywords": ["globals", "global", "identifiers", "variables", "vars", "j<PERSON>t", "eslint", "environments"], "devDependencies": {"mocha": "*"}, "__npminstall_done": true, "_from": "globals@9.18.0", "_resolved": "https://registry.npmmirror.com/globals/-/globals-9.18.0.tgz"}