#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="$basedir/C:/Users/<USER>/Desktop/p/vue/web/node_modules/.store/webpack-cli@6.0.1/node_modules"
else
  export NODE_PATH="$NODE_PATH:$basedir/C:/Users/<USER>/Desktop/p/vue/web/node_modules/.store/webpack-cli@6.0.1/node_modules"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../.store/webpack-cli@6.0.1/node_modules/webpack-cli/bin/cli.js" "$@"
else
  exec node  "$basedir/../.store/webpack-cli@6.0.1/node_modules/webpack-cli/bin/cli.js" "$@"
fi
