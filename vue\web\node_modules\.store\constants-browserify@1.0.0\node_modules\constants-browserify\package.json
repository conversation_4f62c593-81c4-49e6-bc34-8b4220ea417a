{"name": "constants-browserify", "description": "node's constants module for the browser", "version": "1.0.0", "repository": {"type": "git", "url": "git://github.com/juliangruber/constants-browserify.git"}, "homepage": "https://github.com/juliangruber/constants-browserify", "main": "constants.json", "dependencies": {}, "keywords": ["constants", "node", "browser", "browserify"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "http://juliangruber.com"}, "scripts": {"test": "node test.js"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>", "url": "http://netflix.com"}], "license": "MIT", "__npminstall_done": true, "_from": "constants-browserify@1.0.0", "_resolved": "https://registry.npmmirror.com/constants-browserify/-/constants-browserify-1.0.0.tgz"}