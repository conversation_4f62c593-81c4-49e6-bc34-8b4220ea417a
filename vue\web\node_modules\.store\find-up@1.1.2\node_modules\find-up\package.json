{"name": "find-up", "version": "1.1.2", "description": "Find a file by walking up parent directories", "license": "MIT", "repository": "sindresorhus/find-up", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "sindresorhus.com"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "xo && ava"}, "files": ["index.js"], "keywords": ["find", "up", "find-up", "findup", "look-up", "look", "file", "search", "match", "package", "resolve", "parent", "parents", "folder", "directory", "dir", "walk", "walking", "path"], "dependencies": {"path-exists": "^2.0.0", "pinkie-promise": "^2.0.0"}, "devDependencies": {"ava": "*", "tempfile": "^1.1.1", "xo": "*"}, "__npminstall_done": true, "_from": "find-up@1.1.2", "_resolved": "https://registry.npmmirror.com/find-up/-/find-up-1.1.2.tgz"}