var compare = require('./compare');

function mediator(a, b) {
	return compare(this, a.converted, b.converted);
}

module.exports = function (array, opts) {
	if (!Array.isArray(array) || array.length < 2) {
		return array;
	}
	if (typeof opts !== 'object') {
		opts = {};
	}
	opts.sign = !!opts.sign;
	var insensitive = !!opts.insensitive;
	var result = Array(array.length);
	var i, max, value;

	for (i = 0, max = array.length; i < max; i += 1) {
		value = String(array[i]);
		result[i] = {
			value: array[i],
			converted: insensitive ? value.toLowerCase() : value
		};
	}

	result.sort(mediator.bind(opts));

	for (i = result.length - 1; ~i; i -= 1) {
		result[i] = result[i].value;
	}

	return result;
};
