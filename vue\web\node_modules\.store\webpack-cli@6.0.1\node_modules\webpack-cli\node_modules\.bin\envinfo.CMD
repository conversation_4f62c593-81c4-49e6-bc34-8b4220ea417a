@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=%~dp0\C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\envinfo@7.14.0\node_modules"
) ELSE (
  @SET "NODE_PATH=%NODE_PATH%;%~dp0\C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\envinfo@7.14.0\node_modules"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\envinfo@7.14.0\node_modules\envinfo\dist\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\envinfo@7.14.0\node_modules\envinfo\dist\cli.js" %*
)
