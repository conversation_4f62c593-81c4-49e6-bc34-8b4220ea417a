{"name": "commander", "version": "2.13.0", "description": "the complete solution for node.js command-line programs", "keywords": ["commander", "command", "option", "parser"], "author": "<PERSON><PERSON> <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/tj/commander.js.git"}, "scripts": {"test": "make test && npm run test-typings", "test-typings": "node_modules/typescript/bin/tsc -p tsconfig.json"}, "main": "index", "files": ["index.js", "typings/index.d.ts"], "dependencies": {}, "devDependencies": {"@types/node": "^7.0.48", "should": "^11.2.1", "sinon": "^2.4.1", "typescript": "^2.6.2"}, "typings": "typings/index.d.ts", "__npminstall_done": true, "_from": "commander@2.13.0", "_resolved": "https://registry.npmmirror.com/commander/-/commander-2.13.0.tgz"}