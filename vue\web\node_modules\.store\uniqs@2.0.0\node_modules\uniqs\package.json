{"name": "uniqs", "version": "2.0.0", "description": "Tiny utility to create unions and de-duplicated lists", "keywords": ["unique", "uniq", "dedupe", "union"], "repository": {"type": "git", "url": "git://github.com/fgnass/uniqs.git"}, "main": "index.js", "scripts": {"test": "node test"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "__npminstall_done": true, "_from": "uniqs@2.0.0", "_resolved": "https://registry.npmmirror.com/uniqs/-/uniqs-2.0.0.tgz"}