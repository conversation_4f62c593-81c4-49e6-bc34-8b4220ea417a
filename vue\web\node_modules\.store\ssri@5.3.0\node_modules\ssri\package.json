{"name": "ssri", "version": "5.3.0", "description": "Standard Subresource Integrity library --  parses, serializes, generates, and verifies integrity metadata according to the SRI spec.", "main": "index.js", "files": ["*.js"], "scripts": {"prerelease": "npm t", "postrelease": "npm publish && git push --follow-tags", "pretest": "standard", "release": "standard-version -s", "test": "tap -J --coverage test/*.js", "update-coc": "weallbehave -o . && git add CODE_OF_CONDUCT.md && git commit -m 'docs(coc): updated CODE_OF_CONDUCT.md'", "update-contrib": "weallcontribute -o . && git add CONTRIBUTING.md && git commit -m 'docs(contributing): updated CONTRIBUTING.md'"}, "repository": "https://github.com/zkat/ssri", "keywords": ["w3c", "web", "security", "integrity", "checksum", "hashing", "subresource integrity", "sri", "sri hash", "sri string", "sri generator", "html"], "author": {"name": "<PERSON>", "email": "<EMAIL>", "twitter": "maybekatz"}, "license": "ISC", "dependencies": {"safe-buffer": "^5.1.1"}, "devDependencies": {"nyc": "^11.4.1", "standard": "^10.0.3", "standard-version": "^4.3.0", "tap": "^11.1.0", "weallbehave": "^1.2.0", "weallcontribute": "^1.0.8"}, "config": {"nyc": {"exclude": ["node_modules/**", "test/**"]}}, "__npminstall_done": true, "_from": "ssri@5.3.0", "_resolved": "https://registry.npmmirror.com/ssri/-/ssri-5.3.0.tgz"}