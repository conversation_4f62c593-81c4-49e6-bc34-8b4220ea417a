'use strict';

function _defaults(obj, defaults) { var keys = Object.getOwnPropertyNames(defaults); for (var i = 0; i < keys.length; i++) { var key = keys[i]; var value = Object.getOwnPropertyDescriptor(defaults, key); if (value && value.configurable && obj[key] === undefined) { Object.defineProperty(obj, key, value); } } return obj; }

function _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError("Cannot call a class as a function"); } }

function _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError("this hasn't been initialised - super() hasn't been called"); } return call && (typeof call === "object" || typeof call === "function") ? call : self; }

function _inherits(subClass, superClass) { if (typeof superClass !== "function" && superClass !== null) { throw new TypeError("Super expression must either be null or a function, not " + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : _defaults(subClass, superClass); }

var Declaration = require('../declaration');

var BASIC = ['none', 'underline', 'overline', 'line-through', 'blink', 'inherit', 'initial', 'unset'];

var TextDecoration = function (_Declaration) {
    _inherits(TextDecoration, _Declaration);

    function TextDecoration() {
        _classCallCheck(this, TextDecoration);

        return _possibleConstructorReturn(this, _Declaration.apply(this, arguments));
    }

    /**
     * Do not add prefixes for basic values.
     */
    TextDecoration.prototype.check = function check(decl) {
        return decl.value.split(/\s+/).some(function (i) {
            return BASIC.indexOf(i) === -1;
        });
    };

    return TextDecoration;
}(Declaration);

Object.defineProperty(TextDecoration, 'names', {
    enumerable: true,
    writable: true,
    value: ['text-decoration']
});


module.exports = TextDecoration;