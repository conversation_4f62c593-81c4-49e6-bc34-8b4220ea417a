@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=%~dp0\C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\babylon@6.18.0\node_modules"
) ELSE (
  @SET "NODE_PATH=%NODE_PATH%;%~dp0\C:\Users\<USER>\Desktop\p\vue\web\node_modules\.store\babylon@6.18.0\node_modules"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\..\..\..\..\babylon@6.18.0\node_modules\babylon\bin\babylon.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\..\..\..\..\babylon@6.18.0\node_modules\babylon\bin\babylon.js" %*
)
