/* nodent 3.1.5: NoDent - Asynchronous Javascript language extensions */
require=function e(t,n,r){function i(o,a){if(!n[o]){if(!t[o]){var u="function"==typeof require&&require;if(!a&&u)return u(o,!0);if(s)return s(o,!0);var c=new Error("Cannot find module '"+o+"'");throw c.code="MODULE_NOT_FOUND",c}var l=n[o]={exports:{}};t[o][0].call(l.exports,function(e){var n=t[o][1][e];return i(n||e)},l,l.exports,e,t,n,r)}return n[o].exports}for(var s="function"==typeof require&&require,o=0;o<r.length;o++)i(r[o]);return i}({1:[function(e,t,n){t.exports=function(t){switch(parseInt(t.version)){case 2:case 3:t.plugins.asyncawait=e("./acorn-v3");break;case 4:case 5:t.plugins.asyncawait=e("./acorn-v4");break;default:throw new Error("acorn-es7-plugin requires Acorn v2, 3, 4 or 5")}return t}},{"./acorn-v3":2,"./acorn-v4":3}],2:[function(e,t,n){function r(e,t){return e.lineStart>=t}function i(e,t,n){var r=t.input.slice(t.start);return n&&(r=r.replace(l,"$1 $3")),e.test(r)}function s(e,t,n,r){var i=new e.constructor(e.options,e.input,t);if(n)for(var s in n)i[s]=n[s];var o=e,a=i;return["inFunction","inAsyncFunction","inAsync","inGenerator","inModule"].forEach(function(e){e in o&&(a[e]=o[e])}),r&&(i.options.preserveParens=!0),i.nextToken(),i}var o={},a=/^async[\t ]+(return|throw)/,u=/^async[\t ]+function/,c=/^\s*[():;]/,l=/([^\n])\/\*(\*(?!\/)|[^\n*])*\*\/([^\n])/g,p=/\s*(get|set)\s*\(/;t.exports=function(e,t){var n=function(){};e.extend("initialContext",function(r){return function(){return this.options.ecmaVersion<7&&(n=function(t){e.raise(t.start,"async/await keywords only available when ecmaVersion>=7")}),this.reservedWords=new RegExp(this.reservedWords.toString().replace(/await|async/g,"").replace("|/","/").replace("/|","/").replace("||","|")),this.reservedWordsStrict=new RegExp(this.reservedWordsStrict.toString().replace(/await|async/g,"").replace("|/","/").replace("/|","/").replace("||","|")),this.reservedWordsStrictBind=new RegExp(this.reservedWordsStrictBind.toString().replace(/await|async/g,"").replace("|/","/").replace("/|","/").replace("||","|")),this.inAsyncFunction=t.inAsyncFunction,t.awaitAnywhere&&t.inAsyncFunction&&e.raise(node.start,"The options awaitAnywhere and inAsyncFunction are mutually exclusive"),r.apply(this,arguments)}}),e.extend("shouldParseExportStatement",function(e){return function(){return!("name"!==this.type.label||"async"!==this.value||!i(u,this))||e.apply(this,arguments)}}),e.extend("parseStatement",function(e){return function(n,r){var s=this.start,o=this.startLoc;if("name"===this.type.label)if(i(u,this,!0)){var c=this.inAsyncFunction;try{return this.inAsyncFunction=!0,this.next(),(l=this.parseStatement(n,r)).async=!0,l.start=s,l.loc&&(l.loc.start=o),l.range&&(l.range[0]=s),l}finally{this.inAsyncFunction=c}}else if("object"==typeof t&&t.asyncExits&&i(a,this)){this.next();var l;return(l=this.parseStatement(n,r)).async=!0,l.start=s,l.loc&&(l.loc.start=o),l.range&&(l.range[0]=s),l}return e.apply(this,arguments)}}),e.extend("parseIdent",function(e){return function(t){var n=e.apply(this,arguments);return this.inAsyncFunction&&"await"===n.name&&0===arguments.length&&this.raise(n.start,"'await' is reserved within async functions"),n}}),e.extend("parseExprAtom",function(e){return function(i){var a,u=this.start,l=this.startLoc,p=e.apply(this,arguments);if("Identifier"===p.type)if("async"!==p.name||r(this,p.end)){if("await"===p.name){var h=this.startNodeAt(p.start,p.loc&&p.loc.start);if(this.inAsyncFunction)return a=this.parseExprSubscripts(),h.operator="await",h.argument=a,h=this.finishNodeAt(h,"AwaitExpression",a.end,a.loc&&a.loc.end),n(h),h;if(this.input.slice(p.end).match(c))return t.awaitAnywhere||"module"!==this.options.sourceType?p:this.raise(p.start,"'await' is reserved within modules");if("object"==typeof t&&t.awaitAnywhere&&(u=this.start,(a=s(this,u-4).parseExprSubscripts()).end<=u))return a=s(this,u).parseExprSubscripts(),h.operator="await",h.argument=a,h=this.finishNodeAt(h,"AwaitExpression",a.end,a.loc&&a.loc.end),this.pos=a.end,this.end=a.end,this.endLoc=a.endLoc,this.next(),n(h),h;if(!t.awaitAnywhere&&"module"===this.options.sourceType)return this.raise(p.start,"'await' is reserved within modules")}}else{var f=this.inAsyncFunction;try{this.inAsyncFunction=!0;var d=this,y=!1,m={parseFunctionBody:function(e,t){try{var n=y;return y=!0,d.parseFunctionBody.apply(this,arguments)}finally{y=n}},raise:function(){try{return d.raise.apply(this,arguments)}catch(e){throw y?e:o}}};if("SequenceExpression"===(a=s(this,this.start,m,!0).parseExpression()).type&&(a=a.expressions[0]),"CallExpression"===a.type&&(a=a.callee),"FunctionExpression"===a.type||"FunctionDeclaration"===a.type||"ArrowFunctionExpression"===a.type)return"SequenceExpression"===(a=s(this,this.start,m).parseExpression()).type&&(a=a.expressions[0]),"CallExpression"===a.type&&(a=a.callee),a.async=!0,a.start=u,a.loc&&(a.loc.start=l),a.range&&(a.range[0]=u),this.pos=a.end,this.end=a.end,this.endLoc=a.endLoc,this.next(),n(a),a}catch(e){if(e!==o)throw e}finally{this.inAsyncFunction=f}}return p}}),e.extend("finishNodeAt",function(e){return function(t,n,r,i){return t.__asyncValue&&(delete t.__asyncValue,t.value.async=!0),e.apply(this,arguments)}}),e.extend("finishNode",function(e){return function(t,n){return t.__asyncValue&&(delete t.__asyncValue,t.value.async=!0),e.apply(this,arguments)}}),e.extend("parsePropertyName",function(e){return function(t){t.key&&t.key.name;var i=e.apply(this,arguments);return"Identifier"!==i.type||"async"!==i.name||r(this,i.end)||this.input.slice(i.end).match(c)||(p.test(this.input.slice(i.end))?(i=e.apply(this,arguments),t.__asyncValue=!0):(n(t),"set"===t.kind&&this.raise(i.start,"'set <member>(value)' cannot be be async"),"Identifier"===(i=e.apply(this,arguments)).type&&"set"===i.name&&this.raise(i.start,"'set <member>(value)' cannot be be async"),t.__asyncValue=!0)),i}}),e.extend("parseClassMethod",function(e){return function(t,n,r){var i;n.__asyncValue&&("constructor"===n.kind&&this.raise(n.start,"class constructor() cannot be be async"),i=this.inAsyncFunction,this.inAsyncFunction=!0);var s=e.apply(this,arguments);return this.inAsyncFunction=i,s}}),e.extend("parseMethod",function(e){return function(t){var n;this.__currentProperty&&this.__currentProperty.__asyncValue&&(n=this.inAsyncFunction,this.inAsyncFunction=!0);var r=e.apply(this,arguments);return this.inAsyncFunction=n,r}}),e.extend("parsePropertyValue",function(e){return function(t,n,r,i,s,o){var a=this.__currentProperty;this.__currentProperty=t;var u;t.__asyncValue&&(u=this.inAsyncFunction,this.inAsyncFunction=!0);var c=e.apply(this,arguments);return this.inAsyncFunction=u,this.__currentProperty=a,c}})}},{}],3:[function(e,t,n){function r(e,t,n){var r=new e.constructor(e.options,e.input,t);if(n)for(var i in n)r[i]=n[i];var s=e,o=r;return["inFunction","inAsync","inGenerator","inModule"].forEach(function(e){e in s&&(o[e]=s[e])}),r.nextToken(),r}var i=/^async[\t ]+(return|throw)/,s=/^\s*[):;]/,o=/([^\n])\/\*(\*(?!\/)|[^\n*])*\*\/([^\n])/g;t.exports=function(e,t){t&&"object"==typeof t||(t={}),e.extend("parse",function(n){return function(){return this.inAsync=t.inAsyncFunction,t.awaitAnywhere&&t.inAsyncFunction&&e.raise(node.start,"The options awaitAnywhere and inAsyncFunction are mutually exclusive"),n.apply(this,arguments)}}),e.extend("parseStatement",function(e){return function(n,r){var s=this.start,a=this.startLoc;if("name"===this.type.label&&t.asyncExits&&function(e,t,n){var r=t.input.slice(t.start);return n&&(r=r.replace(o,"$1 $3")),e.test(r)}(i,this)){this.next();var u=this.parseStatement(n,r);return u.async=!0,u.start=s,u.loc&&(u.loc.start=a),u.range&&(u.range[0]=s),u}return e.apply(this,arguments)}}),e.extend("parseIdent",function(e){return function(n){return"module"===this.options.sourceType&&this.options.ecmaVersion>=8&&t.awaitAnywhere?e.call(this,!0):e.apply(this,arguments)}}),e.extend("parseExprAtom",function(e){var n={};return function(i){var s,o=this.start,a=(this.startLoc,e.apply(this,arguments));if("Identifier"===a.type&&"await"===a.name&&!this.inAsync&&t.awaitAnywhere){var u=this.startNodeAt(a.start,a.loc&&a.loc.start);o=this.start;var c={raise:function(){try{return pp.raise.apply(this,arguments)}catch(e){throw n}}};try{if((s=r(this,o-4,c).parseExprSubscripts()).end<=o)return s=r(this,o,c).parseExprSubscripts(),u.argument=s,u=this.finishNodeAt(u,"AwaitExpression",s.end,s.loc&&s.loc.end),this.pos=s.end,this.end=s.end,this.endLoc=s.endLoc,this.next(),u}catch(e){if(e===n)return a;throw e}}return a}});var n={undefined:!0,get:!0,set:!0,static:!0,async:!0,constructor:!0};e.extend("parsePropertyName",function(e){return function(t){var r=t.key&&t.key.name,i=e.apply(this,arguments);return"get"===this.value&&(t.__maybeStaticAsyncGetter=!0),n[this.value]?i:("Identifier"!==i.type||"async"!==i.name&&"async"!==r||function(e,t){return e.lineStart>=t}(this,i.end)||this.input.slice(i.end).match(s)?delete t.__maybeStaticAsyncGetter:"set"===t.kind||"set"===i.name?this.raise(i.start,"'set <member>(value)' cannot be be async"):(this.__isAsyncProp=!0,"Identifier"===(i=e.apply(this,arguments)).type&&"set"===i.name&&this.raise(i.start,"'set <member>(value)' cannot be be async")),i)}}),e.extend("parseClassMethod",function(e){return function(t,n,r){var i=e.apply(this,arguments);return n.__maybeStaticAsyncGetter&&(delete n.__maybeStaticAsyncGetter,"get"!==n.key.name&&(n.kind="get")),i}}),e.extend("parseFunctionBody",function(e){return function(t,n){var r=this.inAsync;this.__isAsyncProp&&(t.async=!0,this.inAsync=!0,delete this.__isAsyncProp);var i=e.apply(this,arguments);return this.inAsync=r,i}})}},{}],4:[function(e,t,n){!function(e,r){"object"==typeof n&&void 0!==t?r(n):"function"==typeof define&&define.amd?define(["exports"],r):r(e.acorn=e.acorn||{})}(this,function(e){"use strict";function t(e,t){for(var n=65536,r=0;r<t.length;r+=2){if((n+=t[r])>e)return!1;if((n+=t[r+1])>=e)return!0}}function n(e,n){return e<65?36===e:e<91||(e<97?95===e:e<123||(e<=65535?e>=170&&x.test(String.fromCharCode(e)):!1!==n&&t(e,E)))}function r(e,n){return e<48?36===e:e<58||!(e<65)&&(e<91||(e<97?95===e:e<123||(e<=65535?e>=170&&w.test(String.fromCharCode(e)):!1!==n&&(t(e,E)||t(e,S)))))}function i(e,t){return new k(e,{beforeExpr:!0,binop:t})}function s(e,t){return void 0===t&&(t={}),t.keyword=e,_[e]=new k(e,t)}function o(e){return 10===e||13===e||8232===e||8233===e}function a(e,t){return $.call(e,t)}function u(e,t){for(var n=1,r=0;;){T.lastIndex=r;var i=T.exec(e);if(!(i&&i.index<t))return new I(n,t-r);++n,r=i.index+i[0].length}}function c(e){var t={};for(var n in D)t[n]=e&&a(e,n)?e[n]:D[n];if(t.ecmaVersion>=2015&&(t.ecmaVersion-=2009),null==t.allowReserved&&(t.allowReserved=t.ecmaVersion<5),R(t.onToken)){var r=t.onToken;t.onToken=function(e){return r.push(e)}}return R(t.onComment)&&(t.onComment=function(e,t){return function(n,r,i,s,o,a){var u={type:n?"Block":"Line",value:r,start:i,end:s};e.locations&&(u.loc=new j(this,o,a)),e.ranges&&(u.range=[i,s]),t.push(u)}}(t,t.onComment)),t}function l(e){return new RegExp("^(?:"+e.replace(/ /g,"|")+")$")}function p(){this.shorthandAssign=this.trailingComma=this.parenthesizedAssign=this.parenthesizedBind=-1}function h(e,t,n,r){return e.type=t,e.end=n,this.options.locations&&(e.loc.end=r),this.options.ranges&&(e.range[1]=n),e}function f(e,t,n,r){try{return new RegExp(e,t)}catch(e){if(void 0!==n)throw e instanceof SyntaxError&&r.raise(n,"Error parsing regular expression: "+e.message),e}}function d(e){return e<=65535?String.fromCharCode(e):(e-=65536,String.fromCharCode(55296+(e>>10),56320+(1023&e)))}var y={3:"abstract boolean byte char class double enum export extends final float goto implements import int interface long native package private protected public short static super synchronized throws transient volatile",5:"class enum extends super const export import",6:"enum",strict:"implements interface let package private protected public static yield",strictBind:"eval arguments"},m="break case catch continue debugger default do else finally for function if return switch throw try var while with null true false instanceof typeof void delete new in this",g={5:m,6:m+" const class extends export import super"},v="ªµºÀ-ÖØ-öø-ˁˆ-ˑˠ-ˤˬˮͰ-ʹͶͷͺ-ͽͿΆΈ-ΊΌΎ-ΡΣ-ϵϷ-ҁҊ-ԯԱ-Ֆՙա-ևא-תװ-ײؠ-يٮٯٱ-ۓەۥۦۮۯۺ-ۼۿܐܒ-ܯݍ-ޥޱߊ-ߪߴߵߺࠀ-ࠕࠚࠤࠨࡀ-ࡘࢠ-ࢴࢶ-ࢽऄ-हऽॐक़-ॡॱ-ঀঅ-ঌএঐও-নপ-রলশ-হঽৎড়ঢ়য়-ৡৰৱਅ-ਊਏਐਓ-ਨਪ-ਰਲਲ਼ਵਸ਼ਸਹਖ਼-ੜਫ਼ੲ-ੴઅ-ઍએ-ઑઓ-નપ-રલળવ-હઽૐૠૡૹଅ-ଌଏଐଓ-ନପ-ରଲଳଵ-ହଽଡ଼ଢ଼ୟ-ୡୱஃஅ-ஊஎ-ஐஒ-கஙசஜஞடணதந-பம-ஹௐఅ-ఌఎ-ఐఒ-నప-హఽౘ-ౚౠౡಀಅ-ಌಎ-ಐಒ-ನಪ-ಳವ-ಹಽೞೠೡೱೲഅ-ഌഎ-ഐഒ-ഺഽൎൔ-ൖൟ-ൡൺ-ൿඅ-ඖක-නඳ-රලව-ෆก-ะาำเ-ๆກຂຄງຈຊຍດ-ທນ-ຟມ-ຣລວສຫອ-ະາຳຽເ-ໄໆໜ-ໟༀཀ-ཇཉ-ཬྈ-ྌက-ဪဿၐ-ၕၚ-ၝၡၥၦၮ-ၰၵ-ႁႎႠ-ჅჇჍა-ჺჼ-ቈቊ-ቍቐ-ቖቘቚ-ቝበ-ኈኊ-ኍነ-ኰኲ-ኵኸ-ኾዀዂ-ዅወ-ዖዘ-ጐጒ-ጕጘ-ፚᎀ-ᎏᎠ-Ᏽᏸ-ᏽᐁ-ᙬᙯ-ᙿᚁ-ᚚᚠ-ᛪᛮ-ᛸᜀ-ᜌᜎ-ᜑᜠ-ᜱᝀ-ᝑᝠ-ᝬᝮ-ᝰក-ឳៗៜᠠ-ᡷᢀ-ᢨᢪᢰ-ᣵᤀ-ᤞᥐ-ᥭᥰ-ᥴᦀ-ᦫᦰ-ᧉᨀ-ᨖᨠ-ᩔᪧᬅ-ᬳᭅ-ᭋᮃ-ᮠᮮᮯᮺ-ᯥᰀ-ᰣᱍ-ᱏᱚ-ᱽᲀ-ᲈᳩ-ᳬᳮ-ᳱᳵᳶᴀ-ᶿḀ-ἕἘ-Ἕἠ-ὅὈ-Ὅὐ-ὗὙὛὝὟ-ώᾀ-ᾴᾶ-ᾼιῂ-ῄῆ-ῌῐ-ΐῖ-Ίῠ-Ῥῲ-ῴῶ-ῼⁱⁿₐ-ₜℂℇℊ-ℓℕ℘-ℝℤΩℨK-ℹℼ-ℿⅅ-ⅉⅎⅠ-ↈⰀ-Ⱞⰰ-ⱞⱠ-ⳤⳫ-ⳮⳲⳳⴀ-ⴥⴧⴭⴰ-ⵧⵯⶀ-ⶖⶠ-ⶦⶨ-ⶮⶰ-ⶶⶸ-ⶾⷀ-ⷆⷈ-ⷎⷐ-ⷖⷘ-ⷞ々-〇〡-〩〱-〵〸-〼ぁ-ゖ゛-ゟァ-ヺー-ヿㄅ-ㄭㄱ-ㆎㆠ-ㆺㇰ-ㇿ㐀-䶵一-鿕ꀀ-ꒌꓐ-ꓽꔀ-ꘌꘐ-ꘟꘪꘫꙀ-ꙮꙿ-ꚝꚠ-ꛯꜗ-ꜟꜢ-ꞈꞋ-ꞮꞰ-ꞷꟷ-ꠁꠃ-ꠅꠇ-ꠊꠌ-ꠢꡀ-ꡳꢂ-ꢳꣲ-ꣷꣻꣽꤊ-ꤥꤰ-ꥆꥠ-ꥼꦄ-ꦲꧏꧠ-ꧤꧦ-ꧯꧺ-ꧾꨀ-ꨨꩀ-ꩂꩄ-ꩋꩠ-ꩶꩺꩾ-ꪯꪱꪵꪶꪹ-ꪽꫀꫂꫛ-ꫝꫠ-ꫪꫲ-ꫴꬁ-ꬆꬉ-ꬎꬑ-ꬖꬠ-ꬦꬨ-ꬮꬰ-ꭚꭜ-ꭥꭰ-ꯢ가-힣ힰ-ퟆퟋ-ퟻ豈-舘並-龎ﬀ-ﬆﬓ-ﬗיִײַ-ﬨשׁ-זּטּ-לּמּנּסּףּפּצּ-ﮱﯓ-ﴽﵐ-ﶏﶒ-ﷇﷰ-ﷻﹰ-ﹴﹶ-ﻼＡ-Ｚａ-ｚｦ-ﾾￂ-ￇￊ-ￏￒ-ￗￚ-ￜ",b="‌‍·̀-ͯ·҃-֑҇-ׇֽֿׁׂׅׄؐ-ًؚ-٩ٰۖ-ۜ۟-۪ۤۧۨ-ۭ۰-۹ܑܰ-݊ަ-ް߀-߉߫-߳ࠖ-࠙ࠛ-ࠣࠥ-ࠧࠩ-࡙࠭-࡛ࣔ-ࣣ࣡-ःऺ-़ा-ॏ॑-ॗॢॣ०-९ঁ-ঃ়া-ৄেৈো-্ৗৢৣ০-৯ਁ-ਃ਼ਾ-ੂੇੈੋ-੍ੑ੦-ੱੵઁ-ઃ઼ા-ૅે-ૉો-્ૢૣ૦-૯ଁ-ଃ଼ା-ୄେୈୋ-୍ୖୗୢୣ୦-୯ஂா-ூெ-ைொ-்ௗ௦-௯ఀ-ఃా-ౄె-ైొ-్ౕౖౢౣ౦-౯ಁ-ಃ಼ಾ-ೄೆ-ೈೊ-್ೕೖೢೣ೦-೯ഁ-ഃാ-ൄെ-ൈൊ-്ൗൢൣ൦-൯ංඃ්ා-ුූෘ-ෟ෦-෯ෲෳัิ-ฺ็-๎๐-๙ັິ-ູົຼ່-ໍ໐-໙༘༙༠-༩༹༵༷༾༿ཱ-྄྆྇ྍ-ྗྙ-ྼ࿆ါ-ှ၀-၉ၖ-ၙၞ-ၠၢ-ၤၧ-ၭၱ-ၴႂ-ႍႏ-ႝ፝-፟፩-፱ᜒ-᜔ᜲ-᜴ᝒᝓᝲᝳ឴-៓៝០-៩᠋-᠍᠐-᠙ᢩᤠ-ᤫᤰ-᤻᥆-᥏᧐-᧚ᨗ-ᨛᩕ-ᩞ᩠-᩿᩼-᪉᪐-᪙᪰-᪽ᬀ-ᬄ᬴-᭄᭐-᭙᭫-᭳ᮀ-ᮂᮡ-ᮭ᮰-᮹᯦-᯳ᰤ-᰷᱀-᱉᱐-᱙᳐-᳔᳒-᳨᳭ᳲ-᳴᳸᳹᷀-᷵᷻-᷿‿⁀⁔⃐-⃥⃜⃡-⃰⳯-⵿⳱ⷠ-〪ⷿ-゙゚〯꘠-꘩꙯ꙴ-꙽ꚞꚟ꛰꛱ꠂ꠆ꠋꠣ-ꠧꢀꢁꢴ-ꣅ꣐-꣙꣠-꣱꤀-꤉ꤦ-꤭ꥇ-꥓ꦀ-ꦃ꦳-꧀꧐-꧙ꧥ꧰-꧹ꨩ-ꨶꩃꩌꩍ꩐-꩙ꩻ-ꩽꪰꪲ-ꪴꪷꪸꪾ꪿꫁ꫫ-ꫯꫵ꫶ꯣ-ꯪ꯬꯭꯰-꯹ﬞ︀-️︠-︯︳︴﹍-﹏０-９＿",x=new RegExp("["+v+"]"),w=new RegExp("["+v+b+"]");v=b=null;var E=[0,11,2,25,2,18,2,1,2,14,3,13,35,122,70,52,268,28,4,48,48,31,17,26,6,37,11,29,3,35,5,7,2,4,43,157,19,35,5,35,5,39,9,51,157,310,10,21,11,7,153,5,3,0,2,43,2,1,4,0,3,22,11,22,10,30,66,18,2,1,11,21,11,25,71,55,7,1,65,0,16,3,2,2,2,26,45,28,4,28,36,7,2,27,28,53,11,21,11,18,14,17,111,72,56,50,14,50,785,52,76,44,33,24,27,35,42,34,4,0,13,47,15,3,22,0,2,0,36,17,2,24,85,6,2,0,2,3,2,14,2,9,8,46,39,7,3,1,3,21,2,6,2,1,2,4,4,0,19,0,13,4,159,52,19,3,54,47,21,1,2,0,185,46,42,3,37,47,21,0,60,42,86,25,391,63,32,0,449,56,264,8,2,36,18,0,50,29,881,921,103,110,18,195,2749,1070,4050,582,8634,568,8,30,114,29,19,47,17,3,32,20,6,18,881,68,12,0,67,12,65,0,32,6124,20,754,9486,1,3071,106,6,12,4,8,8,9,5991,84,2,70,2,1,3,0,3,1,3,3,2,11,2,0,2,6,2,64,2,3,3,7,2,6,2,27,2,3,2,4,2,0,4,6,2,339,3,24,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,30,2,24,2,7,4149,196,60,67,1213,3,2,26,2,1,2,0,3,0,2,9,2,3,2,0,2,0,7,0,5,0,2,0,2,0,2,2,2,1,2,0,3,0,2,0,2,0,2,0,2,0,2,1,2,0,3,3,2,6,2,3,2,3,2,0,2,9,2,16,6,2,2,4,2,16,4421,42710,42,4148,12,221,3,5761,10591,541],S=[509,0,227,0,150,4,294,9,1368,2,2,1,6,3,41,2,5,0,166,1,1306,2,54,14,32,9,16,3,46,10,54,9,7,2,37,13,2,9,52,0,13,2,49,13,10,2,4,9,83,11,7,0,161,11,6,9,7,3,57,0,2,6,3,1,3,2,10,0,11,1,3,6,4,4,193,17,10,9,87,19,13,9,214,6,3,8,28,1,83,16,16,9,82,12,9,9,84,14,5,9,423,9,838,7,2,7,17,9,57,21,2,13,19882,9,135,4,60,6,26,9,1016,45,17,3,19723,1,5319,4,4,5,9,7,3,6,31,3,149,2,1418,49,513,54,5,49,9,0,15,0,23,4,2,14,1361,6,2,16,3,6,2,1,2,4,2214,6,110,6,6,9,792487,239],k=function(e,t){void 0===t&&(t={}),this.label=e,this.keyword=t.keyword,this.beforeExpr=!!t.beforeExpr,this.startsExpr=!!t.startsExpr,this.isLoop=!!t.isLoop,this.isAssign=!!t.isAssign,this.prefix=!!t.prefix,this.postfix=!!t.postfix,this.binop=t.binop||null,this.updateContext=null},A={beforeExpr:!0},C={startsExpr:!0},_={},L={num:new k("num",C),regexp:new k("regexp",C),string:new k("string",C),name:new k("name",C),eof:new k("eof"),bracketL:new k("[",{beforeExpr:!0,startsExpr:!0}),bracketR:new k("]"),braceL:new k("{",{beforeExpr:!0,startsExpr:!0}),braceR:new k("}"),parenL:new k("(",{beforeExpr:!0,startsExpr:!0}),parenR:new k(")"),comma:new k(",",A),semi:new k(";",A),colon:new k(":",A),dot:new k("."),question:new k("?",A),arrow:new k("=>",A),template:new k("template"),invalidTemplate:new k("invalidTemplate"),ellipsis:new k("...",A),backQuote:new k("`",C),dollarBraceL:new k("${",{beforeExpr:!0,startsExpr:!0}),eq:new k("=",{beforeExpr:!0,isAssign:!0}),assign:new k("_=",{beforeExpr:!0,isAssign:!0}),incDec:new k("++/--",{prefix:!0,postfix:!0,startsExpr:!0}),prefix:new k("!/~",{beforeExpr:!0,prefix:!0,startsExpr:!0}),logicalOR:i("||",1),logicalAND:i("&&",2),bitwiseOR:i("|",3),bitwiseXOR:i("^",4),bitwiseAND:i("&",5),equality:i("==/!=/===/!==",6),relational:i("</>/<=/>=",7),bitShift:i("<</>>/>>>",8),plusMin:new k("+/-",{beforeExpr:!0,binop:9,prefix:!0,startsExpr:!0}),modulo:i("%",10),star:i("*",10),slash:i("/",10),starstar:new k("**",{beforeExpr:!0}),_break:s("break"),_case:s("case",A),_catch:s("catch"),_continue:s("continue"),_debugger:s("debugger"),_default:s("default",A),_do:s("do",{isLoop:!0,beforeExpr:!0}),_else:s("else",A),_finally:s("finally"),_for:s("for",{isLoop:!0}),_function:s("function",C),_if:s("if"),_return:s("return",A),_switch:s("switch"),_throw:s("throw",A),_try:s("try"),_var:s("var"),_const:s("const"),_while:s("while",{isLoop:!0}),_with:s("with"),_new:s("new",{beforeExpr:!0,startsExpr:!0}),_this:s("this",C),_super:s("super",C),_class:s("class",C),_extends:s("extends",A),_export:s("export"),_import:s("import"),_null:s("null",C),_true:s("true",C),_false:s("false",C),_in:s("in",{beforeExpr:!0,binop:7}),_instanceof:s("instanceof",{beforeExpr:!0,binop:7}),_typeof:s("typeof",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_void:s("void",{beforeExpr:!0,prefix:!0,startsExpr:!0}),_delete:s("delete",{beforeExpr:!0,prefix:!0,startsExpr:!0})},O=/\r\n?|\n|\u2028|\u2029/,T=new RegExp(O.source,"g"),N=/[\u1680\u180e\u2000-\u200a\u202f\u205f\u3000\ufeff]/,P=/(?:\s|\/\/.*|\/\*[^]*?\*\/)*/g,F=Object.prototype,$=F.hasOwnProperty,B=F.toString,R=Array.isArray||function(e){return"[object Array]"===B.call(e)},I=function(e,t){this.line=e,this.column=t};I.prototype.offset=function(e){return new I(this.line,this.column+e)};var j=function(e,t,n){this.start=t,this.end=n,null!==e.sourceFile&&(this.source=e.sourceFile)},D={ecmaVersion:7,sourceType:"script",onInsertedSemicolon:null,onTrailingComma:null,allowReserved:null,allowReturnOutsideFunction:!1,allowImportExportEverywhere:!1,allowHashBang:!1,locations:!1,onToken:null,onComment:null,ranges:!1,program:null,sourceFile:null,directSourceFile:null,preserveParens:!1,plugins:{}},M={},V=function(e,t,n){this.options=e=c(e),this.sourceFile=e.sourceFile,this.keywords=l(g[e.ecmaVersion>=6?6:5]);var r="";if(!e.allowReserved){for(var i=e.ecmaVersion;!(r=y[i]);i--);"module"==e.sourceType&&(r+=" await")}this.reservedWords=l(r);var s=(r?r+" ":"")+y.strict;this.reservedWordsStrict=l(s),this.reservedWordsStrictBind=l(s+" "+y.strictBind),this.input=String(t),this.containsEsc=!1,this.loadPlugins(e.plugins),n?(this.pos=n,this.lineStart=this.input.lastIndexOf("\n",n-1)+1,this.curLine=this.input.slice(0,this.lineStart).split(O).length):(this.pos=this.lineStart=0,this.curLine=1),this.type=L.eof,this.value=null,this.start=this.end=this.pos,this.startLoc=this.endLoc=this.curPosition(),this.lastTokEndLoc=this.lastTokStartLoc=null,this.lastTokStart=this.lastTokEnd=this.pos,this.context=this.initialContext(),this.exprAllowed=!0,this.inModule="module"===e.sourceType,this.strict=this.inModule||this.strictDirective(this.pos),this.potentialArrowAt=-1,this.inFunction=this.inGenerator=this.inAsync=!1,this.yieldPos=this.awaitPos=0,this.labels=[],0===this.pos&&e.allowHashBang&&"#!"===this.input.slice(0,2)&&this.skipLineComment(2),this.scopeStack=[],this.enterFunctionScope()};V.prototype.isKeyword=function(e){return this.keywords.test(e)},V.prototype.isReservedWord=function(e){return this.reservedWords.test(e)},V.prototype.extend=function(e,t){this[e]=t(this[e])},V.prototype.loadPlugins=function(e){for(var t in e){var n=M[t];if(!n)throw new Error("Plugin '"+t+"' not found");n(this,e[t])}},V.prototype.parse=function(){var e=this.options.program||this.startNode();return this.nextToken(),this.parseTopLevel(e)};var q=V.prototype,U=/^(?:'((?:\\.|[^'])*?)'|"((?:\\.|[^"])*?)"|;)/;q.strictDirective=function(e){for(;;){P.lastIndex=e,e+=P.exec(this.input)[0].length;var t=U.exec(this.input.slice(e));if(!t)return!1;if("use strict"==(t[1]||t[2]))return!0;e+=t[0].length}},q.eat=function(e){return this.type===e&&(this.next(),!0)},q.isContextual=function(e){return this.type===L.name&&this.value===e},q.eatContextual=function(e){return this.value===e&&this.eat(L.name)},q.expectContextual=function(e){this.eatContextual(e)||this.unexpected()},q.canInsertSemicolon=function(){return this.type===L.eof||this.type===L.braceR||O.test(this.input.slice(this.lastTokEnd,this.start))},q.insertSemicolon=function(){if(this.canInsertSemicolon())return this.options.onInsertedSemicolon&&this.options.onInsertedSemicolon(this.lastTokEnd,this.lastTokEndLoc),!0},q.semicolon=function(){this.eat(L.semi)||this.insertSemicolon()||this.unexpected()},q.afterTrailingComma=function(e,t){if(this.type==e)return this.options.onTrailingComma&&this.options.onTrailingComma(this.lastTokStart,this.lastTokStartLoc),t||this.next(),!0},q.expect=function(e){this.eat(e)||this.unexpected()},q.unexpected=function(e){this.raise(null!=e?e:this.start,"Unexpected token")},q.checkPatternErrors=function(e,t){if(e){e.trailingComma>-1&&this.raiseRecoverable(e.trailingComma,"Comma is not permitted after the rest element");var n=t?e.parenthesizedAssign:e.parenthesizedBind;n>-1&&this.raiseRecoverable(n,"Parenthesized pattern")}},q.checkExpressionErrors=function(e,t){var n=e?e.shorthandAssign:-1;if(!t)return n>=0;n>-1&&this.raise(n,"Shorthand property assignments are valid only in destructuring patterns")},q.checkYieldAwaitInDefaultParams=function(){this.yieldPos&&(!this.awaitPos||this.yieldPos<this.awaitPos)&&this.raise(this.yieldPos,"Yield expression cannot be a default value"),this.awaitPos&&this.raise(this.awaitPos,"Await expression cannot be a default value")},q.isSimpleAssignTarget=function(e){return"ParenthesizedExpression"===e.type?this.isSimpleAssignTarget(e.expression):"Identifier"===e.type||"MemberExpression"===e.type};var z=V.prototype;z.parseTopLevel=function(e){var t={};for(e.body||(e.body=[]);this.type!==L.eof;){var n=this.parseStatement(!0,!0,t);e.body.push(n)}return this.adaptDirectivePrologue(e.body),this.next(),this.options.ecmaVersion>=6&&(e.sourceType=this.options.sourceType),this.finishNode(e,"Program")};var W={kind:"loop"},G={kind:"switch"};z.isLet=function(){if(this.type!==L.name||this.options.ecmaVersion<6||"let"!=this.value)return!1;P.lastIndex=this.pos;var e=P.exec(this.input),t=this.pos+e[0].length,i=this.input.charCodeAt(t);if(91===i||123==i)return!0;if(n(i,!0)){for(var s=t+1;r(this.input.charCodeAt(s),!0);)++s;var o=this.input.slice(t,s);if(!this.isKeyword(o))return!0}return!1},z.isAsyncFunction=function(){if(this.type!==L.name||this.options.ecmaVersion<8||"async"!=this.value)return!1;P.lastIndex=this.pos;var e=P.exec(this.input),t=this.pos+e[0].length;return!(O.test(this.input.slice(this.pos,t))||"function"!==this.input.slice(t,t+8)||t+8!=this.input.length&&r(this.input.charAt(t+8)))},z.parseStatement=function(e,t,n){var r,i=this.type,s=this.startNode();switch(this.isLet()&&(i=L._var,r="let"),i){case L._break:case L._continue:return this.parseBreakContinueStatement(s,i.keyword);case L._debugger:return this.parseDebuggerStatement(s);case L._do:return this.parseDoStatement(s);case L._for:return this.parseForStatement(s);case L._function:return!e&&this.options.ecmaVersion>=6&&this.unexpected(),this.parseFunctionStatement(s,!1);case L._class:return e||this.unexpected(),this.parseClass(s,!0);case L._if:return this.parseIfStatement(s);case L._return:return this.parseReturnStatement(s);case L._switch:return this.parseSwitchStatement(s);case L._throw:return this.parseThrowStatement(s);case L._try:return this.parseTryStatement(s);case L._const:case L._var:return r=r||this.value,e||"var"==r||this.unexpected(),this.parseVarStatement(s,r);case L._while:return this.parseWhileStatement(s);case L._with:return this.parseWithStatement(s);case L.braceL:return this.parseBlock();case L.semi:return this.parseEmptyStatement(s);case L._export:case L._import:return this.options.allowImportExportEverywhere||(t||this.raise(this.start,"'import' and 'export' may only appear at the top level"),this.inModule||this.raise(this.start,"'import' and 'export' may appear only with 'sourceType: module'")),i===L._import?this.parseImport(s):this.parseExport(s,n);default:if(this.isAsyncFunction()&&e)return this.next(),this.parseFunctionStatement(s,!0);var o=this.value,a=this.parseExpression();return i===L.name&&"Identifier"===a.type&&this.eat(L.colon)?this.parseLabeledStatement(s,o,a):this.parseExpressionStatement(s,a)}},z.parseBreakContinueStatement=function(e,t){var n="break"==t;this.next(),this.eat(L.semi)||this.insertSemicolon()?e.label=null:this.type!==L.name?this.unexpected():(e.label=this.parseIdent(),this.semicolon());for(var r=0;r<this.labels.length;++r){var i=this.labels[r];if(null==e.label||i.name===e.label.name){if(null!=i.kind&&(n||"loop"===i.kind))break;if(e.label&&n)break}}return r===this.labels.length&&this.raise(e.start,"Unsyntactic "+t),this.finishNode(e,n?"BreakStatement":"ContinueStatement")},z.parseDebuggerStatement=function(e){return this.next(),this.semicolon(),this.finishNode(e,"DebuggerStatement")},z.parseDoStatement=function(e){return this.next(),this.labels.push(W),e.body=this.parseStatement(!1),this.labels.pop(),this.expect(L._while),e.test=this.parseParenExpression(),this.options.ecmaVersion>=6?this.eat(L.semi):this.semicolon(),this.finishNode(e,"DoWhileStatement")},z.parseForStatement=function(e){if(this.next(),this.labels.push(W),this.enterLexicalScope(),this.expect(L.parenL),this.type===L.semi)return this.parseFor(e,null);var t=this.isLet();if(this.type===L._var||this.type===L._const||t){var n=this.startNode(),r=t?"let":this.value;return this.next(),this.parseVar(n,!0,r),this.finishNode(n,"VariableDeclaration"),!(this.type===L._in||this.options.ecmaVersion>=6&&this.isContextual("of"))||1!==n.declarations.length||"var"!==r&&n.declarations[0].init?this.parseFor(e,n):this.parseForIn(e,n)}var i=new p,s=this.parseExpression(!0,i);return this.type===L._in||this.options.ecmaVersion>=6&&this.isContextual("of")?(this.toAssignable(s),this.checkLVal(s),this.checkPatternErrors(i,!0),this.parseForIn(e,s)):(this.checkExpressionErrors(i,!0),this.parseFor(e,s))},z.parseFunctionStatement=function(e,t){return this.next(),this.parseFunction(e,!0,!1,t)},z.isFunction=function(){return this.type===L._function||this.isAsyncFunction()},z.parseIfStatement=function(e){return this.next(),e.test=this.parseParenExpression(),e.consequent=this.parseStatement(!this.strict&&this.isFunction()),e.alternate=this.eat(L._else)?this.parseStatement(!this.strict&&this.isFunction()):null,this.finishNode(e,"IfStatement")},z.parseReturnStatement=function(e){return this.inFunction||this.options.allowReturnOutsideFunction||this.raise(this.start,"'return' outside of function"),this.next(),this.eat(L.semi)||this.insertSemicolon()?e.argument=null:(e.argument=this.parseExpression(),this.semicolon()),this.finishNode(e,"ReturnStatement")},z.parseSwitchStatement=function(e){this.next(),e.discriminant=this.parseParenExpression(),e.cases=[],this.expect(L.braceL),this.labels.push(G),this.enterLexicalScope();for(var t,n=!1;this.type!=L.braceR;)if(this.type===L._case||this.type===L._default){var r=this.type===L._case;t&&this.finishNode(t,"SwitchCase"),e.cases.push(t=this.startNode()),t.consequent=[],this.next(),r?t.test=this.parseExpression():(n&&this.raiseRecoverable(this.lastTokStart,"Multiple default clauses"),n=!0,t.test=null),this.expect(L.colon)}else t||this.unexpected(),t.consequent.push(this.parseStatement(!0));return this.exitLexicalScope(),t&&this.finishNode(t,"SwitchCase"),this.next(),this.labels.pop(),this.finishNode(e,"SwitchStatement")},z.parseThrowStatement=function(e){return this.next(),O.test(this.input.slice(this.lastTokEnd,this.start))&&this.raise(this.lastTokEnd,"Illegal newline after throw"),e.argument=this.parseExpression(),this.semicolon(),this.finishNode(e,"ThrowStatement")};var J=[];z.parseTryStatement=function(e){if(this.next(),e.block=this.parseBlock(),e.handler=null,this.type===L._catch){var t=this.startNode();this.next(),this.expect(L.parenL),t.param=this.parseBindingAtom(),this.enterLexicalScope(),this.checkLVal(t.param,"let"),this.expect(L.parenR),t.body=this.parseBlock(!1),this.exitLexicalScope(),e.handler=this.finishNode(t,"CatchClause")}return e.finalizer=this.eat(L._finally)?this.parseBlock():null,e.handler||e.finalizer||this.raise(e.start,"Missing catch or finally clause"),this.finishNode(e,"TryStatement")},z.parseVarStatement=function(e,t){return this.next(),this.parseVar(e,!1,t),this.semicolon(),this.finishNode(e,"VariableDeclaration")},z.parseWhileStatement=function(e){return this.next(),e.test=this.parseParenExpression(),this.labels.push(W),e.body=this.parseStatement(!1),this.labels.pop(),this.finishNode(e,"WhileStatement")},z.parseWithStatement=function(e){return this.strict&&this.raise(this.start,"'with' in strict mode"),this.next(),e.object=this.parseParenExpression(),e.body=this.parseStatement(!1),this.finishNode(e,"WithStatement")},z.parseEmptyStatement=function(e){return this.next(),this.finishNode(e,"EmptyStatement")},z.parseLabeledStatement=function(e,t,n){for(var r=0,i=this.labels;r<i.length;r+=1){i[r].name===t&&this.raise(n.start,"Label '"+t+"' is already declared")}for(var s=this.type.isLoop?"loop":this.type===L._switch?"switch":null,o=this.labels.length-1;o>=0;o--){var a=this.labels[o];if(a.statementStart!=e.start)break;a.statementStart=this.start,a.kind=s}return this.labels.push({name:t,kind:s,statementStart:this.start}),e.body=this.parseStatement(!0),("ClassDeclaration"==e.body.type||"VariableDeclaration"==e.body.type&&"var"!=e.body.kind||"FunctionDeclaration"==e.body.type&&(this.strict||e.body.generator))&&this.raiseRecoverable(e.body.start,"Invalid labeled declaration"),this.labels.pop(),e.label=n,this.finishNode(e,"LabeledStatement")},z.parseExpressionStatement=function(e,t){return e.expression=t,this.semicolon(),this.finishNode(e,"ExpressionStatement")},z.parseBlock=function(e){void 0===e&&(e=!0);var t=this.startNode();for(t.body=[],this.expect(L.braceL),e&&this.enterLexicalScope();!this.eat(L.braceR);){var n=this.parseStatement(!0);t.body.push(n)}return e&&this.exitLexicalScope(),this.finishNode(t,"BlockStatement")},z.parseFor=function(e,t){return e.init=t,this.expect(L.semi),e.test=this.type===L.semi?null:this.parseExpression(),this.expect(L.semi),e.update=this.type===L.parenR?null:this.parseExpression(),this.expect(L.parenR),this.exitLexicalScope(),e.body=this.parseStatement(!1),this.labels.pop(),this.finishNode(e,"ForStatement")},z.parseForIn=function(e,t){var n=this.type===L._in?"ForInStatement":"ForOfStatement";return this.next(),e.left=t,e.right=this.parseExpression(),this.expect(L.parenR),this.exitLexicalScope(),e.body=this.parseStatement(!1),this.labels.pop(),this.finishNode(e,n)},z.parseVar=function(e,t,n){for(e.declarations=[],e.kind=n;;){var r=this.startNode();if(this.parseVarId(r,n),this.eat(L.eq)?r.init=this.parseMaybeAssign(t):"const"!==n||this.type===L._in||this.options.ecmaVersion>=6&&this.isContextual("of")?"Identifier"==r.id.type||t&&(this.type===L._in||this.isContextual("of"))?r.init=null:this.raise(this.lastTokEnd,"Complex binding patterns require an initialization value"):this.unexpected(),e.declarations.push(this.finishNode(r,"VariableDeclarator")),!this.eat(L.comma))break}return e},z.parseVarId=function(e,t){e.id=this.parseBindingAtom(t),this.checkLVal(e.id,t,!1)},z.parseFunction=function(e,t,n,r){this.initFunction(e),this.options.ecmaVersion>=6&&!r&&(e.generator=this.eat(L.star)),this.options.ecmaVersion>=8&&(e.async=!!r),t&&(e.id="nullableID"===t&&this.type!=L.name?null:this.parseIdent(),e.id&&this.checkLVal(e.id,"var"));var i=this.inGenerator,s=this.inAsync,o=this.yieldPos,a=this.awaitPos,u=this.inFunction;return this.inGenerator=e.generator,this.inAsync=e.async,this.yieldPos=0,this.awaitPos=0,this.inFunction=!0,this.enterFunctionScope(),t||(e.id=this.type==L.name?this.parseIdent():null),this.parseFunctionParams(e),this.parseFunctionBody(e,n),this.inGenerator=i,this.inAsync=s,this.yieldPos=o,this.awaitPos=a,this.inFunction=u,this.finishNode(e,t?"FunctionDeclaration":"FunctionExpression")},z.parseFunctionParams=function(e){this.expect(L.parenL),e.params=this.parseBindingList(L.parenR,!1,this.options.ecmaVersion>=8),this.checkYieldAwaitInDefaultParams()},z.parseClass=function(e,t){this.next(),this.parseClassId(e,t),this.parseClassSuper(e);var n=this.startNode(),r=!1;for(n.body=[],this.expect(L.braceL);!this.eat(L.braceR);)if(!this.eat(L.semi)){var i=this.startNode(),s=this.eat(L.star),o=!1,a=this.type===L.name&&"static"===this.value;this.parsePropertyName(i),i.static=a&&this.type!==L.parenL,i.static&&(s&&this.unexpected(),s=this.eat(L.star),this.parsePropertyName(i)),this.options.ecmaVersion>=8&&!s&&!i.computed&&"Identifier"===i.key.type&&"async"===i.key.name&&this.type!==L.parenL&&!this.canInsertSemicolon()&&(o=!0,this.parsePropertyName(i)),i.kind="method";var u=!1;if(!i.computed){var c=i.key;s||o||"Identifier"!==c.type||this.type===L.parenL||"get"!==c.name&&"set"!==c.name||(u=!0,i.kind=c.name,c=this.parsePropertyName(i)),!i.static&&("Identifier"===c.type&&"constructor"===c.name||"Literal"===c.type&&"constructor"===c.value)&&(r&&this.raise(c.start,"Duplicate constructor in the same class"),u&&this.raise(c.start,"Constructor can't have get/set modifier"),s&&this.raise(c.start,"Constructor can't be a generator"),o&&this.raise(c.start,"Constructor can't be an async method"),i.kind="constructor",r=!0)}if(this.parseClassMethod(n,i,s,o),u){var l="get"===i.kind?0:1;if(i.value.params.length!==l){var p=i.value.start;"get"===i.kind?this.raiseRecoverable(p,"getter should have no params"):this.raiseRecoverable(p,"setter should have exactly one param")}else"set"===i.kind&&"RestElement"===i.value.params[0].type&&this.raiseRecoverable(i.value.params[0].start,"Setter cannot use rest params")}}return e.body=this.finishNode(n,"ClassBody"),this.finishNode(e,t?"ClassDeclaration":"ClassExpression")},z.parseClassMethod=function(e,t,n,r){t.value=this.parseMethod(n,r),e.body.push(this.finishNode(t,"MethodDefinition"))},z.parseClassId=function(e,t){e.id=this.type===L.name?this.parseIdent():!0===t?this.unexpected():null},z.parseClassSuper=function(e){e.superClass=this.eat(L._extends)?this.parseExprSubscripts():null},z.parseExport=function(e,t){if(this.next(),this.eat(L.star))return this.expectContextual("from"),e.source=this.type===L.string?this.parseExprAtom():this.unexpected(),this.semicolon(),this.finishNode(e,"ExportAllDeclaration");if(this.eat(L._default)){this.checkExport(t,"default",this.lastTokStart);var n;if(this.type===L._function||(n=this.isAsyncFunction())){var r=this.startNode();this.next(),n&&this.next(),e.declaration=this.parseFunction(r,"nullableID",!1,n)}else if(this.type===L._class){var i=this.startNode();e.declaration=this.parseClass(i,"nullableID")}else e.declaration=this.parseMaybeAssign(),this.semicolon();return this.finishNode(e,"ExportDefaultDeclaration")}if(this.shouldParseExportStatement())e.declaration=this.parseStatement(!0),"VariableDeclaration"===e.declaration.type?this.checkVariableExport(t,e.declaration.declarations):this.checkExport(t,e.declaration.id.name,e.declaration.id.start),e.specifiers=[],e.source=null;else{if(e.declaration=null,e.specifiers=this.parseExportSpecifiers(t),this.eatContextual("from"))e.source=this.type===L.string?this.parseExprAtom():this.unexpected();else{for(var s=0,o=e.specifiers;s<o.length;s+=1){var a=o[s];this.checkUnreserved(a.local)}e.source=null}this.semicolon()}return this.finishNode(e,"ExportNamedDeclaration")},z.checkExport=function(e,t,n){e&&(a(e,t)&&this.raiseRecoverable(n,"Duplicate export '"+t+"'"),e[t]=!0)},z.checkPatternExport=function(e,t){var n=t.type;if("Identifier"==n)this.checkExport(e,t.name,t.start);else if("ObjectPattern"==n)for(var r=0,i=t.properties;r<i.length;r+=1){var s=i[r];this.checkPatternExport(e,s.value)}else if("ArrayPattern"==n)for(var o=0,a=t.elements;o<a.length;o+=1){var u=a[o];u&&this.checkPatternExport(e,u)}else"AssignmentPattern"==n?this.checkPatternExport(e,t.left):"ParenthesizedExpression"==n&&this.checkPatternExport(e,t.expression)},z.checkVariableExport=function(e,t){if(e)for(var n=0,r=t;n<r.length;n+=1){var i=r[n];this.checkPatternExport(e,i.id)}},z.shouldParseExportStatement=function(){return"var"===this.type.keyword||"const"===this.type.keyword||"class"===this.type.keyword||"function"===this.type.keyword||this.isLet()||this.isAsyncFunction()},z.parseExportSpecifiers=function(e){var t=[],n=!0;for(this.expect(L.braceL);!this.eat(L.braceR);){if(n)n=!1;else if(this.expect(L.comma),this.afterTrailingComma(L.braceR))break;var r=this.startNode();r.local=this.parseIdent(!0),r.exported=this.eatContextual("as")?this.parseIdent(!0):r.local,this.checkExport(e,r.exported.name,r.exported.start),t.push(this.finishNode(r,"ExportSpecifier"))}return t},z.parseImport=function(e){return this.next(),this.type===L.string?(e.specifiers=J,e.source=this.parseExprAtom()):(e.specifiers=this.parseImportSpecifiers(),this.expectContextual("from"),e.source=this.type===L.string?this.parseExprAtom():this.unexpected()),this.semicolon(),this.finishNode(e,"ImportDeclaration")},z.parseImportSpecifiers=function(){var e=[],t=!0;if(this.type===L.name){var n=this.startNode();if(n.local=this.parseIdent(),this.checkLVal(n.local,"let"),e.push(this.finishNode(n,"ImportDefaultSpecifier")),!this.eat(L.comma))return e}if(this.type===L.star){var r=this.startNode();return this.next(),this.expectContextual("as"),r.local=this.parseIdent(),this.checkLVal(r.local,"let"),e.push(this.finishNode(r,"ImportNamespaceSpecifier")),e}for(this.expect(L.braceL);!this.eat(L.braceR);){if(t)t=!1;else if(this.expect(L.comma),this.afterTrailingComma(L.braceR))break;var i=this.startNode();i.imported=this.parseIdent(!0),this.eatContextual("as")?i.local=this.parseIdent():(this.checkUnreserved(i.imported),i.local=i.imported),this.checkLVal(i.local,"let"),e.push(this.finishNode(i,"ImportSpecifier"))}return e},z.adaptDirectivePrologue=function(e){for(var t=0;t<e.length&&this.isDirectiveCandidate(e[t]);++t)e[t].directive=e[t].expression.raw.slice(1,-1)},z.isDirectiveCandidate=function(e){return"ExpressionStatement"===e.type&&"Literal"===e.expression.type&&"string"==typeof e.expression.value&&('"'===this.input[e.start]||"'"===this.input[e.start])};var H=V.prototype;H.toAssignable=function(e,t){if(this.options.ecmaVersion>=6&&e)switch(e.type){case"Identifier":this.inAsync&&"await"===e.name&&this.raise(e.start,"Can not use 'await' as identifier inside an async function");break;case"ObjectPattern":case"ArrayPattern":break;case"ObjectExpression":e.type="ObjectPattern";for(var n=0,r=e.properties;n<r.length;n+=1){var i=r[n];"init"!==i.kind&&this.raise(i.key.start,"Object pattern can't contain getter or setter"),this.toAssignable(i.value,t)}break;case"ArrayExpression":e.type="ArrayPattern",this.toAssignableList(e.elements,t);break;case"AssignmentExpression":if("="!==e.operator){this.raise(e.left.end,"Only '=' operator can be used for specifying default value.");break}e.type="AssignmentPattern",delete e.operator,this.toAssignable(e.left,t);case"AssignmentPattern":break;case"ParenthesizedExpression":this.toAssignable(e.expression,t);break;case"MemberExpression":if(!t)break;default:this.raise(e.start,"Assigning to rvalue")}return e},H.toAssignableList=function(e,t){var n=e.length;if(n){var r=e[n-1];if(r&&"RestElement"==r.type)--n;else if(r&&"SpreadElement"==r.type){r.type="RestElement";var i=r.argument;this.toAssignable(i,t),--n}6===this.options.ecmaVersion&&t&&r&&"RestElement"===r.type&&"Identifier"!==r.argument.type&&this.unexpected(r.argument.start)}for(var s=0;s<n;s++){var o=e[s];o&&this.toAssignable(o,t)}return e},H.parseSpread=function(e){var t=this.startNode();return this.next(),t.argument=this.parseMaybeAssign(!1,e),this.finishNode(t,"SpreadElement")},H.parseRestBinding=function(){var e=this.startNode();return this.next(),6===this.options.ecmaVersion&&this.type!==L.name&&this.unexpected(),e.argument=this.parseBindingAtom(),this.finishNode(e,"RestElement")},H.parseBindingAtom=function(){if(this.options.ecmaVersion>=6)switch(this.type){case L.bracketL:var e=this.startNode();return this.next(),e.elements=this.parseBindingList(L.bracketR,!0,!0),this.finishNode(e,"ArrayPattern");case L.braceL:return this.parseObj(!0)}return this.parseIdent()},H.parseBindingList=function(e,t,n){for(var r=[],i=!0;!this.eat(e);)if(i?i=!1:this.expect(L.comma),t&&this.type===L.comma)r.push(null);else{if(n&&this.afterTrailingComma(e))break;if(this.type===L.ellipsis){var s=this.parseRestBinding();this.parseBindingListItem(s),r.push(s),this.type===L.comma&&this.raise(this.start,"Comma is not permitted after the rest element"),this.expect(e);break}var o=this.parseMaybeDefault(this.start,this.startLoc);this.parseBindingListItem(o),r.push(o)}return r},H.parseBindingListItem=function(e){return e},H.parseMaybeDefault=function(e,t,n){if(n=n||this.parseBindingAtom(),this.options.ecmaVersion<6||!this.eat(L.eq))return n;var r=this.startNodeAt(e,t);return r.left=n,r.right=this.parseMaybeAssign(),this.finishNode(r,"AssignmentPattern")},H.checkLVal=function(e,t,n){switch(e.type){case"Identifier":this.strict&&this.reservedWordsStrictBind.test(e.name)&&this.raiseRecoverable(e.start,(t?"Binding ":"Assigning to ")+e.name+" in strict mode"),n&&(a(n,e.name)&&this.raiseRecoverable(e.start,"Argument name clash"),n[e.name]=!0),t&&"none"!==t&&(("var"===t&&!this.canDeclareVarName(e.name)||"var"!==t&&!this.canDeclareLexicalName(e.name))&&this.raiseRecoverable(e.start,"Identifier '"+e.name+"' has already been declared"),"var"===t?this.declareVarName(e.name):this.declareLexicalName(e.name));break;case"MemberExpression":t&&this.raiseRecoverable(e.start,(t?"Binding":"Assigning to")+" member expression");break;case"ObjectPattern":for(var r=0,i=e.properties;r<i.length;r+=1){var s=i[r];this.checkLVal(s.value,t,n)}break;case"ArrayPattern":for(var o=0,u=e.elements;o<u.length;o+=1){var c=u[o];c&&this.checkLVal(c,t,n)}break;case"AssignmentPattern":this.checkLVal(e.left,t,n);break;case"RestElement":this.checkLVal(e.argument,t,n);break;case"ParenthesizedExpression":this.checkLVal(e.expression,t,n);break;default:this.raise(e.start,(t?"Binding":"Assigning to")+" rvalue")}};var Y=V.prototype;Y.checkPropClash=function(e,t){if(!(this.options.ecmaVersion>=6&&(e.computed||e.method||e.shorthand))){var n,r=e.key;switch(r.type){case"Identifier":n=r.name;break;case"Literal":n=String(r.value);break;default:return}var i=e.kind;if(this.options.ecmaVersion>=6)"__proto__"===n&&"init"===i&&(t.proto&&this.raiseRecoverable(r.start,"Redefinition of __proto__ property"),t.proto=!0);else{var s=t[n="$"+n];if(s){("init"===i?this.strict&&s.init||s.get||s.set:s.init||s[i])&&this.raiseRecoverable(r.start,"Redefinition of property")}else s=t[n]={init:!1,get:!1,set:!1};s[i]=!0}}},Y.parseExpression=function(e,t){var n=this.start,r=this.startLoc,i=this.parseMaybeAssign(e,t);if(this.type===L.comma){var s=this.startNodeAt(n,r);for(s.expressions=[i];this.eat(L.comma);)s.expressions.push(this.parseMaybeAssign(e,t));return this.finishNode(s,"SequenceExpression")}return i},Y.parseMaybeAssign=function(e,t,n){if(this.inGenerator&&this.isContextual("yield"))return this.parseYield();var r=!1,i=-1,s=-1;t?(i=t.parenthesizedAssign,s=t.trailingComma,t.parenthesizedAssign=t.trailingComma=-1):(t=new p,r=!0);var o=this.start,a=this.startLoc;this.type!=L.parenL&&this.type!=L.name||(this.potentialArrowAt=this.start);var u=this.parseMaybeConditional(e,t);if(n&&(u=n.call(this,u,o,a)),this.type.isAssign){this.checkPatternErrors(t,!0),r||p.call(t);var c=this.startNodeAt(o,a);return c.operator=this.value,c.left=this.type===L.eq?this.toAssignable(u):u,t.shorthandAssign=-1,this.checkLVal(u),this.next(),c.right=this.parseMaybeAssign(e),this.finishNode(c,"AssignmentExpression")}return r&&this.checkExpressionErrors(t,!0),i>-1&&(t.parenthesizedAssign=i),s>-1&&(t.trailingComma=s),u},Y.parseMaybeConditional=function(e,t){var n=this.start,r=this.startLoc,i=this.parseExprOps(e,t);if(this.checkExpressionErrors(t))return i;if(this.eat(L.question)){var s=this.startNodeAt(n,r);return s.test=i,s.consequent=this.parseMaybeAssign(),this.expect(L.colon),s.alternate=this.parseMaybeAssign(e),this.finishNode(s,"ConditionalExpression")}return i},Y.parseExprOps=function(e,t){var n=this.start,r=this.startLoc,i=this.parseMaybeUnary(t,!1);return this.checkExpressionErrors(t)?i:i.start==n&&"ArrowFunctionExpression"===i.type?i:this.parseExprOp(i,n,r,-1,e)},Y.parseExprOp=function(e,t,n,r,i){var s=this.type.binop;if(null!=s&&(!i||this.type!==L._in)&&s>r){var o=this.type===L.logicalOR||this.type===L.logicalAND,a=this.value;this.next();var u=this.start,c=this.startLoc,l=this.parseExprOp(this.parseMaybeUnary(null,!1),u,c,s,i),p=this.buildBinary(t,n,e,l,a,o);return this.parseExprOp(p,t,n,r,i)}return e},Y.buildBinary=function(e,t,n,r,i,s){var o=this.startNodeAt(e,t);return o.left=n,o.operator=i,o.right=r,this.finishNode(o,s?"LogicalExpression":"BinaryExpression")},Y.parseMaybeUnary=function(e,t){var n,r=this.start,i=this.startLoc;if(this.inAsync&&this.isContextual("await"))n=this.parseAwait(),t=!0;else if(this.type.prefix){var s=this.startNode(),o=this.type===L.incDec;s.operator=this.value,s.prefix=!0,this.next(),s.argument=this.parseMaybeUnary(null,!0),this.checkExpressionErrors(e,!0),o?this.checkLVal(s.argument):this.strict&&"delete"===s.operator&&"Identifier"===s.argument.type?this.raiseRecoverable(s.start,"Deleting local variable in strict mode"):t=!0,n=this.finishNode(s,o?"UpdateExpression":"UnaryExpression")}else{if(n=this.parseExprSubscripts(e),this.checkExpressionErrors(e))return n;for(;this.type.postfix&&!this.canInsertSemicolon();){var a=this.startNodeAt(r,i);a.operator=this.value,a.prefix=!1,a.argument=n,this.checkLVal(n),this.next(),n=this.finishNode(a,"UpdateExpression")}}return!t&&this.eat(L.starstar)?this.buildBinary(r,i,n,this.parseMaybeUnary(null,!1),"**",!1):n},Y.parseExprSubscripts=function(e){var t=this.start,n=this.startLoc,r=this.parseExprAtom(e),i="ArrowFunctionExpression"===r.type&&")"!==this.input.slice(this.lastTokStart,this.lastTokEnd);if(this.checkExpressionErrors(e)||i)return r;var s=this.parseSubscripts(r,t,n);return e&&"MemberExpression"===s.type&&(e.parenthesizedAssign>=s.start&&(e.parenthesizedAssign=-1),e.parenthesizedBind>=s.start&&(e.parenthesizedBind=-1)),s},Y.parseSubscripts=function(e,t,n,r){for(var i=this.options.ecmaVersion>=8&&"Identifier"===e.type&&"async"===e.name&&this.lastTokEnd==e.end&&!this.canInsertSemicolon(),s=void 0;;)if((s=this.eat(L.bracketL))||this.eat(L.dot)){var o=this.startNodeAt(t,n);o.object=e,o.property=s?this.parseExpression():this.parseIdent(!0),o.computed=!!s,s&&this.expect(L.bracketR),e=this.finishNode(o,"MemberExpression")}else if(!r&&this.eat(L.parenL)){var a=new p,u=this.yieldPos,c=this.awaitPos;this.yieldPos=0,this.awaitPos=0;var l=this.parseExprList(L.parenR,this.options.ecmaVersion>=8,!1,a);if(i&&!this.canInsertSemicolon()&&this.eat(L.arrow))return this.checkPatternErrors(a,!1),this.checkYieldAwaitInDefaultParams(),this.yieldPos=u,this.awaitPos=c,this.parseArrowExpression(this.startNodeAt(t,n),l,!0);this.checkExpressionErrors(a,!0),this.yieldPos=u||this.yieldPos,this.awaitPos=c||this.awaitPos;var h=this.startNodeAt(t,n);h.callee=e,h.arguments=l,e=this.finishNode(h,"CallExpression")}else{if(this.type!==L.backQuote)return e;var f=this.startNodeAt(t,n);f.tag=e,f.quasi=this.parseTemplate({isTagged:!0}),e=this.finishNode(f,"TaggedTemplateExpression")}},Y.parseExprAtom=function(e){var t,n=this.potentialArrowAt==this.start;switch(this.type){case L._super:return this.inFunction||this.raise(this.start,"'super' outside of function or class"),t=this.startNode(),this.next(),this.type!==L.dot&&this.type!==L.bracketL&&this.type!==L.parenL&&this.unexpected(),this.finishNode(t,"Super");case L._this:return t=this.startNode(),this.next(),this.finishNode(t,"ThisExpression");case L.name:var r=this.start,i=this.startLoc,s=this.parseIdent(this.type!==L.name);if(this.options.ecmaVersion>=8&&"async"===s.name&&!this.canInsertSemicolon()&&this.eat(L._function))return this.parseFunction(this.startNodeAt(r,i),!1,!1,!0);if(n&&!this.canInsertSemicolon()){if(this.eat(L.arrow))return this.parseArrowExpression(this.startNodeAt(r,i),[s],!1);if(this.options.ecmaVersion>=8&&"async"===s.name&&this.type===L.name)return s=this.parseIdent(),!this.canInsertSemicolon()&&this.eat(L.arrow)||this.unexpected(),this.parseArrowExpression(this.startNodeAt(r,i),[s],!0)}return s;case L.regexp:var o=this.value;return t=this.parseLiteral(o.value),t.regex={pattern:o.pattern,flags:o.flags},t;case L.num:case L.string:return this.parseLiteral(this.value);case L._null:case L._true:case L._false:return t=this.startNode(),t.value=this.type===L._null?null:this.type===L._true,t.raw=this.type.keyword,this.next(),this.finishNode(t,"Literal");case L.parenL:var a=this.start,u=this.parseParenAndDistinguishExpression(n);return e&&(e.parenthesizedAssign<0&&!this.isSimpleAssignTarget(u)&&(e.parenthesizedAssign=a),e.parenthesizedBind<0&&(e.parenthesizedBind=a)),u;case L.bracketL:return t=this.startNode(),this.next(),t.elements=this.parseExprList(L.bracketR,!0,!0,e),this.finishNode(t,"ArrayExpression");case L.braceL:return this.parseObj(!1,e);case L._function:return t=this.startNode(),this.next(),this.parseFunction(t,!1);case L._class:return this.parseClass(this.startNode(),!1);case L._new:return this.parseNew();case L.backQuote:return this.parseTemplate();default:this.unexpected()}},Y.parseLiteral=function(e){var t=this.startNode();return t.value=e,t.raw=this.input.slice(this.start,this.end),this.next(),this.finishNode(t,"Literal")},Y.parseParenExpression=function(){this.expect(L.parenL);var e=this.parseExpression();return this.expect(L.parenR),e},Y.parseParenAndDistinguishExpression=function(e){var t,n=this.start,r=this.startLoc,i=this.options.ecmaVersion>=8;if(this.options.ecmaVersion>=6){this.next();var s,o,a=this.start,u=this.startLoc,c=[],l=!0,h=!1,f=new p,d=this.yieldPos,y=this.awaitPos;for(this.yieldPos=0,this.awaitPos=0;this.type!==L.parenR;){if(l?l=!1:this.expect(L.comma),i&&this.afterTrailingComma(L.parenR,!0)){h=!0;break}if(this.type===L.ellipsis){s=this.start,c.push(this.parseParenItem(this.parseRestBinding())),this.type===L.comma&&this.raise(this.start,"Comma is not permitted after the rest element");break}this.type!==L.parenL||o||(o=this.start),c.push(this.parseMaybeAssign(!1,f,this.parseParenItem))}var m=this.start,g=this.startLoc;if(this.expect(L.parenR),e&&!this.canInsertSemicolon()&&this.eat(L.arrow))return this.checkPatternErrors(f,!1),this.checkYieldAwaitInDefaultParams(),o&&this.unexpected(o),this.yieldPos=d,this.awaitPos=y,this.parseParenArrowList(n,r,c);c.length&&!h||this.unexpected(this.lastTokStart),s&&this.unexpected(s),this.checkExpressionErrors(f,!0),this.yieldPos=d||this.yieldPos,this.awaitPos=y||this.awaitPos,c.length>1?((t=this.startNodeAt(a,u)).expressions=c,this.finishNodeAt(t,"SequenceExpression",m,g)):t=c[0]}else t=this.parseParenExpression();if(this.options.preserveParens){var v=this.startNodeAt(n,r);return v.expression=t,this.finishNode(v,"ParenthesizedExpression")}return t},Y.parseParenItem=function(e){return e},Y.parseParenArrowList=function(e,t,n){return this.parseArrowExpression(this.startNodeAt(e,t),n)};var Q=[];Y.parseNew=function(){var e=this.startNode(),t=this.parseIdent(!0);if(this.options.ecmaVersion>=6&&this.eat(L.dot))return e.meta=t,e.property=this.parseIdent(!0),"target"!==e.property.name&&this.raiseRecoverable(e.property.start,"The only valid meta property for new is new.target"),this.inFunction||this.raiseRecoverable(e.start,"new.target can only be used in functions"),this.finishNode(e,"MetaProperty");var n=this.start,r=this.startLoc;return e.callee=this.parseSubscripts(this.parseExprAtom(),n,r,!0),this.eat(L.parenL)?e.arguments=this.parseExprList(L.parenR,this.options.ecmaVersion>=8,!1):e.arguments=Q,this.finishNode(e,"NewExpression")},Y.parseTemplateElement=function(e){var t=e.isTagged,n=this.startNode();return this.type===L.invalidTemplate?(t||this.raiseRecoverable(this.start,"Bad escape sequence in untagged template literal"),n.value={raw:this.value,cooked:null}):n.value={raw:this.input.slice(this.start,this.end).replace(/\r\n?/g,"\n"),cooked:this.value},this.next(),n.tail=this.type===L.backQuote,this.finishNode(n,"TemplateElement")},Y.parseTemplate=function(e){void 0===e&&(e={});var t=e.isTagged;void 0===t&&(t=!1);var n=this.startNode();this.next(),n.expressions=[];var r=this.parseTemplateElement({isTagged:t});for(n.quasis=[r];!r.tail;)this.expect(L.dollarBraceL),n.expressions.push(this.parseExpression()),this.expect(L.braceR),n.quasis.push(r=this.parseTemplateElement({isTagged:t}));return this.next(),this.finishNode(n,"TemplateLiteral")},Y.isAsyncProp=function(e){return!e.computed&&"Identifier"===e.key.type&&"async"===e.key.name&&(this.type===L.name||this.type===L.num||this.type===L.string||this.type===L.bracketL||this.type.keyword)&&!O.test(this.input.slice(this.lastTokEnd,this.start))},Y.parseObj=function(e,t){var n=this.startNode(),r=!0,i={};for(n.properties=[],this.next();!this.eat(L.braceR);){if(r)r=!1;else if(this.expect(L.comma),this.afterTrailingComma(L.braceR))break;var s=this.parseProperty(e,t);this.checkPropClash(s,i),n.properties.push(s)}return this.finishNode(n,e?"ObjectPattern":"ObjectExpression")},Y.parseProperty=function(e,t){var n,r,i,s,o=this.startNode();return this.options.ecmaVersion>=6&&(o.method=!1,o.shorthand=!1,(e||t)&&(i=this.start,s=this.startLoc),e||(n=this.eat(L.star))),this.parsePropertyName(o),!e&&this.options.ecmaVersion>=8&&!n&&this.isAsyncProp(o)?(r=!0,this.parsePropertyName(o,t)):r=!1,this.parsePropertyValue(o,e,n,r,i,s,t),this.finishNode(o,"Property")},Y.parsePropertyValue=function(e,t,n,r,i,s,o){if((n||r)&&this.type===L.colon&&this.unexpected(),this.eat(L.colon))e.value=t?this.parseMaybeDefault(this.start,this.startLoc):this.parseMaybeAssign(!1,o),e.kind="init";else if(this.options.ecmaVersion>=6&&this.type===L.parenL)t&&this.unexpected(),e.kind="init",e.method=!0,e.value=this.parseMethod(n,r);else if(t||!(this.options.ecmaVersion>=5)||e.computed||"Identifier"!==e.key.type||"get"!==e.key.name&&"set"!==e.key.name||this.type==L.comma||this.type==L.braceR)this.options.ecmaVersion>=6&&!e.computed&&"Identifier"===e.key.type?(this.checkUnreserved(e.key),e.kind="init",t?e.value=this.parseMaybeDefault(i,s,e.key):this.type===L.eq&&o?(o.shorthandAssign<0&&(o.shorthandAssign=this.start),e.value=this.parseMaybeDefault(i,s,e.key)):e.value=e.key,e.shorthand=!0):this.unexpected();else{(n||r)&&this.unexpected(),e.kind=e.key.name,this.parsePropertyName(e),e.value=this.parseMethod(!1);var a="get"===e.kind?0:1;if(e.value.params.length!==a){var u=e.value.start;"get"===e.kind?this.raiseRecoverable(u,"getter should have no params"):this.raiseRecoverable(u,"setter should have exactly one param")}else"set"===e.kind&&"RestElement"===e.value.params[0].type&&this.raiseRecoverable(e.value.params[0].start,"Setter cannot use rest params")}},Y.parsePropertyName=function(e){if(this.options.ecmaVersion>=6){if(this.eat(L.bracketL))return e.computed=!0,e.key=this.parseMaybeAssign(),this.expect(L.bracketR),e.key;e.computed=!1}return e.key=this.type===L.num||this.type===L.string?this.parseExprAtom():this.parseIdent(!0)},Y.initFunction=function(e){e.id=null,this.options.ecmaVersion>=6&&(e.generator=!1,e.expression=!1),this.options.ecmaVersion>=8&&(e.async=!1)},Y.parseMethod=function(e,t){var n=this.startNode(),r=this.inGenerator,i=this.inAsync,s=this.yieldPos,o=this.awaitPos,a=this.inFunction;return this.initFunction(n),this.options.ecmaVersion>=6&&(n.generator=e),this.options.ecmaVersion>=8&&(n.async=!!t),this.inGenerator=n.generator,this.inAsync=n.async,this.yieldPos=0,this.awaitPos=0,this.inFunction=!0,this.enterFunctionScope(),this.expect(L.parenL),n.params=this.parseBindingList(L.parenR,!1,this.options.ecmaVersion>=8),this.checkYieldAwaitInDefaultParams(),this.parseFunctionBody(n,!1),this.inGenerator=r,this.inAsync=i,this.yieldPos=s,this.awaitPos=o,this.inFunction=a,this.finishNode(n,"FunctionExpression")},Y.parseArrowExpression=function(e,t,n){var r=this.inGenerator,i=this.inAsync,s=this.yieldPos,o=this.awaitPos,a=this.inFunction;return this.enterFunctionScope(),this.initFunction(e),this.options.ecmaVersion>=8&&(e.async=!!n),this.inGenerator=!1,this.inAsync=e.async,this.yieldPos=0,this.awaitPos=0,this.inFunction=!0,e.params=this.toAssignableList(t,!0),this.parseFunctionBody(e,!0),this.inGenerator=r,this.inAsync=i,this.yieldPos=s,this.awaitPos=o,this.inFunction=a,this.finishNode(e,"ArrowFunctionExpression")},Y.parseFunctionBody=function(e,t){var n=t&&this.type!==L.braceL,r=this.strict,i=!1;if(n)e.body=this.parseMaybeAssign(),e.expression=!0,this.checkParams(e,!1);else{var s=this.options.ecmaVersion>=7&&!this.isSimpleParamList(e.params);r&&!s||(i=this.strictDirective(this.end))&&s&&this.raiseRecoverable(e.start,"Illegal 'use strict' directive in function with non-simple parameter list");var o=this.labels;this.labels=[],i&&(this.strict=!0),this.checkParams(e,!r&&!i&&!t&&this.isSimpleParamList(e.params)),e.body=this.parseBlock(!1),e.expression=!1,this.adaptDirectivePrologue(e.body.body),this.labels=o}this.exitFunctionScope(),this.strict&&e.id&&this.checkLVal(e.id,"none"),this.strict=r},Y.isSimpleParamList=function(e){for(var t=0,n=e;t<n.length;t+=1){if("Identifier"!==n[t].type)return!1}return!0},Y.checkParams=function(e,t){for(var n={},r=0,i=e.params;r<i.length;r+=1){var s=i[r];this.checkLVal(s,"var",t?null:n)}},Y.parseExprList=function(e,t,n,r){for(var i=[],s=!0;!this.eat(e);){if(s)s=!1;else if(this.expect(L.comma),t&&this.afterTrailingComma(e))break;var o=void 0;n&&this.type===L.comma?o=null:this.type===L.ellipsis?(o=this.parseSpread(r),r&&this.type===L.comma&&r.trailingComma<0&&(r.trailingComma=this.start)):o=this.parseMaybeAssign(!1,r),i.push(o)}return i},Y.checkUnreserved=function(e){var t=e.start,n=e.end,r=e.name;if(this.inGenerator&&"yield"===r&&this.raiseRecoverable(t,"Can not use 'yield' as identifier inside a generator"),this.inAsync&&"await"===r&&this.raiseRecoverable(t,"Can not use 'await' as identifier inside an async function"),this.isKeyword(r)&&this.raise(t,"Unexpected keyword '"+r+"'"),!(this.options.ecmaVersion<6&&-1!=this.input.slice(t,n).indexOf("\\"))){(this.strict?this.reservedWordsStrict:this.reservedWords).test(r)&&this.raiseRecoverable(t,"The keyword '"+r+"' is reserved")}},Y.parseIdent=function(e,t){var n=this.startNode();return e&&"never"==this.options.allowReserved&&(e=!1),this.type===L.name?n.name=this.value:this.type.keyword?(n.name=this.type.keyword,"class"!==n.name&&"function"!==n.name||this.lastTokEnd===this.lastTokStart+1&&46===this.input.charCodeAt(this.lastTokStart)||this.context.pop()):this.unexpected(),this.next(),this.finishNode(n,"Identifier"),e||this.checkUnreserved(n),n},Y.parseYield=function(){this.yieldPos||(this.yieldPos=this.start);var e=this.startNode();return this.next(),this.type==L.semi||this.canInsertSemicolon()||this.type!=L.star&&!this.type.startsExpr?(e.delegate=!1,e.argument=null):(e.delegate=this.eat(L.star),e.argument=this.parseMaybeAssign()),this.finishNode(e,"YieldExpression")},Y.parseAwait=function(){this.awaitPos||(this.awaitPos=this.start);var e=this.startNode();return this.next(),e.argument=this.parseMaybeUnary(null,!0),this.finishNode(e,"AwaitExpression")};var X=V.prototype;X.raise=function(e,t){var n=u(this.input,e);t+=" ("+n.line+":"+n.column+")";var r=new SyntaxError(t);throw r.pos=e,r.loc=n,r.raisedAt=this.pos,r},X.raiseRecoverable=X.raise,X.curPosition=function(){if(this.options.locations)return new I(this.curLine,this.pos-this.lineStart)};var Z=V.prototype,K=Object.assign||function(e){for(var t=[],n=arguments.length-1;n-- >0;)t[n]=arguments[n+1];for(var r=0,i=t;r<i.length;r+=1){var s=i[r];for(var o in s)a(s,o)&&(e[o]=s[o])}return e};Z.enterFunctionScope=function(){this.scopeStack.push({var:{},lexical:{},childVar:{},parentLexical:{}})},Z.exitFunctionScope=function(){this.scopeStack.pop()},Z.enterLexicalScope=function(){var e=this.scopeStack[this.scopeStack.length-1],t={var:{},lexical:{},childVar:{},parentLexical:{}};this.scopeStack.push(t),K(t.parentLexical,e.lexical,e.parentLexical)},Z.exitLexicalScope=function(){var e=this.scopeStack.pop(),t=this.scopeStack[this.scopeStack.length-1];K(t.childVar,e.var,e.childVar)},Z.canDeclareVarName=function(e){var t=this.scopeStack[this.scopeStack.length-1];return!a(t.lexical,e)&&!a(t.parentLexical,e)},Z.canDeclareLexicalName=function(e){var t=this.scopeStack[this.scopeStack.length-1];return!a(t.lexical,e)&&!a(t.var,e)&&!a(t.childVar,e)},Z.declareVarName=function(e){this.scopeStack[this.scopeStack.length-1].var[e]=!0},Z.declareLexicalName=function(e){this.scopeStack[this.scopeStack.length-1].lexical[e]=!0};var ee=function(e,t,n){this.type="",this.start=t,this.end=0,e.options.locations&&(this.loc=new j(e,n)),e.options.directSourceFile&&(this.sourceFile=e.options.directSourceFile),e.options.ranges&&(this.range=[t,0])},te=V.prototype;te.startNode=function(){return new ee(this,this.start,this.startLoc)},te.startNodeAt=function(e,t){return new ee(this,e,t)},te.finishNode=function(e,t){return h.call(this,e,t,this.lastTokEnd,this.lastTokEndLoc)},te.finishNodeAt=function(e,t,n,r){return h.call(this,e,t,n,r)};var ne=function(e,t,n,r,i){this.token=e,this.isExpr=!!t,this.preserveSpace=!!n,this.override=r,this.generator=!!i},re={b_stat:new ne("{",!1),b_expr:new ne("{",!0),b_tmpl:new ne("${",!1),p_stat:new ne("(",!1),p_expr:new ne("(",!0),q_tmpl:new ne("`",!0,!0,function(e){return e.tryReadTemplateToken()}),f_stat:new ne("function",!1),f_expr:new ne("function",!0),f_expr_gen:new ne("function",!0,!1,null,!0),f_gen:new ne("function",!1,!1,null,!0)},ie=V.prototype;ie.initialContext=function(){return[re.b_stat]},ie.braceIsBlock=function(e){var t=this.curContext();return t===re.f_expr||t===re.f_stat||(e!==L.colon||t!==re.b_stat&&t!==re.b_expr?e===L._return||e==L.name&&this.exprAllowed?O.test(this.input.slice(this.lastTokEnd,this.start)):e===L._else||e===L.semi||e===L.eof||e===L.parenR||e==L.arrow||(e==L.braceL?t===re.b_stat:e!=L._var&&e!=L.name&&!this.exprAllowed):!t.isExpr)},ie.inGeneratorContext=function(){for(var e=this.context.length-1;e>=1;e--){var t=this.context[e];if("function"===t.token)return t.generator}return!1},ie.updateContext=function(e){var t,n=this.type;n.keyword&&e==L.dot?this.exprAllowed=!1:(t=n.updateContext)?t.call(this,e):this.exprAllowed=n.beforeExpr},L.parenR.updateContext=L.braceR.updateContext=function(){if(1!=this.context.length){var e=this.context.pop();e===re.b_stat&&"function"===this.curContext().token&&(e=this.context.pop()),this.exprAllowed=!e.isExpr}else this.exprAllowed=!0},L.braceL.updateContext=function(e){this.context.push(this.braceIsBlock(e)?re.b_stat:re.b_expr),this.exprAllowed=!0},L.dollarBraceL.updateContext=function(){this.context.push(re.b_tmpl),this.exprAllowed=!0},L.parenL.updateContext=function(e){var t=e===L._if||e===L._for||e===L._with||e===L._while;this.context.push(t?re.p_stat:re.p_expr),this.exprAllowed=!0},L.incDec.updateContext=function(){},L._function.updateContext=L._class.updateContext=function(e){e.beforeExpr&&e!==L.semi&&e!==L._else&&(e!==L.colon&&e!==L.braceL||this.curContext()!==re.b_stat)?this.context.push(re.f_expr):this.context.push(re.f_stat),this.exprAllowed=!1},L.backQuote.updateContext=function(){this.curContext()===re.q_tmpl?this.context.pop():this.context.push(re.q_tmpl),this.exprAllowed=!1},L.star.updateContext=function(e){if(e==L._function){var t=this.context.length-1;this.context[t]===re.f_expr?this.context[t]=re.f_expr_gen:this.context[t]=re.f_gen}this.exprAllowed=!0},L.name.updateContext=function(e){var t=!1;this.options.ecmaVersion>=6&&("of"==this.value&&!this.exprAllowed||"yield"==this.value&&this.inGeneratorContext())&&(t=!0),this.exprAllowed=t};var se=function(e){this.type=e.type,this.value=e.value,this.start=e.start,this.end=e.end,e.options.locations&&(this.loc=new j(e,e.startLoc,e.endLoc)),e.options.ranges&&(this.range=[e.start,e.end])},oe=V.prototype,ae="object"==typeof Packages&&"[object JavaPackage]"==Object.prototype.toString.call(Packages);oe.next=function(){this.options.onToken&&this.options.onToken(new se(this)),this.lastTokEnd=this.end,this.lastTokStart=this.start,this.lastTokEndLoc=this.endLoc,this.lastTokStartLoc=this.startLoc,this.nextToken()},oe.getToken=function(){return this.next(),new se(this)},"undefined"!=typeof Symbol&&(oe[Symbol.iterator]=function(){var e=this;return{next:function(){var t=e.getToken();return{done:t.type===L.eof,value:t}}}}),oe.curContext=function(){return this.context[this.context.length-1]},oe.nextToken=function(){var e=this.curContext();return e&&e.preserveSpace||this.skipSpace(),this.start=this.pos,this.options.locations&&(this.startLoc=this.curPosition()),this.pos>=this.input.length?this.finishToken(L.eof):e.override?e.override(this):void this.readToken(this.fullCharCodeAtPos())},oe.readToken=function(e){return n(e,this.options.ecmaVersion>=6)||92===e?this.readWord():this.getTokenFromCode(e)},oe.fullCharCodeAtPos=function(){var e=this.input.charCodeAt(this.pos);if(e<=55295||e>=57344)return e;return(e<<10)+this.input.charCodeAt(this.pos+1)-56613888},oe.skipBlockComment=function(){var e=this.options.onComment&&this.curPosition(),t=this.pos,n=this.input.indexOf("*/",this.pos+=2);if(-1===n&&this.raise(this.pos-2,"Unterminated comment"),this.pos=n+2,this.options.locations){T.lastIndex=t;for(var r;(r=T.exec(this.input))&&r.index<this.pos;)++this.curLine,this.lineStart=r.index+r[0].length}this.options.onComment&&this.options.onComment(!0,this.input.slice(t+2,n),t,this.pos,e,this.curPosition())},oe.skipLineComment=function(e){for(var t=this.pos,n=this.options.onComment&&this.curPosition(),r=this.input.charCodeAt(this.pos+=e);this.pos<this.input.length&&!o(r);)r=this.input.charCodeAt(++this.pos);this.options.onComment&&this.options.onComment(!1,this.input.slice(t+e,this.pos),t,this.pos,n,this.curPosition())},oe.skipSpace=function(){e:for(;this.pos<this.input.length;){var e=this.input.charCodeAt(this.pos);switch(e){case 32:case 160:++this.pos;break;case 13:10===this.input.charCodeAt(this.pos+1)&&++this.pos;case 10:case 8232:case 8233:++this.pos,this.options.locations&&(++this.curLine,this.lineStart=this.pos);break;case 47:switch(this.input.charCodeAt(this.pos+1)){case 42:this.skipBlockComment();break;case 47:this.skipLineComment(2);break;default:break e}break;default:if(!(e>8&&e<14||e>=5760&&N.test(String.fromCharCode(e))))break e;++this.pos}}},oe.finishToken=function(e,t){this.end=this.pos,this.options.locations&&(this.endLoc=this.curPosition());var n=this.type;this.type=e,this.value=t,this.updateContext(n)},oe.readToken_dot=function(){var e=this.input.charCodeAt(this.pos+1);if(e>=48&&e<=57)return this.readNumber(!0);var t=this.input.charCodeAt(this.pos+2);return this.options.ecmaVersion>=6&&46===e&&46===t?(this.pos+=3,this.finishToken(L.ellipsis)):(++this.pos,this.finishToken(L.dot))},oe.readToken_slash=function(){var e=this.input.charCodeAt(this.pos+1);return this.exprAllowed?(++this.pos,this.readRegexp()):61===e?this.finishOp(L.assign,2):this.finishOp(L.slash,1)},oe.readToken_mult_modulo_exp=function(e){var t=this.input.charCodeAt(this.pos+1),n=1,r=42===e?L.star:L.modulo;return this.options.ecmaVersion>=7&&42==e&&42===t&&(++n,r=L.starstar,t=this.input.charCodeAt(this.pos+2)),61===t?this.finishOp(L.assign,n+1):this.finishOp(r,n)},oe.readToken_pipe_amp=function(e){var t=this.input.charCodeAt(this.pos+1);return t===e?this.finishOp(124===e?L.logicalOR:L.logicalAND,2):61===t?this.finishOp(L.assign,2):this.finishOp(124===e?L.bitwiseOR:L.bitwiseAND,1)},oe.readToken_caret=function(){return 61===this.input.charCodeAt(this.pos+1)?this.finishOp(L.assign,2):this.finishOp(L.bitwiseXOR,1)},oe.readToken_plus_min=function(e){var t=this.input.charCodeAt(this.pos+1);return t===e?45!=t||this.inModule||62!=this.input.charCodeAt(this.pos+2)||0!==this.lastTokEnd&&!O.test(this.input.slice(this.lastTokEnd,this.pos))?this.finishOp(L.incDec,2):(this.skipLineComment(3),this.skipSpace(),this.nextToken()):61===t?this.finishOp(L.assign,2):this.finishOp(L.plusMin,1)},oe.readToken_lt_gt=function(e){var t=this.input.charCodeAt(this.pos+1),n=1;return t===e?(n=62===e&&62===this.input.charCodeAt(this.pos+2)?3:2,61===this.input.charCodeAt(this.pos+n)?this.finishOp(L.assign,n+1):this.finishOp(L.bitShift,n)):33!=t||60!=e||this.inModule||45!=this.input.charCodeAt(this.pos+2)||45!=this.input.charCodeAt(this.pos+3)?(61===t&&(n=2),this.finishOp(L.relational,n)):(this.skipLineComment(4),this.skipSpace(),this.nextToken())},oe.readToken_eq_excl=function(e){var t=this.input.charCodeAt(this.pos+1);return 61===t?this.finishOp(L.equality,61===this.input.charCodeAt(this.pos+2)?3:2):61===e&&62===t&&this.options.ecmaVersion>=6?(this.pos+=2,this.finishToken(L.arrow)):this.finishOp(61===e?L.eq:L.prefix,1)},oe.getTokenFromCode=function(e){switch(e){case 46:return this.readToken_dot();case 40:return++this.pos,this.finishToken(L.parenL);case 41:return++this.pos,this.finishToken(L.parenR);case 59:return++this.pos,this.finishToken(L.semi);case 44:return++this.pos,this.finishToken(L.comma);case 91:return++this.pos,this.finishToken(L.bracketL);case 93:return++this.pos,this.finishToken(L.bracketR);case 123:return++this.pos,this.finishToken(L.braceL);case 125:return++this.pos,this.finishToken(L.braceR);case 58:return++this.pos,this.finishToken(L.colon);case 63:return++this.pos,this.finishToken(L.question);case 96:if(this.options.ecmaVersion<6)break;return++this.pos,this.finishToken(L.backQuote);case 48:var t=this.input.charCodeAt(this.pos+1);if(120===t||88===t)return this.readRadixNumber(16);if(this.options.ecmaVersion>=6){if(111===t||79===t)return this.readRadixNumber(8);if(98===t||66===t)return this.readRadixNumber(2)}case 49:case 50:case 51:case 52:case 53:case 54:case 55:case 56:case 57:return this.readNumber(!1);case 34:case 39:return this.readString(e);case 47:return this.readToken_slash();case 37:case 42:return this.readToken_mult_modulo_exp(e);case 124:case 38:return this.readToken_pipe_amp(e);case 94:return this.readToken_caret();case 43:case 45:return this.readToken_plus_min(e);case 60:case 62:return this.readToken_lt_gt(e);case 61:case 33:return this.readToken_eq_excl(e);case 126:return this.finishOp(L.prefix,1)}this.raise(this.pos,"Unexpected character '"+d(e)+"'")},oe.finishOp=function(e,t){var n=this.input.slice(this.pos,this.pos+t);return this.pos+=t,this.finishToken(e,n)};var ue=!!f("￿","u");oe.readRegexp=function(){for(var e,t,n=this,r=this.pos;;){n.pos>=n.input.length&&n.raise(r,"Unterminated regular expression");var i=n.input.charAt(n.pos);if(O.test(i)&&n.raise(r,"Unterminated regular expression"),e)e=!1;else{if("["===i)t=!0;else if("]"===i&&t)t=!1;else if("/"===i&&!t)break;e="\\"===i}++n.pos}var s=this.input.slice(r,this.pos);++this.pos;var o=this.readWord1(),a=s,u="";if(o){var c=/^[gim]*$/;this.options.ecmaVersion>=6&&(c=/^[gimuy]*$/),c.test(o)||this.raise(r,"Invalid regular expression flag"),o.indexOf("u")>=0&&(ue?u="u":(a=(a=a.replace(/\\u\{([0-9a-fA-F]+)\}/g,function(e,t,i){return(t=Number("0x"+t))>1114111&&n.raise(r+i+3,"Code point out of bounds"),"x"})).replace(/\\u([a-fA-F0-9]{4})|[\uD800-\uDBFF][\uDC00-\uDFFF]/g,"x"),u=u.replace("u","")))}var l=null;return ae||(f(a,u,r,this),l=f(s,o)),this.finishToken(L.regexp,{pattern:s,flags:o,value:l})},oe.readInt=function(e,t){for(var n=this.pos,r=0,i=0,s=null==t?1/0:t;i<s;++i){var o=this.input.charCodeAt(this.pos),a=void 0;if((a=o>=97?o-97+10:o>=65?o-65+10:o>=48&&o<=57?o-48:1/0)>=e)break;++this.pos,r=r*e+a}return this.pos===n||null!=t&&this.pos-n!==t?null:r},oe.readRadixNumber=function(e){this.pos+=2;var t=this.readInt(e);return null==t&&this.raise(this.start+2,"Expected number in radix "+e),n(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number"),this.finishToken(L.num,t)},oe.readNumber=function(e){var t=this.pos,r=!1,i=48===this.input.charCodeAt(this.pos);e||null!==this.readInt(10)||this.raise(t,"Invalid number"),i&&this.pos==t+1&&(i=!1);var s=this.input.charCodeAt(this.pos);46!==s||i||(++this.pos,this.readInt(10),r=!0,s=this.input.charCodeAt(this.pos)),69!==s&&101!==s||i||(43!==(s=this.input.charCodeAt(++this.pos))&&45!==s||++this.pos,null===this.readInt(10)&&this.raise(t,"Invalid number"),r=!0),n(this.fullCharCodeAtPos())&&this.raise(this.pos,"Identifier directly after number");var o,a=this.input.slice(t,this.pos);return r?o=parseFloat(a):i&&1!==a.length?this.strict?this.raise(t,"Invalid number"):o=/[89]/.test(a)?parseInt(a,10):parseInt(a,8):o=parseInt(a,10),this.finishToken(L.num,o)},oe.readCodePoint=function(){var e;if(123===this.input.charCodeAt(this.pos)){this.options.ecmaVersion<6&&this.unexpected();var t=++this.pos;e=this.readHexChar(this.input.indexOf("}",this.pos)-this.pos),++this.pos,e>1114111&&this.invalidStringToken(t,"Code point out of bounds")}else e=this.readHexChar(4);return e},oe.readString=function(e){for(var t="",n=++this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated string constant");var r=this.input.charCodeAt(this.pos);if(r===e)break;92===r?(t+=this.input.slice(n,this.pos),t+=this.readEscapedChar(!1),n=this.pos):(o(r)&&this.raise(this.start,"Unterminated string constant"),++this.pos)}return t+=this.input.slice(n,this.pos++),this.finishToken(L.string,t)};var ce={};oe.tryReadTemplateToken=function(){this.inTemplateElement=!0;try{this.readTmplToken()}catch(e){if(e!==ce)throw e;this.readInvalidTemplateToken()}this.inTemplateElement=!1},oe.invalidStringToken=function(e,t){if(this.inTemplateElement&&this.options.ecmaVersion>=9)throw ce;this.raise(e,t)},oe.readTmplToken=function(){for(var e="",t=this.pos;;){this.pos>=this.input.length&&this.raise(this.start,"Unterminated template");var n=this.input.charCodeAt(this.pos);if(96===n||36===n&&123===this.input.charCodeAt(this.pos+1))return this.pos!==this.start||this.type!==L.template&&this.type!==L.invalidTemplate?(e+=this.input.slice(t,this.pos),this.finishToken(L.template,e)):36===n?(this.pos+=2,this.finishToken(L.dollarBraceL)):(++this.pos,this.finishToken(L.backQuote));if(92===n)e+=this.input.slice(t,this.pos),e+=this.readEscapedChar(!0),t=this.pos;else if(o(n)){switch(e+=this.input.slice(t,this.pos),++this.pos,n){case 13:10===this.input.charCodeAt(this.pos)&&++this.pos;case 10:e+="\n";break;default:e+=String.fromCharCode(n)}this.options.locations&&(++this.curLine,this.lineStart=this.pos),t=this.pos}else++this.pos}},oe.readInvalidTemplateToken=function(){for(;this.pos<this.input.length;this.pos++)switch(this.input[this.pos]){case"\\":++this.pos;break;case"$":if("{"!==this.input[this.pos+1])break;case"`":return this.finishToken(L.invalidTemplate,this.input.slice(this.start,this.pos))}this.raise(this.start,"Unterminated template")},oe.readEscapedChar=function(e){var t=this.input.charCodeAt(++this.pos);switch(++this.pos,t){case 110:return"\n";case 114:return"\r";case 120:return String.fromCharCode(this.readHexChar(2));case 117:return d(this.readCodePoint());case 116:return"\t";case 98:return"\b";case 118:return"\v";case 102:return"\f";case 13:10===this.input.charCodeAt(this.pos)&&++this.pos;case 10:return this.options.locations&&(this.lineStart=this.pos,++this.curLine),"";default:if(t>=48&&t<=55){var n=this.input.substr(this.pos-1,3).match(/^[0-7]+/)[0],r=parseInt(n,8);return r>255&&(n=n.slice(0,-1),r=parseInt(n,8)),"0"!==n&&(this.strict||e)&&this.invalidStringToken(this.pos-2,"Octal literal in strict mode"),this.pos+=n.length-1,String.fromCharCode(r)}return String.fromCharCode(t)}},oe.readHexChar=function(e){var t=this.pos,n=this.readInt(16,e);return null===n&&this.invalidStringToken(t,"Bad character escape sequence"),n},oe.readWord1=function(){this.containsEsc=!1;for(var e="",t=!0,i=this.pos,s=this.options.ecmaVersion>=6;this.pos<this.input.length;){var o=this.fullCharCodeAtPos();if(r(o,s))this.pos+=o<=65535?1:2;else{if(92!==o)break;this.containsEsc=!0,e+=this.input.slice(i,this.pos);var a=this.pos;117!=this.input.charCodeAt(++this.pos)&&this.invalidStringToken(this.pos,"Expecting Unicode escape sequence \\uXXXX"),++this.pos;var u=this.readCodePoint();(t?n:r)(u,s)||this.invalidStringToken(a,"Invalid Unicode escape"),e+=d(u),i=this.pos}t=!1}return e+this.input.slice(i,this.pos)},oe.readWord=function(){var e=this.readWord1(),t=L.name;return this.keywords.test(e)&&(this.containsEsc&&this.raiseRecoverable(this.start,"Escape sequence in keyword "+e),t=_[e]),this.finishToken(t,e)};e.version="5.2.1",e.parse=function(e,t){return new V(t,e).parse()},e.parseExpressionAt=function(e,t,n){var r=new V(n,e,t);return r.nextToken(),r.parseExpression()},e.tokenizer=function(e,t){return new V(t,e)},e.addLooseExports=function(t,n,r){e.parse_dammit=t,e.LooseParser=n,e.pluginsLoose=r},e.Parser=V,e.plugins=M,e.defaultOptions=D,e.Position=I,e.SourceLocation=j,e.getLineInfo=u,e.Node=ee,e.TokenType=k,e.tokTypes=L,e.keywordTypes=_,e.TokContext=ne,e.tokContexts=re,e.isIdentifierChar=r,e.isIdentifierStart=n,e.Token=se,e.isNewLine=o,e.lineBreak=O,e.lineBreakG=T,e.nonASCIIwhitespace=N,Object.defineProperty(e,"__esModule",{value:!0})})},{}],5:[function(e,t,n){!function(e,r){"object"==typeof n&&void 0!==t?r(n):"function"==typeof define&&define.amd?define(["exports"],r):r((e.acorn=e.acorn||{},e.acorn.walk=e.acorn.walk||{}))}(this,function(e){"use strict";function t(e){return"string"==typeof e?function(t){return t==e}:e||function(){return!0}}function n(e,t,n){n(e,t)}function r(e,t,n){}var i=function(e,t){this.node=e,this.state=t},s=Object.create||function(e){function t(){}return t.prototype=e,new t},o={};o.Program=o.BlockStatement=function(e,t,n){for(var r=0,i=e.body;r<i.length;r+=1){n(i[r],t,"Statement")}},o.Statement=n,o.EmptyStatement=r,o.ExpressionStatement=o.ParenthesizedExpression=function(e,t,n){return n(e.expression,t,"Expression")},o.IfStatement=function(e,t,n){n(e.test,t,"Expression"),n(e.consequent,t,"Statement"),e.alternate&&n(e.alternate,t,"Statement")},o.LabeledStatement=function(e,t,n){return n(e.body,t,"Statement")},o.BreakStatement=o.ContinueStatement=r,o.WithStatement=function(e,t,n){n(e.object,t,"Expression"),n(e.body,t,"Statement")},o.SwitchStatement=function(e,t,n){n(e.discriminant,t,"Expression");for(var r=0,i=e.cases;r<i.length;r+=1){var s=i[r];s.test&&n(s.test,t,"Expression");for(var o=0,a=s.consequent;o<a.length;o+=1){n(a[o],t,"Statement")}}},o.ReturnStatement=o.YieldExpression=o.AwaitExpression=function(e,t,n){e.argument&&n(e.argument,t,"Expression")},o.ThrowStatement=o.SpreadElement=function(e,t,n){return n(e.argument,t,"Expression")},o.TryStatement=function(e,t,n){n(e.block,t,"Statement"),e.handler&&n(e.handler,t),e.finalizer&&n(e.finalizer,t,"Statement")},o.CatchClause=function(e,t,n){n(e.param,t,"Pattern"),n(e.body,t,"ScopeBody")},o.WhileStatement=o.DoWhileStatement=function(e,t,n){n(e.test,t,"Expression"),n(e.body,t,"Statement")},o.ForStatement=function(e,t,n){e.init&&n(e.init,t,"ForInit"),e.test&&n(e.test,t,"Expression"),e.update&&n(e.update,t,"Expression"),n(e.body,t,"Statement")},o.ForInStatement=o.ForOfStatement=function(e,t,n){n(e.left,t,"ForInit"),n(e.right,t,"Expression"),n(e.body,t,"Statement")},o.ForInit=function(e,t,n){"VariableDeclaration"==e.type?n(e,t):n(e,t,"Expression")},o.DebuggerStatement=r,o.FunctionDeclaration=function(e,t,n){return n(e,t,"Function")},o.VariableDeclaration=function(e,t,n){for(var r=0,i=e.declarations;r<i.length;r+=1){n(i[r],t)}},o.VariableDeclarator=function(e,t,n){n(e.id,t,"Pattern"),e.init&&n(e.init,t,"Expression")},o.Function=function(e,t,n){e.id&&n(e.id,t,"Pattern");for(var r=0,i=e.params;r<i.length;r+=1){n(i[r],t,"Pattern")}n(e.body,t,e.expression?"ScopeExpression":"ScopeBody")},o.ScopeBody=function(e,t,n){return n(e,t,"Statement")},o.ScopeExpression=function(e,t,n){return n(e,t,"Expression")},o.Pattern=function(e,t,n){"Identifier"==e.type?n(e,t,"VariablePattern"):"MemberExpression"==e.type?n(e,t,"MemberPattern"):n(e,t)},o.VariablePattern=r,o.MemberPattern=n,o.RestElement=function(e,t,n){return n(e.argument,t,"Pattern")},o.ArrayPattern=function(e,t,n){for(var r=0,i=e.elements;r<i.length;r+=1){var s=i[r];s&&n(s,t,"Pattern")}},o.ObjectPattern=function(e,t,n){for(var r=0,i=e.properties;r<i.length;r+=1){n(i[r].value,t,"Pattern")}},o.Expression=n,o.ThisExpression=o.Super=o.MetaProperty=r,o.ArrayExpression=function(e,t,n){for(var r=0,i=e.elements;r<i.length;r+=1){var s=i[r];s&&n(s,t,"Expression")}},o.ObjectExpression=function(e,t,n){for(var r=0,i=e.properties;r<i.length;r+=1){n(i[r],t)}},o.FunctionExpression=o.ArrowFunctionExpression=o.FunctionDeclaration,o.SequenceExpression=o.TemplateLiteral=function(e,t,n){for(var r=0,i=e.expressions;r<i.length;r+=1){n(i[r],t,"Expression")}},o.UnaryExpression=o.UpdateExpression=function(e,t,n){n(e.argument,t,"Expression")},o.BinaryExpression=o.LogicalExpression=function(e,t,n){n(e.left,t,"Expression"),n(e.right,t,"Expression")},o.AssignmentExpression=o.AssignmentPattern=function(e,t,n){n(e.left,t,"Pattern"),n(e.right,t,"Expression")},o.ConditionalExpression=function(e,t,n){n(e.test,t,"Expression"),n(e.consequent,t,"Expression"),n(e.alternate,t,"Expression")},o.NewExpression=o.CallExpression=function(e,t,n){if(n(e.callee,t,"Expression"),e.arguments)for(var r=0,i=e.arguments;r<i.length;r+=1){n(i[r],t,"Expression")}},o.MemberExpression=function(e,t,n){n(e.object,t,"Expression"),e.computed&&n(e.property,t,"Expression")},o.ExportNamedDeclaration=o.ExportDefaultDeclaration=function(e,t,n){e.declaration&&n(e.declaration,t,"ExportNamedDeclaration"==e.type||e.declaration.id?"Statement":"Expression"),e.source&&n(e.source,t,"Expression")},o.ExportAllDeclaration=function(e,t,n){n(e.source,t,"Expression")},o.ImportDeclaration=function(e,t,n){for(var r=0,i=e.specifiers;r<i.length;r+=1){n(i[r],t)}n(e.source,t,"Expression")},o.ImportSpecifier=o.ImportDefaultSpecifier=o.ImportNamespaceSpecifier=o.Identifier=o.Literal=r,o.TaggedTemplateExpression=function(e,t,n){n(e.tag,t,"Expression"),n(e.quasi,t)},o.ClassDeclaration=o.ClassExpression=function(e,t,n){return n(e,t,"Class")},o.Class=function(e,t,n){e.id&&n(e.id,t,"Pattern"),e.superClass&&n(e.superClass,t,"Expression");for(var r=0,i=e.body.body;r<i.length;r+=1){n(i[r],t)}},o.MethodDefinition=o.Property=function(e,t,n){e.computed&&n(e.key,t,"Expression"),n(e.value,t,"Expression")},e.simple=function(t,n,r,i,s){r||(r=e.base),function e(t,i,s){var o=s||t.type,a=n[o];r[o](t,i,e),a&&a(t,i)}(t,i,s)},e.ancestor=function(t,n,r,i){r||(r=e.base);var s=[];!function e(t,i,o){var a=o||t.type,u=n[a],c=t!=s[s.length-1];c&&s.push(t),r[a](t,i,e),u&&u(t,i||s,s),c&&s.pop()}(t,i)},e.recursive=function(t,n,r,i,s){var o=r?e.make(r,i):i;!function e(t,n,r){o[r||t.type](t,n,e)}(t,n,s)},e.full=function(t,n,r,i,s){r||(r=e.base),function e(t,i,s){var o=s||t.type;r[o](t,i,e),s||n(t,i,o)}(t,i,s)},e.fullAncestor=function(t,n,r,i){r||(r=e.base);var s=[];!function e(t,i,o){var a=o||t.type,u=t!=s[s.length-1];u&&s.push(t),r[a](t,i,e),o||n(t,i||s,s,a),u&&s.pop()}(t,i)},e.findNodeAt=function(n,r,s,o,a,u){o=t(o),a||(a=e.base);try{!function e(t,n,u){var c=u||t.type;if((null==r||t.start<=r)&&(null==s||t.end>=s)&&a[c](t,n,e),(null==r||t.start==r)&&(null==s||t.end==s)&&o(c,t))throw new i(t,n)}(n,u)}catch(e){if(e instanceof i)return e;throw e}},e.findNodeAround=function(n,r,s,o,a){s=t(s),o||(o=e.base);try{!function e(t,n,a){var u=a||t.type;if(!(t.start>r||t.end<r)&&(o[u](t,n,e),s(u,t)))throw new i(t,n)}(n,a)}catch(e){if(e instanceof i)return e;throw e}},e.findNodeAfter=function(n,r,s,o,a){s=t(s),o||(o=e.base);try{!function e(t,n,a){if(!(t.end<r)){var u=a||t.type;if(t.start>=r&&s(u,t))throw new i(t,n);o[u](t,n,e)}}(n,a)}catch(e){if(e instanceof i)return e;throw e}},e.findNodeBefore=function(n,r,s,o,a){s=t(s),o||(o=e.base);var u;return function e(t,n,a){if(!(t.start>r)){var c=a||t.type;t.end<=r&&(!u||u.node.end<t.end)&&s(c,t)&&(u=new i(t,n)),o[c](t,n,e)}}(n,a),u},e.make=function(t,n){n||(n=e.base);var r=s(n);for(var i in t)r[i]=t[i];return r},e.base=o,Object.defineProperty(e,"__esModule",{value:!0})})},{}],6:[function(e,t,n){"use strict";function r(e){var t=e.length;if(t%4>0)throw new Error("Invalid string. Length must be a multiple of 4");return"="===e[t-2]?2:"="===e[t-1]?1:0}function i(e){return o[e>>18&63]+o[e>>12&63]+o[e>>6&63]+o[63&e]}function s(e,t,n){for(var r,s=[],o=t;o<n;o+=3)r=(e[o]<<16)+(e[o+1]<<8)+e[o+2],s.push(i(r));return s.join("")}n.byteLength=function(e){return 3*e.length/4-r(e)},n.toByteArray=function(e){var t,n,i,s,o,c=e.length;s=r(e),o=new u(3*c/4-s),n=s>0?c-4:c;var l=0;for(t=0;t<n;t+=4)i=a[e.charCodeAt(t)]<<18|a[e.charCodeAt(t+1)]<<12|a[e.charCodeAt(t+2)]<<6|a[e.charCodeAt(t+3)],o[l++]=i>>16&255,o[l++]=i>>8&255,o[l++]=255&i;return 2===s?(i=a[e.charCodeAt(t)]<<2|a[e.charCodeAt(t+1)]>>4,o[l++]=255&i):1===s&&(i=a[e.charCodeAt(t)]<<10|a[e.charCodeAt(t+1)]<<4|a[e.charCodeAt(t+2)]>>2,o[l++]=i>>8&255,o[l++]=255&i),o},n.fromByteArray=function(e){for(var t,n=e.length,r=n%3,i="",a=[],u=0,c=n-r;u<c;u+=16383)a.push(s(e,u,u+16383>c?c:u+16383));return 1===r?(t=e[n-1],i+=o[t>>2],i+=o[t<<4&63],i+="=="):2===r&&(t=(e[n-2]<<8)+e[n-1],i+=o[t>>10],i+=o[t>>4&63],i+=o[t<<2&63],i+="="),a.push(i),a.join("")};for(var o=[],a=[],u="undefined"!=typeof Uint8Array?Uint8Array:Array,c="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",l=0,p=c.length;l<p;++l)o[l]=c[l],a[c.charCodeAt(l)]=l;a["-".charCodeAt(0)]=62,a["_".charCodeAt(0)]=63},{}],7:[function(e,t,n){},{}],8:[function(e,t,n){"use strict";function r(e){if(e>B)throw new RangeError("Invalid typed array length");var t=new Uint8Array(e);return t.__proto__=i.prototype,t}function i(e,t,n){if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return a(e)}return s(e,t,n)}function s(e,t,n){if("number"==typeof e)throw new TypeError('"value" argument must not be a number');return T(e)?function(e,t,n){if(t<0||e.byteLength<t)throw new RangeError("'offset' is out of bounds");if(e.byteLength<t+(n||0))throw new RangeError("'length' is out of bounds");var r;r=void 0===t&&void 0===n?new Uint8Array(e):void 0===n?new Uint8Array(e,t):new Uint8Array(e,t,n);return r.__proto__=i.prototype,r}(e,t,n):"string"==typeof e?function(e,t){"string"==typeof t&&""!==t||(t="utf8");if(!i.isEncoding(t))throw new TypeError('"encoding" must be a valid string encoding');var n=0|l(e,t),s=r(n),o=s.write(e,t);o!==n&&(s=s.slice(0,o));return s}(e,t):function(e){if(i.isBuffer(e)){var t=0|c(e.length),n=r(t);return 0===n.length?n:(e.copy(n,0,0,t),n)}if(e){if(N(e)||"length"in e)return"number"!=typeof e.length||P(e.length)?r(0):u(e);if("Buffer"===e.type&&Array.isArray(e.data))return u(e.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(e)}function o(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function a(e){return o(e),r(e<0?0:0|c(e))}function u(e){for(var t=e.length<0?0:0|c(e.length),n=r(t),i=0;i<t;i+=1)n[i]=255&e[i];return n}function c(e){if(e>=B)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+B.toString(16)+" bytes");return 0|e}function l(e,t){if(i.isBuffer(e))return e.length;if(N(e)||T(e))return e.byteLength;"string"!=typeof e&&(e=""+e);var n=e.length;if(0===n)return 0;for(var r=!1;;)switch(t){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return _(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return L(e).length;default:if(r)return _(e).length;t=(""+t).toLowerCase(),r=!0}}function p(e,t,n){var r=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if(n>>>=0,t>>>=0,n<=t)return"";for(e||(e="utf8");;)switch(e){case"hex":return function(e,t,n){var r=e.length;(!t||t<0)&&(t=0);(!n||n<0||n>r)&&(n=r);for(var i="",s=t;s<n;++s)i+=function(e){return e<16?"0"+e.toString(16):e.toString(16)}(e[s]);return i}(this,t,n);case"utf8":case"utf-8":return w(this,t,n);case"ascii":return function(e,t,n){var r="";n=Math.min(e.length,n);for(var i=t;i<n;++i)r+=String.fromCharCode(127&e[i]);return r}(this,t,n);case"latin1":case"binary":return function(e,t,n){var r="";n=Math.min(e.length,n);for(var i=t;i<n;++i)r+=String.fromCharCode(e[i]);return r}(this,t,n);case"base64":return function(e,t,n){return 0===t&&n===e.length?F.fromByteArray(e):F.fromByteArray(e.slice(t,n))}(this,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(e,t,n){for(var r=e.slice(t,n),i="",s=0;s<r.length;s+=2)i+=String.fromCharCode(r[s]+256*r[s+1]);return i}(this,t,n);default:if(r)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),r=!0}}function h(e,t,n){var r=e[t];e[t]=e[n],e[n]=r}function f(e,t,n,r,s){if(0===e.length)return-1;if("string"==typeof n?(r=n,n=0):n>2147483647?n=2147483647:n<-2147483648&&(n=-2147483648),n=+n,P(n)&&(n=s?0:e.length-1),n<0&&(n=e.length+n),n>=e.length){if(s)return-1;n=e.length-1}else if(n<0){if(!s)return-1;n=0}if("string"==typeof t&&(t=i.from(t,r)),i.isBuffer(t))return 0===t.length?-1:d(e,t,n,r,s);if("number"==typeof t)return t&=255,"function"==typeof Uint8Array.prototype.indexOf?s?Uint8Array.prototype.indexOf.call(e,t,n):Uint8Array.prototype.lastIndexOf.call(e,t,n):d(e,[t],n,r,s);throw new TypeError("val must be string, number or Buffer")}function d(e,t,n,r,i){function s(e,t){return 1===o?e[t]:e.readUInt16BE(t*o)}var o=1,a=e.length,u=t.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(e.length<2||t.length<2)return-1;o=2,a/=2,u/=2,n/=2}var c;if(i){var l=-1;for(c=n;c<a;c++)if(s(e,c)===s(t,-1===l?0:c-l)){if(-1===l&&(l=c),c-l+1===u)return l*o}else-1!==l&&(c-=c-l),l=-1}else for(n+u>a&&(n=a-u),c=n;c>=0;c--){for(var p=!0,h=0;h<u;h++)if(s(e,c+h)!==s(t,h)){p=!1;break}if(p)return c}return-1}function y(e,t,n,r){n=Number(n)||0;var i=e.length-n;r?(r=Number(r))>i&&(r=i):r=i;var s=t.length;if(s%2!=0)throw new TypeError("Invalid hex string");r>s/2&&(r=s/2);for(var o=0;o<r;++o){var a=parseInt(t.substr(2*o,2),16);if(P(a))return o;e[n+o]=a}return o}function m(e,t,n,r){return O(_(t,e.length-n),e,n,r)}function g(e,t,n,r){return O(function(e){for(var t=[],n=0;n<e.length;++n)t.push(255&e.charCodeAt(n));return t}(t),e,n,r)}function v(e,t,n,r){return g(e,t,n,r)}function b(e,t,n,r){return O(L(t),e,n,r)}function x(e,t,n,r){return O(function(e,t){for(var n,r,i,s=[],o=0;o<e.length&&!((t-=2)<0);++o)n=e.charCodeAt(o),r=n>>8,i=n%256,s.push(i),s.push(r);return s}(t,e.length-n),e,n,r)}function w(e,t,n){n=Math.min(e.length,n);for(var r=[],i=t;i<n;){var s=e[i],o=null,a=s>239?4:s>223?3:s>191?2:1;if(i+a<=n){var u,c,l,p;switch(a){case 1:s<128&&(o=s);break;case 2:128==(192&(u=e[i+1]))&&(p=(31&s)<<6|63&u)>127&&(o=p);break;case 3:u=e[i+1],c=e[i+2],128==(192&u)&&128==(192&c)&&(p=(15&s)<<12|(63&u)<<6|63&c)>2047&&(p<55296||p>57343)&&(o=p);break;case 4:u=e[i+1],c=e[i+2],l=e[i+3],128==(192&u)&&128==(192&c)&&128==(192&l)&&(p=(15&s)<<18|(63&u)<<12|(63&c)<<6|63&l)>65535&&p<1114112&&(o=p)}}null===o?(o=65533,a=1):o>65535&&(o-=65536,r.push(o>>>10&1023|55296),o=56320|1023&o),r.push(o),i+=a}return function(e){var t=e.length;if(t<=R)return String.fromCharCode.apply(String,e);var n="",r=0;for(;r<t;)n+=String.fromCharCode.apply(String,e.slice(r,r+=R));return n}(r)}function E(e,t,n){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>n)throw new RangeError("Trying to access beyond buffer length")}function S(e,t,n,r,s,o){if(!i.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>s||t<o)throw new RangeError('"value" argument is out of bounds');if(n+r>e.length)throw new RangeError("Index out of range")}function k(e,t,n,r,i,s){if(n+r>e.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function A(e,t,n,r,i){return t=+t,n>>>=0,i||k(e,0,n,4),$.write(e,t,n,r,23,4),n+4}function C(e,t,n,r,i){return t=+t,n>>>=0,i||k(e,0,n,8),$.write(e,t,n,r,52,8),n+8}function _(e,t){t=t||1/0;for(var n,r=e.length,i=null,s=[],o=0;o<r;++o){if((n=e.charCodeAt(o))>55295&&n<57344){if(!i){if(n>56319){(t-=3)>-1&&s.push(239,191,189);continue}if(o+1===r){(t-=3)>-1&&s.push(239,191,189);continue}i=n;continue}if(n<56320){(t-=3)>-1&&s.push(239,191,189),i=n;continue}n=65536+(i-55296<<10|n-56320)}else i&&(t-=3)>-1&&s.push(239,191,189);if(i=null,n<128){if((t-=1)<0)break;s.push(n)}else if(n<2048){if((t-=2)<0)break;s.push(n>>6|192,63&n|128)}else if(n<65536){if((t-=3)<0)break;s.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;s.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return s}function L(e){return F.toByteArray(function(e){if((e=e.trim().replace(I,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function O(e,t,n,r){for(var i=0;i<r&&!(i+n>=t.length||i>=e.length);++i)t[i+n]=e[i];return i}function T(e){return e instanceof ArrayBuffer||null!=e&&null!=e.constructor&&"ArrayBuffer"===e.constructor.name&&"number"==typeof e.byteLength}function N(e){return"function"==typeof ArrayBuffer.isView&&ArrayBuffer.isView(e)}function P(e){return e!=e}var F=e("base64-js"),$=e("ieee754");n.Buffer=i,n.SlowBuffer=function(e){return+e!=e&&(e=0),i.alloc(+e)},n.INSPECT_MAX_BYTES=50;var B=2147483647;n.kMaxLength=B,(i.TYPED_ARRAY_SUPPORT=function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()}catch(e){return!1}}())||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),"undefined"!=typeof Symbol&&Symbol.species&&i[Symbol.species]===i&&Object.defineProperty(i,Symbol.species,{value:null,configurable:!0,enumerable:!1,writable:!1}),i.poolSize=8192,i.from=function(e,t,n){return s(e,t,n)},i.prototype.__proto__=Uint8Array.prototype,i.__proto__=Uint8Array,i.alloc=function(e,t,n){return function(e,t,n){return o(e),e<=0?r(e):void 0!==t?"string"==typeof n?r(e).fill(t,n):r(e).fill(t):r(e)}(e,t,n)},i.allocUnsafe=function(e){return a(e)},i.allocUnsafeSlow=function(e){return a(e)},i.isBuffer=function(e){return null!=e&&!0===e._isBuffer},i.compare=function(e,t){if(!i.isBuffer(e)||!i.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var n=e.length,r=t.length,s=0,o=Math.min(n,r);s<o;++s)if(e[s]!==t[s]){n=e[s],r=t[s];break}return n<r?-1:r<n?1:0},i.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},i.concat=function(e,t){if(!Array.isArray(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return i.alloc(0);var n;if(void 0===t)for(t=0,n=0;n<e.length;++n)t+=e[n].length;var r=i.allocUnsafe(t),s=0;for(n=0;n<e.length;++n){var o=e[n];if(!i.isBuffer(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(r,s),s+=o.length}return r},i.byteLength=l,i.prototype._isBuffer=!0,i.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)h(this,t,t+1);return this},i.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)h(this,t,t+3),h(this,t+1,t+2);return this},i.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)h(this,t,t+7),h(this,t+1,t+6),h(this,t+2,t+5),h(this,t+3,t+4);return this},i.prototype.toString=function(){var e=this.length;return 0===e?"":0===arguments.length?w(this,0,e):p.apply(this,arguments)},i.prototype.equals=function(e){if(!i.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===i.compare(this,e)},i.prototype.inspect=function(){var e="",t=n.INSPECT_MAX_BYTES;return this.length>0&&(e=this.toString("hex",0,t).match(/.{2}/g).join(" "),this.length>t&&(e+=" ... ")),"<Buffer "+e+">"},i.prototype.compare=function(e,t,n,r,s){if(!i.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===n&&(n=e?e.length:0),void 0===r&&(r=0),void 0===s&&(s=this.length),t<0||n>e.length||r<0||s>this.length)throw new RangeError("out of range index");if(r>=s&&t>=n)return 0;if(r>=s)return-1;if(t>=n)return 1;if(t>>>=0,n>>>=0,r>>>=0,s>>>=0,this===e)return 0;for(var o=s-r,a=n-t,u=Math.min(o,a),c=this.slice(r,s),l=e.slice(t,n),p=0;p<u;++p)if(c[p]!==l[p]){o=c[p],a=l[p];break}return o<a?-1:a<o?1:0},i.prototype.includes=function(e,t,n){return-1!==this.indexOf(e,t,n)},i.prototype.indexOf=function(e,t,n){return f(this,e,t,n,!0)},i.prototype.lastIndexOf=function(e,t,n){return f(this,e,t,n,!1)},i.prototype.write=function(e,t,n,r){if(void 0===t)r="utf8",n=this.length,t=0;else if(void 0===n&&"string"==typeof t)r=t,n=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t>>>=0,isFinite(n)?(n>>>=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}var i=this.length-t;if((void 0===n||n>i)&&(n=i),e.length>0&&(n<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var s=!1;;)switch(r){case"hex":return y(this,e,t,n);case"utf8":case"utf-8":return m(this,e,t,n);case"ascii":return g(this,e,t,n);case"latin1":case"binary":return v(this,e,t,n);case"base64":return b(this,e,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return x(this,e,t,n);default:if(s)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),s=!0}},i.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var R=4096;i.prototype.slice=function(e,t){var n=this.length;e=~~e,t=void 0===t?n:~~t,e<0?(e+=n)<0&&(e=0):e>n&&(e=n),t<0?(t+=n)<0&&(t=0):t>n&&(t=n),t<e&&(t=e);var r=this.subarray(e,t);return r.__proto__=i.prototype,r},i.prototype.readUIntLE=function(e,t,n){e>>>=0,t>>>=0,n||E(e,t,this.length);for(var r=this[e],i=1,s=0;++s<t&&(i*=256);)r+=this[e+s]*i;return r},i.prototype.readUIntBE=function(e,t,n){e>>>=0,t>>>=0,n||E(e,t,this.length);for(var r=this[e+--t],i=1;t>0&&(i*=256);)r+=this[e+--t]*i;return r},i.prototype.readUInt8=function(e,t){return e>>>=0,t||E(e,1,this.length),this[e]},i.prototype.readUInt16LE=function(e,t){return e>>>=0,t||E(e,2,this.length),this[e]|this[e+1]<<8},i.prototype.readUInt16BE=function(e,t){return e>>>=0,t||E(e,2,this.length),this[e]<<8|this[e+1]},i.prototype.readUInt32LE=function(e,t){return e>>>=0,t||E(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},i.prototype.readUInt32BE=function(e,t){return e>>>=0,t||E(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},i.prototype.readIntLE=function(e,t,n){e>>>=0,t>>>=0,n||E(e,t,this.length);for(var r=this[e],i=1,s=0;++s<t&&(i*=256);)r+=this[e+s]*i;return i*=128,r>=i&&(r-=Math.pow(2,8*t)),r},i.prototype.readIntBE=function(e,t,n){e>>>=0,t>>>=0,n||E(e,t,this.length);for(var r=t,i=1,s=this[e+--r];r>0&&(i*=256);)s+=this[e+--r]*i;return i*=128,s>=i&&(s-=Math.pow(2,8*t)),s},i.prototype.readInt8=function(e,t){return e>>>=0,t||E(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},i.prototype.readInt16LE=function(e,t){e>>>=0,t||E(e,2,this.length);var n=this[e]|this[e+1]<<8;return 32768&n?4294901760|n:n},i.prototype.readInt16BE=function(e,t){e>>>=0,t||E(e,2,this.length);var n=this[e+1]|this[e]<<8;return 32768&n?4294901760|n:n},i.prototype.readInt32LE=function(e,t){return e>>>=0,t||E(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},i.prototype.readInt32BE=function(e,t){return e>>>=0,t||E(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},i.prototype.readFloatLE=function(e,t){return e>>>=0,t||E(e,4,this.length),$.read(this,e,!0,23,4)},i.prototype.readFloatBE=function(e,t){return e>>>=0,t||E(e,4,this.length),$.read(this,e,!1,23,4)},i.prototype.readDoubleLE=function(e,t){return e>>>=0,t||E(e,8,this.length),$.read(this,e,!0,52,8)},i.prototype.readDoubleBE=function(e,t){return e>>>=0,t||E(e,8,this.length),$.read(this,e,!1,52,8)},i.prototype.writeUIntLE=function(e,t,n,r){if(e=+e,t>>>=0,n>>>=0,!r){S(this,e,t,n,Math.pow(2,8*n)-1,0)}var i=1,s=0;for(this[t]=255&e;++s<n&&(i*=256);)this[t+s]=e/i&255;return t+n},i.prototype.writeUIntBE=function(e,t,n,r){if(e=+e,t>>>=0,n>>>=0,!r){S(this,e,t,n,Math.pow(2,8*n)-1,0)}var i=n-1,s=1;for(this[t+i]=255&e;--i>=0&&(s*=256);)this[t+i]=e/s&255;return t+n},i.prototype.writeUInt8=function(e,t,n){return e=+e,t>>>=0,n||S(this,e,t,1,255,0),this[t]=255&e,t+1},i.prototype.writeUInt16LE=function(e,t,n){return e=+e,t>>>=0,n||S(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},i.prototype.writeUInt16BE=function(e,t,n){return e=+e,t>>>=0,n||S(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},i.prototype.writeUInt32LE=function(e,t,n){return e=+e,t>>>=0,n||S(this,e,t,4,4294967295,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},i.prototype.writeUInt32BE=function(e,t,n){return e=+e,t>>>=0,n||S(this,e,t,4,4294967295,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},i.prototype.writeIntLE=function(e,t,n,r){if(e=+e,t>>>=0,!r){var i=Math.pow(2,8*n-1);S(this,e,t,n,i-1,-i)}var s=0,o=1,a=0;for(this[t]=255&e;++s<n&&(o*=256);)e<0&&0===a&&0!==this[t+s-1]&&(a=1),this[t+s]=(e/o>>0)-a&255;return t+n},i.prototype.writeIntBE=function(e,t,n,r){if(e=+e,t>>>=0,!r){var i=Math.pow(2,8*n-1);S(this,e,t,n,i-1,-i)}var s=n-1,o=1,a=0;for(this[t+s]=255&e;--s>=0&&(o*=256);)e<0&&0===a&&0!==this[t+s+1]&&(a=1),this[t+s]=(e/o>>0)-a&255;return t+n},i.prototype.writeInt8=function(e,t,n){return e=+e,t>>>=0,n||S(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},i.prototype.writeInt16LE=function(e,t,n){return e=+e,t>>>=0,n||S(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},i.prototype.writeInt16BE=function(e,t,n){return e=+e,t>>>=0,n||S(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},i.prototype.writeInt32LE=function(e,t,n){return e=+e,t>>>=0,n||S(this,e,t,4,2147483647,-2147483648),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},i.prototype.writeInt32BE=function(e,t,n){return e=+e,t>>>=0,n||S(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},i.prototype.writeFloatLE=function(e,t,n){return A(this,e,t,!0,n)},i.prototype.writeFloatBE=function(e,t,n){return A(this,e,t,!1,n)},i.prototype.writeDoubleLE=function(e,t,n){return C(this,e,t,!0,n)},i.prototype.writeDoubleBE=function(e,t,n){return C(this,e,t,!1,n)},i.prototype.copy=function(e,t,n,r){if(n||(n=0),r||0===r||(r=this.length),t>=e.length&&(t=e.length),t||(t=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),e.length-t<r-n&&(r=e.length-t+n);var i,s=r-n;if(this===e&&n<t&&t<r)for(i=s-1;i>=0;--i)e[i+t]=this[i+n];else if(s<1e3)for(i=0;i<s;++i)e[i+t]=this[i+n];else Uint8Array.prototype.set.call(e,this.subarray(n,n+s),t);return s},i.prototype.fill=function(e,t,n,r){if("string"==typeof e){if("string"==typeof t?(r=t,t=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),1===e.length){var s=e.charCodeAt(0);s<256&&(e=s)}if(void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!i.isEncoding(r))throw new TypeError("Unknown encoding: "+r)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<n)throw new RangeError("Out of range index");if(n<=t)return this;t>>>=0,n=void 0===n?this.length:n>>>0,e||(e=0);var o;if("number"==typeof e)for(o=t;o<n;++o)this[o]=e;else{var a=i.isBuffer(e)?e:new i(e,r),u=a.length;for(o=0;o<n-t;++o)this[o+t]=a[o%u]}return this};var I=/[^+/0-9A-Za-z-_]/g},{"base64-js":6,ieee754:9}],9:[function(e,t,n){n.read=function(e,t,n,r,i){var s,o,a=8*i-r-1,u=(1<<a)-1,c=u>>1,l=-7,p=n?i-1:0,h=n?-1:1,f=e[t+p];for(p+=h,s=f&(1<<-l)-1,f>>=-l,l+=a;l>0;s=256*s+e[t+p],p+=h,l-=8);for(o=s&(1<<-l)-1,s>>=-l,l+=r;l>0;o=256*o+e[t+p],p+=h,l-=8);if(0===s)s=1-c;else{if(s===u)return o?NaN:1/0*(f?-1:1);o+=Math.pow(2,r),s-=c}return(f?-1:1)*o*Math.pow(2,s-r)},n.write=function(e,t,n,r,i,s){var o,a,u,c=8*s-i-1,l=(1<<c)-1,p=l>>1,h=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,f=r?0:s-1,d=r?1:-1,y=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(a=isNaN(t)?1:0,o=l):(o=Math.floor(Math.log(t)/Math.LN2),t*(u=Math.pow(2,-o))<1&&(o--,u*=2),(t+=o+p>=1?h/u:h*Math.pow(2,1-p))*u>=2&&(o++,u/=2),o+p>=l?(a=0,o=l):o+p>=1?(a=(t*u-1)*Math.pow(2,i),o+=p):(a=t*Math.pow(2,p-1)*Math.pow(2,i),o=0));i>=8;e[n+f]=255&a,f+=d,a/=256,i-=8);for(o=o<<i|a,c+=i;c>0;e[n+f]=255&o,f+=d,o/=256,c-=8);e[n+f-d]|=128*y}},{}],10:[function(e,t,n){(function(n){function r(){}function i(e){this.covers={},this._ident=i.prototype.version+"_"+Math.random(),this.setOptions(e||{})}var s=e("./lib/parser"),o=e("./lib/arboriculture"),a=e("./lib/output");i.prototype.smCache={},i.prototype.setOptions=function(e){return this.log=!1===e.log?r:e.log||this.log,this.options=function(e){var t={};return e.forEach(function(e){if(e&&"object"==typeof e)for(var n in e)t[n]=e[n]}),t}([this.options,e]),delete this.options.log,this},i.prototype.version=e("./package.json").version,i.prototype.isThenable=function(e){return e&&e instanceof Object&&"function"==typeof e.then},i.prototype.compile=function(e,t,n,s){"object"==typeof n&&void 0===s&&(s=n),s=s||{};for(var o in i.initialCodeGenOpts)o in s||(s[o]=i.initialCodeGenOpts[o]);var a=this.parse(e,t,null,s);return this.asynchronize(a,null,s,this.log||r),this.prettyPrint(a,s),a},i.prototype.parse=function(e,t,n,r){"object"==typeof n&&void 0===r&&(r=n);var i={origCode:e.toString(),filename:t};try{return i.ast=s.parse(i.origCode,r&&r.parser),r.babelTree&&s.treeWalker(i.ast,function(e,t,n){"Literal"===e.type?n[0].replace(o.babelLiteralNode(e.value)):"Property"===e.type&&("ClassBody"===n[0].parent.type?e.type="ClassProperty":e.type="ObjectProperty"),t()}),i}catch(e){if(e instanceof SyntaxError){var a=i.origCode.substr(e.pos-e.loc.column);a=a.split("\n")[0],e.message+=" "+t+" (nodent)\n"+a+"\n"+a.replace(/[\S ]/g,"-").substring(0,e.loc.column)+"^",e.stack=""}throw e}},i.prototype.asynchronize=o.asynchronize,i.prototype.printNode=o.printNode,i.prototype.prettyPrint=function(t,r){var i=t.filename?t.filename.split("/"):["anonymous"],s=i.pop(),o=a(t.ast,r&&r.sourcemap?{map:{startLine:r.mapStartLine||0,file:s+"(original)",sourceMapRoot:i.join("/"),sourceContent:t.origCode}}:null,t.origCode);if(r&&r.sourcemap)try{var u="",c=o.map.toJSON();if(c){var l=e("source-map").SourceMapConsumer;t.sourcemap=c,this.smCache[t.filename]={map:c,smc:new l(c)},u="\n\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,"+function(e){return(e instanceof n?e:new n(e.toString(),"binary")).toString("base64")}(JSON.stringify(c))+"\n"}t.code=o.code+u}catch(e){t.code=o}else t.code=o;return t},i.prototype.getDefaultCompileOptions=void 0,Object.defineProperty(i.prototype,"Promise",{get:function(){return initOpts.log("Warning: nodent.Promise is deprecated. Use nodent.Thenable instead"),Thenable},enumerable:!1,configurable:!1}),i.initialCodeGenOpts={noRuntime:!1,lazyThenables:!1,es6target:!1,noUseDirective:!1,wrapAwait:null,mapStartLine:0,sourcemap:!0,engine:!1,parser:{sourceType:"script"},$return:"$return",$error:"$error",$arguments:"$args",$asyncspawn:"$asyncspawn",$asyncbind:"$asyncbind",generatedSymbolPrefix:"$",$makeThenable:"$makeThenable"},t.exports=i}).call(this,e("buffer").Buffer)},{"./lib/arboriculture":11,"./lib/output":12,"./lib/parser":13,"./package.json":25,buffer:8,"source-map":24}],11:[function(e,t,n){"use strict";function r(e){if(!e)return"";if(Array.isArray(e))return e.map(r).join("|\n");try{return m(e)}catch(t){return t.message+": "+(e&&e.type)}}function i(e){if(Array.isArray(e))return e.map(function(e){return i(e)});var t={};return Object.keys(e).forEach(function(n){t[n]=e[n]}),t}function s(e,t){e!==t&&(e.__proto__=Object.getPrototypeOf(t),Object.keys(e).forEach(function(t){t in g||delete e[t]}),Object.keys(t).forEach(function(n){n in e||(e[n]=t[n])}))}function o(){}function a(e){return e?(b.node=e,b):{}}function u(e,t,n){if(!e)return null;if(t&&"object"==typeof t){var r=Object.keys(t);return u(e,function(e){return r.every(function(n){return e[n]==t[n]})})}var i,s={};if(Array.isArray(e)){for(var o=0;o<e.length;o++)if(i=u(e[o],t))return i;return null}var c=n;"function"!=typeof n&&(c=n?function(e){return!0}:function(e){return!a(e).isScope});try{y.treeWalker(e,function(n,r,i){if(t(n))throw s.path=i,s;(n===e||c(n))&&r()})}catch(e){if(e===s)return s.path;throw e}return null}function c(e){return u(e,function(e){return"AwaitExpression"===e.type&&!e.$hidden})}function l(e){return u(e,function(e){return"AwaitExpression"===e.type&&!e.$hidden},function(e){var t=a(e);return!t.isBlockStatement&&!t.isScope})}function p(e){if(null===e)return{type:"NullLiteral",value:null,raw:"null"};if(!0===e||!1===e)return{type:"BooleanLiteral",value:e,raw:JSON.stringify(e)};if(e instanceof RegExp){var t=e.toString(),n=t.split("/");return{type:"RegExpLiteral",value:e,raw:t,pattern:n[1],flags:n[2]}}return"number"==typeof e?{type:"NumericLiteral",value:e,raw:JSON.stringify(e)}:{type:"StringLiteral",value:e,raw:JSON.stringify(e)}}function h(e,t){return{type:"Identifier",name:e,loc:t}}function f(e){var t={};for(var n in e)t[n]="string"==typeof e[n]?h(e[n]):e[n];return t}function d(e,t,n,d){function m(e,t){if(n.es6target&&!e.id&&!t&&0===e.type.indexOf("Function"))return e.type="ArrowFunctionExpression",e;if(n.noRuntime){if(t){if(a(t).isLiteral)throw new Error("Nodent: 'noRuntime' option only compatible with -promise and -engine modes");e.body.body=y.part("try {$:0} catch($2) {return $1($2)}",[i(e.body),t,h("$boundEx")]).body}else if(n.es6target&&!e.id&&0===e.type.indexOf("Function"))return e.type="ArrowFunctionExpression",e;return n.es6target&&!e.id?(e.type="ArrowFunctionExpression",e):y.part("$0.bind(this)",[e]).expr}return t?y.part("$0.$1(this,$2)",[e,pe.asyncbind,t]).expr:y.part("$0.$1(this)",[e,pe.asyncbind]).expr}function g(e,t,n,r){return y.part("var $0 = $1",[h(e),m({type:"FunctionExpression",id:null,generator:!1,expression:!1,params:n||[],body:Array.isArray(t)?{type:"BlockStatement",body:t}:t},r)]).body[0]}function v(t){return e.filename+(t&&t.loc&&t.loc.start?"("+t.loc.start.line+":"+t.loc.start.column+")\t":"\t")}function b(e){return n.babelTree?p(e):{type:"Literal",value:e,raw:JSON.stringify(e)}}function x(e){return e?!n.babelTree||"ClassMethod"!==e.type&&"ObjectMethod"!==e.type?(!n.babelTree&&"MethodDefinition"===e.type||"Property"===e.type&&(e.method||"get"==e.kind||"set"==e.kind))&&a(e.value).isFunction?e.value:null:e:null}function w(e,t){if(!a(e).isFunction)throw new Error("Can only replace 'arguments' in functions");return"$usesArguments"in e||(y.treeWalker(e,function(t,r,i){"Identifier"===t.type&&"arguments"===t.name?(i[0].parent.shorthand&&(i[0].parent.shorthand=!1,i[0].parent.key=h("arguments"),e.$usesArguments=!0),"key"!==i[0].field&&(t.name=n.$arguments,e.$usesArguments=!0)):t!==e&&a(t).isFunction?"ArrowFunctionExpression"===t.type&&(w(t),e.$usesArguments=e.$usesArguments||t.$usesArguments):r()}),e.$usesArguments=e.$usesArguments||!1),e.$usesArguments&&"ArrowFunctionExpression"!==e.type}function E(e){return"string"!=typeof e&&(e=e.type.replace(/Statement|Expression/g,"")),n.generatedSymbolPrefix+e+"_"+le++}function S(e,t){return e&&(e.$exit=f({$error:t.$error,$return:t.$return})),e}function k(e,t){var n=function(e){for(var t=0;t<e.length;t++){if(e[t].self.$exit)return e[t].self;if(e[t].parent&&e[t].parent.$exit)return e[t].parent}return null}(e);if(n)return n.$exit;if(t)for(var r=0;r<t.length;r++)if(t[r])return f(t[r]);return null}function A(e,t){var r=!(n.promises||n.generators||n.engine)&&n.lazyThenables;ie(e),ne(e),j(e),H(e),J(e),(r?function(e,t){return y.treeWalker(e,function(e,t,r){function o(e){return{type:"ReturnStatement",argument:{type:"UnaryExpression",operator:"void",prefix:!0,argument:L(e||S)}}}function l(e,t){if("BreakStatement"===e.type)s(e,i(A(e.label&&n.generatedSymbolPrefix+"Loop_"+e.label.name+"_exit")));else if("ContinueStatement"===e.type)s(e,i(o(e.label&&n.generatedSymbolPrefix+"Loop_"+e.label.name+"_next")));else if(a(e).isFunction)return!0;t()}"ForInStatement"===e.type&&c(e)?U(e,r):"ForOfStatement"===e.type&&c(e)&&z(e,r),t();var p;if(a(e).isLoop&&c(e)){var f=e.init,d=e.test||b(!0),g=e.update,v=e.body,x=function(e){return u(e,{type:"ThisExpression"})}(v);f&&(a(f).isStatement||(f={type:"ExpressionStatement",expression:f})),g=g&&{type:"ExpressionStatement",expression:g},v=a(v).isBlockStatement?i(v).body:[i(v)];var w=e.$label&&e.$label.name;w="Loop_"+(w||le++);var E=n.generatedSymbolPrefix+(w+"_exit"),S=n.generatedSymbolPrefix+(w+"_next"),k=h(n.generatedSymbolPrefix+w),A=function(e){return{type:"ReturnStatement",argument:{type:"UnaryExpression",operator:"void",prefix:!0,argument:{type:"CallExpression",callee:h(e||E),arguments:[]}}}},_=C(S,[{type:"ReturnStatement",argument:{type:"CallExpression",callee:x?m(k):k,arguments:[h(E),pe.error]}}]);g&&_.body.body.unshift(g);for(var O=0;O<v.length;O++)y.treeWalker(v[O],l);v.push(i(o()));var T={type:"FunctionExpression",id:k,params:[h(E),pe.error],body:{type:"BlockStatement",body:[_]}};if("DoWhileStatement"===e.type)_.body.body=[{type:"IfStatement",test:i(d),consequent:{type:"BlockStatement",body:i(_.body.body)},alternate:{type:"ReturnStatement",argument:{type:"CallExpression",callee:h(E),arguments:[]}}}],T.body.body=[_].concat(v);else{var N={type:"IfStatement",test:i(d),consequent:{type:"BlockStatement",body:v},alternate:i(A())};T.body.body.push(N)}var P={type:"ExpressionStatement",expression:{type:"AwaitExpression",argument:m(T,b(0))}};for(!f||"VariableDeclaration"!==f.type||"let"!==f.kind&&"const"!==f.kind||("const"===f.kind&&(f.kind="let"),P={type:"BlockStatement",body:[i(f),P]},f=null),p=0;p<r.length;p++){var F=r[p];if("index"in F)return f?F.parent[F.field].splice(F.index,1,i(f),P):F.parent[F.field][F.index]=P,!0}}return!0},t),e}:o)(e),F(e),P(e),V(e,[M,r?o:W,B,R,$]),q(e,t),se(e),ue(e)}function C(e,t){var n={$continuation:!0,type:e?"FunctionDeclaration":"FunctionExpression",id:e?"string"==typeof e?h(e):e:void 0,params:[],body:{type:"BlockStatement",body:i(t)}};return e&&(ce[e]={def:n}),n}function _(e){return{type:"AwaitExpression",argument:H({type:"FunctionExpression",generator:!1,expression:!1,async:!0,params:[],body:{type:"BlockStatement",body:e}}).body.body[0].argument}}function L(e,t){"string"==typeof e&&(e=h(e));var n=y.part("$0.call($1)",[e,[{type:"ThisExpression"}].concat(t||[])]).expr;return e.$thisCall=n,n.$thisCallName=e.name,n}function O(e,t){return{type:"ReturnStatement",argument:L(e,t)}}function T(e,t){return{type:"CallExpression",callee:h(e.$seh+"Finally"),arguments:t?[t]:[]}}function N(e,t){if(Array.isArray(e))return e.map(function(e){return N(e,t)});var r=0,i=0;return y.treeWalker(e,function(e,t,s){if("ReturnStatement"!==e.type||e.$mapped){if("ThrowStatement"===e.type){var o=a(e).isAsync;if(r>0){if(!o)return t(e);delete e.async}return void(!o&&i?t():(e.type="ReturnStatement",e.$mapped=!0,e.argument={type:"CallExpression",callee:k(s,[n]).$error,arguments:[e.argument]}))}return"TryStatement"===e.type?(i++,t(e),void i--):a(e).isFunction?(r++,t(e),void r--):void t(e)}if(r>0){if(!a(e).isAsync)return t(e);delete e.async}return e.$mapped=!0,void(a(e.argument).isUnaryExpression&&"void"===e.argument.operator?e.argument=e.argument.argument:e.argument={type:"CallExpression",callee:k(s,[n]).$return,arguments:e.argument?[e.argument]:[]})},t)}function P(e,t){return Array.isArray(e)?e.map(function(e){return P(e,t)}):(y.treeWalker(e,function(e,t,n){if(t(),"ConditionalExpression"===e.type&&(c(e.alternate)||c(e.consequent))){h(E("condOp"));s(e,_(y.part("if ($0) return $1 ; return $2",[e.test,e.consequent,e.alternate]).body))}},t),e)}function F(e,t){return Array.isArray(e)?e.map(function(e){return F(e,t)}):(y.treeWalker(e,function(e,t,n){if(t(),"LogicalExpression"===e.type&&c(e.right)){var r,i=h(E("logical"+("&&"===e.operator?"And":"Or")));if("||"===e.operator)r="var $0; if (!($0 = $1)) {$0 = $2} return $0";else{if("&&"!==e.operator)throw new Error(v(e)+"Illegal logical operator: "+e.operator);r="var $0; if ($0 = $1) {$0 = $2} return $0"}s(e,_(y.part(r,[i,e.left,e.right]).body))}},t),e)}function $(e,t,n){if("SwitchCase"!==e.type&&a(e).isBlockStatement)for(var r=0;r<e.body.length;){var i=e.body[r];if("SwitchCase"!==i.type&&a(i).isBlockStatement){if(ae(i.body))if(l(i)){var s=E(i),o=e.body.splice(r+1,e.body.length-(r+1));if(o.length){var u=C(s,o);delete ce[s],i.body.push(O(s)),e.body.push(u),r++}else r++}else r++;else e.body.splice.apply(e.body,[r,1].concat(i.body))}else r++}}function B(e,t,n){if("IfStatement"===e.type&&c([e.consequent,e.alternate])){var r=E(e),o=t[0],u={type:"BlockStatement",body:[e]};if("index"in o){var l=o.index,p=o.parent[o.field].splice(l+1,o.parent[o.field].length-(l+1));if(o.replace(u),p.length){var h=O(r);u.body.push(n(C(r,p))),[e.consequent,e.alternate].forEach(function(e){if(e){var t;(t=a(e).isBlockStatement?e.body[e.body.length-1]:e)&&"ReturnStatement"===t.type||("BlockStatement"!==e.type&&s(e,{type:"BlockStatement",body:[i(e)]}),e.$deferred=!0,e.body.push(i(h))),n(e)}}),e.consequent&&e.alternate&&e.consequent.$deferred&&e.alternate.$deferred||u.body.push(i(h))}}else o.parent[o.field]=u}}function R(e,t,n){if(!e.$switched&&"SwitchStatement"===e.type&&c(e.cases)){e.$switched=!0;var r,s,o,a=t[0];if("index"in a){var u=a.index+1;(o=a.parent[a.field].splice(u,a.parent[a.field].length-u)).length&&"BreakStatement"===o[o.length-1].type&&a.parent[a.field].push(o.pop()),r=E(e),s=O(r),a.parent[a.field].unshift(C(r,o)),a.parent[a.field].push(i(s))}return e.cases.forEach(function(e,t){if("SwitchCase"!==e.type)throw new Error("switch contains non-case/default statement: "+e.type);if(c(e.consequent)){var n=e.consequent[e.consequent.length-1];"BreakStatement"===n.type?e.consequent[e.consequent.length-1]=i(s):"ReturnStatement"===n.type||"ThrowStatement"===n.type||(d(v(e)+"switch-case fall-through not supported - added break. See https://github.com/MatAtBread/nodent#differences-from-the-es7-specification"),e.consequent.push(i(s)))}}),!0}}function I(e){return"ReturnStatement"===e.type||"ThrowStatement"===e.type}function j(t,r){return y.treeWalker(t,function(t,i,s){if("TryStatement"===t.type&&!t.$seh&&(a(s[0].parent).isBlockStatement||(s[0].parent[s[0].field]={type:"BlockStatement",body:[t]}),t.$seh=E("Try")+"_",t.$containedAwait=!!c(t),t.$finallyExit=t.finalizer&&function(e){for(var t=0;t<e.length;t++)if(a(e[t].self).isFunction)return e[t].self.async||e[t].self.$wasAsync;return!1}(s)&&!!u(t.finalizer.body,I),t.$containedAwait||t.$finallyExit)){t.$needsMapping=!r||!t.$finallyExit;var o=k(s,[n]);if(t.finalizer&&!t.handler){var l=h(E("exception"));t.handler={type:"CatchClause",param:l,body:{type:"BlockStatement",body:[{type:"ThrowStatement",argument:l}]}}}if(!t.handler&&!t.finalizer){var p=new SyntaxError(v(t.value)+"try requires catch and/or finally clause",e.filename,t.start);throw p.pos=t.start,p.loc=t.loc.start,p}t.finalizer?(S(t.block,{$error:t.$seh+"Catch",$return:T(t,o.$return)}),S(t.handler,{$error:T(t,o.$error),$return:T(t,o.$return)})):S(t.block,{$error:t.$seh+"Catch",$return:o.$return})}i()}),t}function D(e,t){for(var n=0;n<e.length;n++)if(!a(e[n]).isDirective)return void e.splice.apply(e,[n,0].concat(t));e.splice.apply(e,[e.length,0].concat(t))}function M(t,r,s){if(t.$needsMapping){var o,a,u,c=r[0];if(!("index"in c))throw new Error(e.filename+" - malformed try/catch blocks");var l=c.index+1,p=c.parent[c.field].splice(l,c.parent[c.field].length-l);if(p.length){var f=s(g(a=t.$seh+"Post",p,[],k(r,[n]).$error));c.parent[c.field].splice(c.index,0,f),o=y.part("return $0()",[t.finalizer?T(t,h(a)):h(a)]).body[0]}else t.finalizer&&(o=O(T(t)));t.$mapped=!0,o&&(t.block.body.push(i(o)),t.handler.body.body.push(i(o)));var d=k(r,[n]);if(t.handler){var v=h(t.$seh+"Catch");u=i(t.handler.body);var b=g(v.name,u,[i(t.handler.param)],t.finalizer?T(t,d.$error):d.$error);t.handler.body.body=[{type:"CallExpression",callee:v,arguments:[i(t.handler.param)]}],c.parent[c.field].splice(c.index,0,b)}if(t.finalizer){s(t.finalizer);var x={exit:h(t.$seh+"Exit"),value:h(t.$seh+"Value"),body:i(t.finalizer.body)},w=y.part("(function ($value) {                                             $:body;                                                  return $exit && ($exit.call(this, $value));           })",x).expr,E={type:"VariableDeclaration",kind:"var",declarations:[{type:"VariableDeclarator",id:h(t.$seh+"Finally"),init:m({type:"FunctionExpression",params:[x.exit],id:null,body:{type:"BlockStatement",body:[{type:"ReturnStatement",argument:m(w,d.$error)}]}})}]};D(c.parent[c.field],[E]);var S=y.part("return $0()",[t.finalizer?T(t,h(a)):h(a)]).body[0];u.body[u.length-1]=S,t.block.body[t.block.body.length-1]=S,delete t.finalizer}}}function V(e,t,n){function r(e,n){return y.treeWalker(e,function(e,n,s){function o(e){return r(e,s)}i.indexOf(e)<0&&(i.push(e),t.forEach(function(t){t(e,s,o)})),n()},n)}var i=[];return r(e,n),e}function q(e,t,o){return y.treeWalker(e,function(e,u,l){if("IfStatement"==e.type&&("BlockStatement"!=e.consequent.type&&c(e.consequent)&&(e.consequent={type:"BlockStatement",body:[e.consequent]}),e.alternate&&"BlockStatement"!=e.alternate.type&&c(e.alternate)&&(e.alternate={type:"BlockStatement",body:[e.alternate]})),u(),a(e).isAwait){var p=e.loc;if(!(t=t||l.some(function(e){return e.self&&e.self.$wasAsync}))||"warn"===t){var f=v(e)+"'await' used inside non-async function. ";n.promises?f+="'return' value Promise runtime-specific":f+="'return' value from await is synchronous",d(f+". See https://github.com/MatAtBread/nodent#differences-from-the-es7-specification")}var y=l[0].parent;"LogicalExpression"===y.type&&y.right===e&&d(v(e.argument)+"'"+r(y)+"' on right of "+y.operator+" will always evaluate '"+r(e.argument)+"'"),"ConditionalExpression"===y.type&&y.test!==e&&d(v(e.argument)+"'"+r(y)+"' will always evaluate '"+r(e.argument)+"'");var g=h(E("await")),b=i(e.argument);s(e,g);for(var x,w,S=1;S<l.length;S++)if(w=a(l[S].self).isBlockStatement){x=l[S-1];break}if(!x)throw new Error(v(e)+"Illegal await not contained in a statement");var A,C,_=k(l,[o,n]),L=x.index,O=w.splice(L,w.length-L).slice(1);"ReturnStatement"===x.self.type&&"CallExpression"===x.self.argument.type&&1===x.self.argument.arguments.length&&x.self.argument.arguments[0].name===g.name?C=A=x.self.argument.callee:"Identifier"===x.self.type||x.self.name===g.name||"ExpressionStatement"===x.self.type&&"Identifier"===x.self.expression.type&&x.self.expression.name===g.name?A=O.length?{type:"FunctionExpression",params:[i(g)],body:q({type:"BlockStatement",body:i(O)},t,_)}:{type:"FunctionExpression",params:[],body:{type:"BlockStatement",body:[]}}:(O.unshift(x.self),A={type:"FunctionExpression",params:[i(g)],body:q({type:"BlockStatement",body:i(O)},t,_)}),C||(C=A?m(A,_.$error):{type:"FunctionExpression",params:[],body:{type:"BlockStatement",body:[]}}),n.wrapAwait&&(b={type:"CallExpression",arguments:[b],callee:n.promises||n.generators?{type:"MemberExpression",object:h("Promise"),property:h("resolve")}:{type:"MemberExpression",object:h("Object"),property:h("$makeThenable")}});var T={type:"CallExpression",callee:{type:"MemberExpression",object:b,property:h("then",p),computed:!1},arguments:[C,_.$error]};w.push({loc:p,type:"ReturnStatement",argument:T})}return!0}),e}function U(e,t){var n=e.$label;delete e.$label;var r=h(E("idx")),i=h(E("in")),o=y.part("var $0,$1 = [];for ($0 in $2) $1.push($0)",[r,i,e.right]).body,u=y.part("for ($0; $1.length;){ $2 = $1.shift(); $:3 ; }",[e.left,i,"VariableDeclaration"===e.left.type?e.left.declarations[0].id:e.left,e.body]).body[0];u.$label=n;for(var c=0;c<t.length;c++)if(a(t[c].parent).isBlockStatement){t[c].parent[t[c].field].splice(t[c].index,0,o[0],o[1]);break}s(e,u)}function z(e,t){"BlockStatement"!==e.body.type&&(e.body={type:"BlockStatement",body:[e.body]});var n,r,i=y.part("[$0[Symbol.iterator]()]",[e.right]).expr;if("VariableDeclaration"===e.left.type){"const"===e.left.kind&&(e.left.kind="let"),n=e.left.declarations[0].id;var s=te(e.left.declarations[0].id);r=h(E("iterator_"+s.join("_"))),e.left.declarations=s.map(function(e){return{type:"VariableDeclarator",id:h(e)}}),e.left.declarations.push({type:"VariableDeclarator",id:r,init:i}),e.init=e.left}else{var o={type:"VariableDeclaration",kind:"var",declarations:[{type:"VariableDeclarator",id:r=h(E("iterator_"+(n=e.left).name)),init:i}]};e.init=o}e.type="ForStatement",e.test=y.part("!($0[1] = $0[0].next()).done && (($1 = $0[1].value) || true)",[r,n]).expr,delete e.left,delete e.right}function W(e,t,r){var i=e.$depth;"ForInStatement"===e.type&&c(e)?U(e,t):"ForOfStatement"===e.type&&c(e)&&z(e);var s=t.some(function(e){return"$label"in e.self&&"ForStatement"===e.self.type&&e.self.$mapped});if(!e.$mapped&&a(e).isLoop&&u(e,function(e){return"AwaitExpression"===e.type&&!e.$hidden||s&&("BreakStatement"===e.type||"ContinueStatement"===e.type)&&e.label})){t[0].self.$mapped=!0;var o=[],l=e.init,p=e.test||b(!0),f=e.update,d=e.body;f&&(f={type:"ExpressionStatement",expression:f}),l&&(a(l).isStatement||(l={type:"ExpressionStatement",expression:l}),o.push(l));var m,g;e.$label?(g=e.$label.name,m=t[1]):(g=le++,m=t[0]);var v,x,w=h((g=n.generatedSymbolPrefix+"Loop_"+g)+"_trampoline"),E=h(g),S=f?h(g+"_step"):E,A=h(g+"_exit");h("q"),h("$exception");if("index"in m){var _=m.index;x=m.parent[m.field].splice(_+1,m.parent[m.field].length-(_+1))}else x=[];v=C(A,x);var L={type:"ReturnStatement",argument:E},O={type:"ReturnStatement",argument:S},T={type:"ReturnStatement",argument:{type:"ArrayExpression",elements:[b(1)]}};y.treeWalker(d,function(e,t,n){if(a(e).isFunction||a(e).isLoop)return!0;if("BreakStatement"===e.type||"ContinueStatement"===e.type)if(e.label)for(var r=n.filter(function(e){return"$label"in e.self}).map(function(e,t){return e.self.$label&&e.self.$label.name}),i=[],s=0;s<r.length;s++){if(r[s]===e.label.name){"BreakStatement"===e.type&&i.push(b(1)),n[0].replace({type:"ReturnStatement",argument:{type:"ArrayExpression",elements:i.reverse()}});break}i.push(b(0))}else"BreakStatement"===e.type?n[0].replace(T):n[0].replace(O);else t()},t),d="BlockStatement"===d.type?d.body.slice(0):[d],d="DoWhileStatement"===e.type?d.concat({type:"IfStatement",test:{type:"UnaryExpression",argument:p,prefix:!0,operator:"!"},consequent:T,alternate:O}):[{type:"IfStatement",test:p,consequent:{type:"BlockStatement",body:d.concat(O)},alternate:T}],n.noRuntime&&o.push({type:"VariableDeclaration",declarations:[{type:"VariableDeclarator",id:w}],kind:"var"});var N,P=k(t,[n]).$error;N=n.noRuntime?y.part(n.es6target?"($idTrampoline = ((q) => {     $$setMapped: while (q) {          if (q.then)  "+(1===i?"             return void q.then($idTrampoline, $exit); ":"             return q.then($idTrampoline, $exit); ")+"         try {              if (q.pop)                   if (q.length)                   return q.pop() ? $idContinuation.call(this) : q;               else                   q = $idStep;               else                   q = q.call(this)           } catch (_exception) {              return $exit(_exception);           }      } }))($idIter)":"($idTrampoline = (function (q) {     $$setMapped: while (q) {          if (q.then)  "+(1===i?"             return void q.then($idTrampoline, $exit); ":"             return q.then($idTrampoline, $exit); ")+"         try {              if (q.pop)                   if (q.length)                   return q.pop() ? $idContinuation.call(this) : q;               else                   q = $idStep;               else                   q = q.call(this)           } catch (_exception) {              return $exit(_exception);           }      } }).bind(this))($idIter)",{setMapped:function(e){return e.$mapped=!0,e},idTrampoline:w,exit:P,idIter:E,idContinuation:A,idStep:S}).expr:y.part("(Function.$0.trampoline(this,$1,$2,$3,$5)($4))",[pe.asyncbind,A,S,P,E,b(1===i)]).expr,o.push({type:"ReturnStatement",argument:N}),o.push({$label:e.$label,type:"FunctionDeclaration",id:E,params:[],body:{type:"BlockStatement",body:d}}),f&&o.push({type:"FunctionDeclaration",id:S,params:[],body:{type:"BlockStatement",body:[f,L]}}),!l||"VariableDeclaration"!==l.type||"let"!==l.kind&&"const"!==l.kind?(o.push(v),t[0].replace(o.map(r))):("const"===l.kind&&(l.kind="let"),t[0].replace([{type:"BlockStatement",body:o.map(r)},r(v)]))}}function G(e){if(!a(e).isFunction)throw new Error("Cannot examine non-Function node types for async exits");return u(e.body,function(e){return"Identifier"===e.type&&(e.name===n.$return||e.name===n.$error)||I(e)&&a(e).isAsync},function(e){return!(a(e).isFunction&&(e.$wasAsync||a(e).isAsync))})}function J(t){return y.treeWalker(t,function(t,r,i){var s=x(t);if(r(),s&&a(s).isAsync){if("set"==t.kind){var o=new SyntaxError(v(s)+"method 'async set' cannot be invoked",e.filename,t.start);throw o.pos=t.start,o.loc=t.loc.start,o}s.async=!1;var u=w(s);G(s)||0!==s.body.body.length&&"ReturnStatement"===s.body.body[s.body.body.length-1].type||s.body.body.push({type:"ReturnStatement"});var c=m(S({type:"FunctionExpression",params:[pe.return,pe.error],body:J(N(s.body,i)),$wasAsync:!0},n),n.promises||n.generators||n.engine?null:b(!n.lazyThenables||0));n.promises?s.body={type:"BlockStatement",body:[{type:"ReturnStatement",argument:{type:"NewExpression",callee:h("Promise"),arguments:[c]}}]}:s.body={type:"BlockStatement",body:[{type:"ReturnStatement",argument:c}]},u&&D(s.body.body,[he])}})}function H(e){return y.treeWalker(e,function(e,t,r){if(t(),a(e).isAsync&&a(e).isFunction){var i;(i=x(r[0].parent))&&a(i).isAsync&&"get"===r[0].parent.kind&&X(r[0].parent.key),delete e.async;var s=w(e),o=S({type:"FunctionExpression",params:[pe.return,pe.error],$wasAsync:!0},n),u=[{self:o}].concat(r);return a(e.body).isBlockStatement?(G(e)||0!==e.body.body.length&&"ReturnStatement"===e.body.body[e.body.body.length-1].type||e.body.body.push({type:"ReturnStatement"}),o.body={type:"BlockStatement",body:e.body.body.map(function(e){return N(e,u)})}):(o.body={type:"BlockStatement",body:[N({type:"ReturnStatement",argument:e.body},u)]},e.expression=!1),o=m(o,n.promises||n.generators||n.engine?null:b(!n.lazyThenables||0)),n.promises&&(o={type:"NewExpression",callee:h("Promise"),arguments:[o]}),o={type:"BlockStatement",body:[{type:"ReturnStatement",loc:e.loc,argument:o}]},s&&D(o.body,[he]),void(e.body=o)}}),e}function Y(e){if(Array.isArray(e))return e.map(Y);var t=0;return y.treeWalker(e,function(e,n,r){if("ThrowStatement"!==e.type&&"ReturnStatement"!==e.type||e.$mapped){if(a(e).isFunction)return t++,n(e),void t--}else if(t>0&&a(e).isAsync)return delete e.async,e.argument={type:"CallExpression",callee:"ThrowStatement"===e.type?pe.error:pe.return,arguments:e.argument?[e.argument]:[]},void(e.type="ReturnStatement");n(e)})}function Q(e,t){if(n.noRuntime)throw new Error("Nodent: 'noRuntime' option only compatible with -promise and -engine modes");return y.part("{ return (function*($return,$error){ $:body }).$asyncspawn(Promise,this) }",{return:pe.return,error:pe.error,asyncspawn:pe.asyncspawn,body:Y(e).concat(t?[{type:"ReturnStatement",argument:pe.return}]:[])}).body[0]}function X(e){e.$asyncgetwarninig||(e.$asyncgetwarninig=!0,d(v(e)+"'async get "+r(e)+"(){...}' is non-standard. See https://github.com/MatAtBread/nodent#differences-from-the-es7-specification"))}function Z(e,t){function r(e,t){y.treeWalker(e,function(n,r,i){n!==e&&a(n).isFunction||(a(n).isAwait?t?(n.$hidden=!0,r()):(delete n.operator,n.delegate=!1,n.type="YieldExpression",r()):r())})}function o(e){var t=n.promises;n.promises=!0,A(e,!0),n.promises=t}function u(e){return"BlockStatement"!==e.body.type&&(e.body={type:"BlockStatement",body:[{type:"ReturnStatement",argument:e.body}]}),e}function c(e,n){n.$asyncexitwarninig||(n.$asyncexitwarninig=!0,d(v(e)+"'async "+{ReturnStatement:"return",ThrowStatement:"throw"}[e.type]+"' not possible in "+(t?"engine":"generator")+" mode. Using Promises for function at "+v(n)))}y.treeWalker(e,function(e,n,i){n();var l,p,h;if(a(e).isAsync&&a(e).isFunction){var f;(f=x(i[0].parent))&&a(f).isAsync&&"get"===i[0].parent.kind&&X(i[0].parent.key),(p=G(e))?(c(p,e.body),o(e)):t?"get"!==i[0].parent.kind&&r(e,!0):(delete(l=e).async,h=w(l),r(l,!1),(l=u(l)).body=Q(l.body.body,p),h&&D(l.body.body,[he]),l.id&&"ExpressionStatement"===i[0].parent.type?(l.type="FunctionDeclaration",i[1].replace(l)):i[0].replace(l))}else(l=x(e))&&a(l).isAsync&&((p=G(l))?(c(p,l),o(e)):t&&"get"!==e.kind||(t?o(e):(e.async=!1,h=w(l),r(l,!1),s(l,u(l)),l.body=Q(l.body.body,p)),h&&D(l.body.body,[he])))});var l=i(n);return n.engine=!1,n.generators=!1,ie(e),ne(e),j(e,l.engine),F(e),P(e),V(e,[M,W,B,R,$]),q(e,"warn"),n.engine=l.engine,n.generators=l.generators,e}function K(e,t,n){var r=[];return y.treeWalker(e,function(i,s,o){if(i===e)return s();t(i,o)?r.push([].concat(o)):n||a(i).isScope||s()}),r}function ee(e,t){var n=[],r={};if((e=e.filter(function(e){return"ExportNamedDeclaration"!==e[0].parent.type})).length){var s={};e.forEach(function(e){function t(e){e in s?r[e]=o.declarations[u]:s[e]=o.declarations[u]}for(var n=e[0],o=n.self,a=(o.kind,[]),u=0;u<o.declarations.length;u++){var c=o.declarations[u];if(te(c.id).forEach(t),c.init){var l={type:"AssignmentExpression",left:i(c.id),operator:"=",right:i(c.init)};a.push(l)}}if(0==a.length)n.remove();else{var p=a.length>1?{type:"SequenceExpression",expressions:a}:a[0];"For"!==n.parent.type.slice(0,3)&&(p={type:"ExpressionStatement",expression:p}),n.replace(p)}});var o=Object.keys(s);o.length&&(o=o.map(function(e){return{type:"VariableDeclarator",id:h(e),loc:s[e].loc,start:s[e].start,end:s[e].end}}),n[0]&&"VariableDeclaration"===n[0].type?n[0].declarations=n[0].declarations.concat(o):n.unshift({type:"VariableDeclaration",kind:t,declarations:o}))}return{decls:n,duplicates:r}}function te(e){if(!e)return[];if(Array.isArray(e))return e.reduce(function(e,t){return e.concat(te(t.id))},[]);switch(e.type){case"Identifier":return[e.name];case"AssignmentPattern":return te(e.left);case"ArrayPattern":return e.elements.reduce(function(e,t){return e.concat(te(t))},[]);case"ObjectPattern":return e.properties.reduce(function(e,t){return e.concat(te(t))},[]);case"ObjectProperty":case"Property":return te(e.value);case"RestElement":case"RestProperty":return te(e.argument)}}function ne(e){function t(e){return u(e,function(e){return"AssignmentExpression"===e.type})}function n(e){return function(t,n){if("VariableDeclaration"===t.type&&(t.kind=t.kind||"var")&&e.indexOf(t.kind)>=0){var r=n[0];return("left"!=r.field||"ForInStatement"!==r.parent.type&&"ForOfStatement"!==r.parent.type)&&("init"!=r.field||"ForStatement"!==r.parent.type||"const"!==t.kind&&"let"!==t.kind)}}}function o(e,t){return!("FunctionDeclaration"!==e.type||!e.id)&&(a(e).isAsync||!e.$continuation)}var l={TemplateLiteral:function(e){return e.expressions},NewExpression:function(e){return e.arguments},CallExpression:function(e){return e.arguments},SequenceExpression:function(e){return e.expressions},ArrayExpression:function(e){return e.elements},ObjectExpression:function(e){return e.properties.map(function(e){return e.value})}};y.treeWalker(e,function(e,n,r){function o(e){h.length&&(e.argument={type:"SequenceExpression",expressions:h.map(function(e){var t=i(e);return s(e,e.left),t}).concat(e.argument)},h=[])}var u;if(n(),e.type in l&&!e.$hoisted){var p=l[e.type](e),h=[];for(u=0;u<p.length;u++)if(!a(p[u]).isScope){if((r=c(p[u]))&&o(r[0].self),!c(p.slice(u+1)))break;(r=t(p[u]))&&h.push(r[0].self)}}else if("VariableDeclaration"===e.type)for(u=e.declarations.length-1;u>0;u--)if(e.declarations[u]&&e.declarations[u].init&&c(e.declarations[u].init)){var f={type:"VariableDeclaration",kind:e.kind,declarations:e.declarations.splice(u)},d=r[0];if(!("index"in d))throw new Error("VariableDeclaration not in a block");d.parent[d.field].splice(d.index+1,0,f)}}),function(e){function t(e){d(v(e)+"Possible assignment to 'const "+r(e)+"'")}function n(e){switch(e.type){case"Identifier":"const"===i[e.name]&&t(e);break;case"ArrayPattern":e.elements.forEach(function(e){"const"===i[e.name]&&t(e)});break;case"ObjectPattern":e.properties.forEach(function(e){"const"===i[e.key.name]&&t(e)})}}var i={};y.treeWalker(e,function(e,t,r){var s=a(e).isBlockStatement;if(s){i=Object.create(i);for(var o=0;o<s.length;o++)if("VariableDeclaration"===s[o].type)for(var u=0;u<s[o].declarations.length;u++)te(s[o].declarations[u].id).forEach(function(e){i[e]=s[o].kind})}t(),"AssignmentExpression"===e.type?n(e.left):"UpdateExpression"===e.type&&n(e.argument),s&&(i=Object.getPrototypeOf(i))})}(e);var p=!1;return y.treeWalker(e,function(e,t,i){var s=p;if(p=p||oe(e),a(e).isBlockStatement){if(c(e)){var l,f,y,m,g;if(!i[0].parent||a(i[0].parent).isScope){var x={},w={};(f=K(e,n(["const"]),!1)).forEach(function(e){e[0].self.declarations.forEach(function(e){te(e.id).forEach(function(t){x[t]||w[t]?(delete x[t],w[t]=e):x[t]=e})})}),f.forEach(function(e){for(var t=0;t<e.length&&!a(e[t].parent).isBlockStatement;t++);var n=e[t];n.append({type:"ExpressionStatement",expression:{type:"SequenceExpression",expressions:e[0].self.declarations.map(function(e){var t={type:"AssignmentExpression",operator:"=",left:e.id,right:e.init};return e.init=null,t})}});var r=te(e[0].self.declarations),i=r.filter(function(e){return e in w});i.length&&e[0].append({type:"VariableDeclaration",kind:"let",declarations:i.map(function(e){return{type:"VariableDeclarator",id:h(e)}})}),e[0].self.kind="var",(i=r.filter(function(e){return e in x})).length?e[0].self.declarations=i.map(function(e){return{type:"VariableDeclarator",id:h(e)}}):n.remove()}),y=K(e,n(["var"]),!1),m=[]}else m=K(e,n(["const"]),!0);m=m.concat(K(e,n(["let"]),!0)),l=K(e,function(e){return a(e).isDirective},!0),g=K(e,o,p),y=y?ee(y,"var"):{duplicates:{},decls:[]},m=m?ee(m,"let"):{duplicates:{},decls:[]},Object.keys(y.duplicates).forEach(function(e){d(v(y.duplicates[e])+"Duplicate declaration '"+r(y.duplicates[e])+"'")}),Object.keys(m.duplicates).forEach(function(e){d(v(m.duplicates[e])+"Duplicate declaration '"+r(m.duplicates[e])+"'")}),g=g.map(function(e){var t,n=e[0];if(a(n.self).isAsync)return t=n.self.id.name,a(n.parent).isBlockStatement?(n.self.type="FunctionDeclaration",n.remove(),n.self):n.replace(h(t));t=n.self.id.name;return"FunctionDeclaration"===n.self.type?n.remove():n.replace(h(t))}),((l=l.map(function(e){return e[0].remove()})).length||y.decls.length||m.decls.length||g.length)&&(e.body=l.concat(y.decls).concat(m.decls).concat(g).concat(e.body))}p=s}if("LabeledStatement"===e.type&&"BlockStatement"===e.body.type&&c(e.body)&&u(e.body,function(e){return"BreakStatement"===e.type&&e.label})&&(e.body.body.push({type:"BreakStatement"}),e.body={type:"DoWhileStatement",test:b(0),body:e.body}),t(),"ForOfStatement"===e.type||"ForInStatement"===e.type||a(e).isLoop){for(var E=0,S=0;S<i.length;S++)if("ForOfStatement"===i[S].self.type||"ForInStatement"===i[S].self.type||a(i[S].self).isLoop)E+=1;else if(a(i[S].self).isFunction)break;e.$depth=E,"LabeledStatement"===i[0].parent.type?e.$label=i[0].parent.label:e.$label=null}return!0}),e}function re(e,t){return y.treeWalker(e,function(e,r,i){if(r(),("ClassDeclaration"===e.type||"ClassExpression"===e.type)&&(e.body.body.forEach(function(e,t){function n(){return e.$superID=e.$superID||h("$super$"+le++)}return function(e){(e=x(e))&&a(e).isAsync&&(!t||"get"===e.kind||u(e,function(e){return a(e).isFunction&&u(e,function(e){return"Super"===e.type})&&u(e,function(e){return e.async&&("ReturnStatement"===e.type||"ThrowStatement"===e.type)})},!0))&&y.treeWalker(e.body,function(e,t,r){var i;a(e).isClass||(t(),"Super"===e.type&&("MemberExpression"===r[0].parent.type?"CallExpression"===r[1].parent.type&&"callee"===r[1].field?(i=y.part("this.$super($field).call(this,$args)",{super:n(),field:r[0].parent.computed?r[0].parent.property:b(r[0].parent.property.name),args:r[1].parent.arguments}).expr,r[2].replace(i)):(i=y.part("this.$super($field)",{super:n(),field:r[0].parent.computed?r[0].parent.property:b(r[0].parent.property.name)}).expr,r[1].replace(i)):d(v(e)+"'super' in async methods must be deferenced. 'async constructor()'/'await super()' not valid.")))})}}(e,t)),e.$superID)){var s=y.part("(function($field) { return super[$field] })",{field:h("$field")}).expr;n.babelTree?(s.type="ClassMethod",s.key=e.$superID,s.kind="method",e.body.body.push(s)):e.body.body.push({type:"MethodDefinition",key:e.$superID,kind:"method",value:s})}})}function ie(e){return y.treeWalker(e,function(e,t,n){return c(e)&&"ArrowFunctionExpression"===e.type&&"BlockStatement"!==e.body.type&&(e.body={type:"BlockStatement",body:[{type:"ReturnStatement",argument:e.body}]}),t(),!0}),e}function se(e){return y.treeWalker(e,function(e,t,r){t(),"Identifier"===e.type&&"__nodent"===e.name&&s(e,b(n))}),e}function oe(e){if("Program"===e.type&&"module"===e.sourceType)return!0;var t;if("Program"===e.type)t=e.body;else{if(!a(e).isFunction)return!1;t=e.body.body}if(t)for(var n=0;n<t.length;n++)if(a(t[n]).isDirective&&t[n].expression.value.match(/^\s*use\s+strict\s*$/))return!0;return!1}function ae(e){for(var t=0;t<e.length;t++){var n=e[t];if("ClassDeclaration"===n.type||"VariableDeclaration"===n.type&&("let"===n.kind||"const"===n.kind)||"FunctionDeclaration"===n.type&&n.id&&n.id.name&&!n.$continuation)return!0}return!1}function ue(e){y.treeWalker(e,function(e,t,n){if(t(),"ArrowFunctionExpression"===e.type&&"BlockStatement"===e.body.type&&1===e.body.body.length&&"ReturnStatement"===e.body.body[0].type)e.body=e.body.body[0].argument;else{var r,i;if(r=a(e).isBlockStatement)for(var s=0;s<r.length;s++)(i=a(r[s]).isBlockStatement)&&!ae(i)&&(ae(r[s])||[].splice.apply(r,[s,1].concat(i)))}}),y.treeWalker(e,function(e,t,n){if(t(),a(e).isJump){var r=n[0];if("index"in r)for(var i=r.index+1,s=r.parent[r.field];i<s.length;)"VariableDeclaration"===s[i].type||a(s[i]).isFunction&&s[i].id?i+=1:s.splice(i,1)}}),y.treeWalker(e,function(e,t,n){t(),e.$thisCall&&ce[e.name]&&(ce[e.name].ref?delete ce[e.name]:ce[e.name].ref=e.$thisCall)});var t=Object.keys(ce).map(function(e){return ce[e].ref});if(t.length){y.treeWalker(e,function(e,n,r){if(n(),t.indexOf(e)>=0&&"ReturnStatement"===r[1].self.type){var s=e.$thisCallName,o=i(ce[s].def.body.body);ce[s].$inlined=!0,a(r[1].self).isJump||o.push({type:"ReturnStatement"}),r[1].replace(o)}});var n=Object.keys(ce).map(function(e){return ce[e].$inlined&&ce[e].def});y.treeWalker(e,function(e,t,r){t(),n.indexOf(e)>=0&&r[0].remove()})}if(!("Program"===e.type&&"module"===e.sourceType||u(e,function(e){return a(e).isES6},!0))){var r=oe(e);!function(e){y.treeWalker(e,function(e,t,n){if("Program"===e.type||"FunctionDeclaration"===e.type||"FunctionExpression"===e.type){var i=r;if(r=r||oe(e)){t();var s="Program"===e.type?e:e.body,o=K(s,function(e,t){if("FunctionDeclaration"===e.type)return t[0].parent!==s});o=o.map(function(e){return e[0].remove()}),[].push.apply(s.body,o)}else t();r=i}else t()})}(e)}return y.treeWalker(e,function(e,t,n){t(),Object.keys(e).filter(function(e){return"$"===e[0]}).forEach(function(t){delete e[t]})}),e}var ce={},le=1,pe={};Object.keys(n).filter(function(e){return"$"===e[0]}).forEach(function(e){pe[e.slice(1)]=h(n[e])});var he=y.part("var $0 = arguments",[pe.arguments]).body[0];return n.engine?(e.ast=re(e.ast,!0),e.ast=Z(e.ast,n.engine),e.ast=se(e.ast),ue(e.ast)):n.generators?(e.ast=re(e.ast),e.ast=Z(e.ast),e.ast=se(e.ast),ue(e.ast)):(e.ast=re(e.ast),A(e.ast)),n.babelTree&&y.treeWalker(e.ast,function(e,t,n){t(),"Literal"===e.type&&s(e,b(e.value))}),e}var y=e("./parser"),m=e("./output"),g={start:!0,end:!0,loc:!0,range:!0},v={getScope:function(){return"FunctionDeclaration"===this.node.type||"FunctionExpression"===this.node.type||"Function"===this.node.type||"ObjectMethod"===this.node.type||"ClassMethod"===this.node.type||"ArrowFunctionExpression"===this.node.type&&"BlockStatement"===this.node.body.type?this.node.body.body:"Program"===this.node.type?this.node.body:null},isScope:function(){return"FunctionDeclaration"===this.node.type||"FunctionExpression"===this.node.type||"Function"===this.node.type||"Program"===this.node.type||"ObjectMethod"===this.node.type||"ClassMethod"===this.node.type||"ArrowFunctionExpression"===this.node.type&&"BlockStatement"===this.node.body.type},isFunction:function(){return"FunctionDeclaration"===this.node.type||"FunctionExpression"===this.node.type||"Function"===this.node.type||"ObjectMethod"===this.node.type||"ClassMethod"===this.node.type||"ArrowFunctionExpression"===this.node.type},isClass:function(){return"ClassDeclaration"===this.node.type||"ClassExpression"===this.node.type},isBlockStatement:function(){return"ClassBody"===this.node.type||"Program"===this.node.type||"BlockStatement"===this.node.type?this.node.body:"SwitchCase"===this.node.type&&this.node.consequent},isExpressionStatement:function(){return"ExpressionStatement"===this.node.type},isLiteral:function(){return"Literal"===this.node.type||"BooleanLiteral"===this.node.type||"RegExpLiteral"===this.node.type||"NumericLiteral"===this.node.type||"StringLiteral"===this.node.type||"NullLiteral"===this.node.type},isDirective:function(){return"ExpressionStatement"===this.node.type&&("StringLiteral"===this.node.expression.type||"Literal"===this.node.expression.type&&"string"==typeof this.node.expression.value)},isUnaryExpression:function(){return"UnaryExpression"===this.node.type},isAwait:function(){return"AwaitExpression"===this.node.type&&!this.node.$hidden},isAsync:function(){return this.node.async},isStatement:function(){return null!==this.node.type.match(/[a-zA-Z]+Declaration/)||null!==this.node.type.match(/[a-zA-Z]+Statement/)},isExpression:function(){return null!==this.node.type.match(/[a-zA-Z]+Expression/)},isLoop:function(){return"ForStatement"===this.node.type||"WhileStatement"===this.node.type||"DoWhileStatement"===this.node.type},isJump:function(){return"ReturnStatement"===this.node.type||"ThrowStatement"===this.node.type||"BreakStatement"===this.node.type||"ContinueStatement"===this.node.type},isES6:function(){switch(this.node.type){case"ExportNamedDeclaration":case"ExportSpecifier":case"ExportDefaultDeclaration":case"ExportAllDeclaration":case"ImportDeclaration":case"ImportSpecifier":case"ImportDefaultSpecifier":case"ImportNamespaceSpecifier":case"ArrowFunctionExpression":case"ForOfStatement":case"YieldExpression":case"Super":case"RestElement":case"RestProperty":case"SpreadElement":case"TemplateLiteral":case"ClassDeclaration":case"ClassExpression":return!0;case"VariableDeclaration":return this.node.kind&&"var"!==this.node.kind;case"FunctionDeclaration":case"FunctionExpression":return!!this.node.generator}}},b={};Object.keys(v).forEach(function(e){Object.defineProperty(b,e,{get:v[e]})}),t.exports={printNode:r,babelLiteralNode:p,asynchronize:function(e,t,n,r){try{return d(e,0,n,r)}catch(t){if(t instanceof SyntaxError){var i=e.origCode.substr(t.pos-t.loc.column);i=i.split("\n")[0],t.message+=" (nodent)\n"+i+"\n"+i.replace(/[\S ]/g,"-").substring(0,t.loc.column)+"^",t.stack=""}throw t}}}},{"./output":12,"./parser":13}],12:[function(e,t,n){"use strict";function r(e){if("NewExpression"===e.type&&e.arguments&&e.arguments.length)return 19;var t=h[e.type]||h[e.type+e.operator]||h[e.type+e.operator+(e.prefix?"prefix":"")];return void 0!==t?t:20}var i,s,o,a,u,c,l=e("source-map").SourceMapGenerator;if("".repeat)c=function(e,t){return t&&e?e.repeat(t):""};else{var p={};c=function(e,t){if(!t||!e)return"";var n=""+e+t;if(!p[n]){for(var r=[];t--;)r.push(e);p[n]=r.join("")}return p[n]}}var h={ExpressionStatement:-1,Identifier:21,Literal:21,BooleanLiteral:21,RegExpLiteral:21,NumericLiteral:21,StringLiteral:21,NullLiteral:21,ThisExpression:21,SuperExpression:21,ObjectExpression:21,ClassExpression:21,MemberExpression:19,CallExpression:18,NewExpression:18,ArrayExpression:17.5,FunctionExpression:17.5,FunctionDeclaration:17.5,ArrowFunctionExpression:17.5,"UpdateExpression++":17,"UpdateExpression--":17,"UpdateExpression++prefix":16,"UpdateExpression--prefix":16,UnaryExpression:16,AwaitExpression:16,"BinaryExpression**":15,"BinaryExpression*":15,"BinaryExpression/":15,"BinaryExpression%":15,"BinaryExpression+":14,"BinaryExpression-":14,"BinaryExpression<<":13,"BinaryExpression>>":13,"BinaryExpression>>>":13,"BinaryExpression<":12,"BinaryExpression<=":12,"BinaryExpression>":12,"BinaryExpression>=":12,BinaryExpressionin:12,BinaryExpressioninstanceof:12,"BinaryExpression==":11,"BinaryExpression===":11,"BinaryExpression!=":11,"BinaryExpression!==":11,"BinaryExpression&":10,"BinaryExpression^":9,"BinaryExpression|":8,"LogicalExpression&&":7,"LogicalExpression||":6,ConditionalExpression:5,AssignmentPattern:4,AssignmentExpression:4,yield:3,YieldExpression:3,SpreadElement:2,"comma-separated-list":1.5,SequenceExpression:1},f={type:"comma-separated-list"},d={out:function(e,t,n){var r=this[n||e.type];r?r.call(this,e,t):t.write(e,"/*"+e.type+"?*/ "+t.sourceAt(e.start,e.end))},expr:function(e,t,n,i){2===i||r(n)<r(t)||r(n)==r(t)&&(i||t.right===n)?(e.write(null,"("),this.out(n,e,n.type),e.write(null,")")):this.out(n,e,n.type)},argumentList:function(e,t){t.write(e,"(");var n=e.arguments;if(n.length>0)for(var r=n.length,i=0;i<r;i++)0!=i&&t.write(null,", "),this.expr(t,f,n[i]);t.write(null,")")},formatParameters:function(e,t){var n;if(t.write(null,"("),null!=e&&e.length>0){this.out(e[0],t,e[0].type);for(var r=1,i=e.length;r<i;r++)n=e[r],t.write(n,", "),this.out(n,t,n.type)}t.write(null,") ")},Program:function(e,t){for(var n,r,i=c(t.indent,t.indentLevel),s=t.lineEnd,o=0,a=(n=e.body).length;o<a;o++)r=n[o],t.write(null,i),this.out(r,t,r.type),t.write(null,s)},BlockStatement:u=function(e,t){var n,r,i=c(t.indent,t.indentLevel++),s=t.lineEnd,o=i+t.indent;if(t.write(e,"{"),null!=(n=e.body)&&n.length>0){t.write(null,s);for(var a=0,u=n.length;a<u;a++)r=n[a],t.write(null,o),this.out(r,t,r.type),t.write(null,s);t.write(null,i)}t.write(e.loc?{loc:{start:{line:e.loc.end.line,column:0}}}:null,"}"),t.indentLevel--},ClassBody:u,EmptyStatement:function(e,t){t.write(e,";")},ParenthesizedExpression:function(e,t){this.expr(t,e,e.expression,2)},ExpressionStatement:function(e,t){"FunctionExpression"===e.expression.type||"ObjectExpression"===e.expression.type?(t.write(null,"("),this.expr(t,e,e.expression),t.write(null,")")):this.expr(t,e,e.expression),t.write(null,";")},IfStatement:function(e,t){t.write(e,"if ("),this.out(e.test,t,e.test.type),t.write(null,") "),"BlockStatement"!==e.consequent.type&&t.write(null,t.lineEnd,c(t.indent,t.indentLevel+1)),this.out(e.consequent,t,e.consequent.type),null!=e.alternate&&("BlockStatement"!==e.consequent.type&&t.write(null,t.lineEnd,c(t.indent,t.indentLevel)),t.write(null," else "),"BlockStatement"!==e.alternate.type&&"IfStatement"!==e.alternate.type&&t.write(null,t.lineEnd,c(t.indent,t.indentLevel+1)),this.out(e.alternate,t,e.alternate.type))},LabeledStatement:function(e,t){this.out(e.label,t,e.label.type),t.write(null,":"),this.out(e.body,t,e.body.type)},BreakStatement:function(e,t){t.write(e,"break"),e.label&&(t.write(null," "),this.out(e.label,t,e.label.type)),t.write(null,";")},ContinueStatement:function(e,t){t.write(e,"continue"),e.label&&(t.write(null," "),this.out(e.label,t,e.label.type)),t.write(null,";")},WithStatement:function(e,t){t.write(e,"with ("),this.out(e.object,t,e.object.type),t.write(null,") "),this.out(e.body,t,e.body.type)},SwitchStatement:function(e,t){var n,r,i,s=c(t.indent,t.indentLevel++),o=t.lineEnd;t.indentLevel++;var a=s+t.indent,u=a+t.indent;t.write(e,"switch ("),this.out(e.discriminant,t,e.discriminant.type),t.write(null,") {",o);for(var l=e.cases,p=0;p<l.length;p++){(n=l[p]).test?(t.write(n,a,"case "),this.out(n.test,t,n.test.type),t.write(null,":",o)):t.write(n,a,"default:",o),r=n.consequent;for(var h=0;h<r.length;h++)i=r[h],t.write(null,u),this.out(i,t,i.type),t.write(null,o)}t.indentLevel-=2,t.write(null,s,"}")},ReturnStatement:function(e,t){e.async&&t.write(e," async "),t.write(e,"return"),e.argument&&(t.write(null," "),this.out(e.argument,t,e.argument.type)),t.write(null,";")},ThrowStatement:function(e,t){e.async&&t.write(e," async "),t.write(e,"throw "),this.out(e.argument,t,e.argument.type),t.write(null,";")},TryStatement:function(e,t){t.write(e,"try "),this.out(e.block,t,e.block.type),e.handler&&this.out(e.handler,t,e.handler.type),e.finalizer&&(t.write(e.finalizer," finally "),this.out(e.finalizer,t,e.finalizer.type))},CatchClause:function(e,t){t.write(e," catch ("),this.out(e.param,t,e.param.type),t.write(null,") "),this.out(e.body,t,e.body.type)},WhileStatement:function(e,t){t.write(e,"while ("),this.out(e.test,t,e.test.type),t.write(null,") "),"BlockStatement"!==e.body.type&&t.write(null,t.lineEnd,c(t.indent,t.indentLevel+1)),this.out(e.body,t,e.body.type)},DoWhileStatement:function(e,t){t.write(e,"do "),"BlockStatement"!==e.body.type&&t.write(null,t.lineEnd,c(t.indent,t.indentLevel+1)),this.out(e.body,t,e.body.type),t.write(null," while ("),this.out(e.test,t,e.test.type),t.write(null,");")},ForStatement:function(e,t){if(t.write(e,"for ("),null!=e.init){var n=e.init,r=n.type;t.inForInit++,this.out(n,t,r),t.inForInit--,"VariableDeclaration"!==r&&t.write(null,"; ")}else t.write(null,"; ");e.test&&this.out(e.test,t,e.test.type),t.write(null,"; "),e.update&&this.out(e.update,t,e.update.type),t.write(null,") "),"BlockStatement"!==e.body.type&&t.write(null,t.lineEnd,c(t.indent,t.indentLevel+1)),this.out(e.body,t,e.body.type)},ForInStatement:i=function(e,t){t.write(e,"for (");var n=e.left,r=n.type;t.inForInit++,this.out(n,t,r),"V"===r[0]&&19===r.length&&t.back(),t.inForInit--,t.write(null,"I"===e.type[3]?" in ":" of "),this.out(e.right,t,e.right.type),t.write(null,") "),"BlockStatement"!==e.body.type&&t.write(null,t.lineEnd,c(t.indent,t.indentLevel+1)),this.out(e.body,t,e.body.type)},ForOfStatement:i,DebuggerStatement:function(e,t){t.write(e,"debugger;")},Function:function(e,t){e.async&&t.write(e,"async "),t.write(e,e.generator?"function* ":"function "),e.id&&t.write(e.id,e.id.name),this.formatParameters(e.params,t),this.out(e.body,t,e.body.type)},FunctionDeclaration:function(e,t){this.Function(e,t),t.write(null,t.lineEnd,c(t.indent,t.indentLevel))},FunctionExpression:function(e,t){this.Function(e,t)},VariableDeclaration:function(e,t){var n=e.declarations;t.write(e,e.kind," ");var r=n.length;if(r>0){this.out(n[0],t,"VariableDeclarator");for(var i=1;i<r;i++)t.write(null,", "),this.out(n[i],t,"VariableDeclarator")}t.write(null,";")},VariableDeclarator:function(e,t){this.out(e.id,t,e.id.type),null!=e.init&&(t.write(null," = "),this.expr(t,f,e.init))},ClassDeclaration:function(e,t){t.write(e,"class "),e.id&&t.write(e.id,e.id.name+" "),e.superClass&&(t.write(null,"extends "),this.out(e.superClass,t,e.superClass.type),t.write(null," ")),this.out(e.body,t,"BlockStatement")},ImportSpecifier:function(e,t){e.local.name==e.imported.name?this.out(e.local,t,e.local.type):(this.out(e.imported,t,e.imported.type),t.write(null," as "),this.out(e.local,t,e.local.type))},ImportDefaultSpecifier:function(e,t){this.out(e.local,t,e.local.type)},ImportNamespaceSpecifier:function(e,t){t.write(null,"* as "),this.out(e.local,t,e.local.type)},ImportDeclaration:function(e,t){t.write(e,"import ");var n=e.specifiers,r=n.length,i=!0;if(r>0){for(var s=0;s<r;s++)"ImportSpecifier"===n[s].type&&i&&(i=!1,t.write(null,"{")),this.out(n[s],t,n[s].type),s<r-1&&t.write(null,", ");"ImportSpecifier"===n[r-1].type&&t.write(null,"}"),t.write(null," from ")}t.write(e.source,e.source.raw),t.write(null,";")},ExportDefaultDeclaration:function(e,t){t.write(e,"export default "),this.out(e.declaration,t,e.declaration.type)},ExportSpecifier:function(e,t){e.local.name==e.exported.name?this.out(e.local,t,e.local.type):(this.out(e.local,t,e.local.type),t.write(null," as "),this.out(e.exported,t,e.exported.type))},ExportNamedDeclaration:function(e,t){if(t.write(e,"export "),e.declaration)this.out(e.declaration,t,e.declaration.type);else{var n=e.specifiers;if(t.write(e,"{"),n&&n.length>0)for(var r=0;r<n.length;r++)this.out(n[r],t,n[r].type),r<n.length-1&&t.write(null,", ");t.write(null,"}"),e.source&&t.write(e.source," from ",e.source.raw),t.write(null,";")}},ExportAllDeclaration:function(e,t){t.write(e,"export * from "),t.write(e.source,e.source.raw,";")},MethodDefinition:function(e,t){switch(e.value.async&&t.write(e,"async "),e.static&&t.write(e,"static "),e.kind){case"get":case"set":t.write(e,e.kind," ")}e.value.generator&&t.write(null,"*"),e.computed?(t.write(null,"["),this.out(e.key,t,e.key.type),t.write(null,"]")):this.out(e.key,t,e.key.type),this.formatParameters(e.value.params,t),this.out(e.value.body,t,e.value.body.type)},ClassMethod:function(e,t){switch(e.async&&t.write(e,"async "),e.static&&t.write(e,"static "),e.kind){case"get":case"set":t.write(e,e.kind," ")}e.generator&&t.write(null,"*"),e.computed?(t.write(null,"["),this.out(e.key,t,e.key.type),t.write(null,"]")):this.out(e.key,t,e.key.type),this.formatParameters(e.params,t),this.out(e.body,t,e.body.type)},ClassExpression:function(e,t){this.out(e,t,"ClassDeclaration")},ArrowFunctionExpression:function(e,t){e.async&&t.write(e,"async "),1===e.params.length&&"Identifier"===e.params[0].type?(this.out(e.params[0],t,e.params[0].type),t.write(e," => ")):(this.formatParameters(e.params,t),t.write(e,"=> ")),"ObjectExpression"===e.body.type||"SequenceExpression"===e.body.type?(t.write(null,"("),this.out(e.body,t,e.body.type),t.write(null,")")):this.out(e.body,t,e.body.type)},ThisExpression:function(e,t){t.write(e,"this")},Super:function(e,t){t.write(e,"super")},RestElement:s=function(e,t){t.write(e,"..."),this.out(e.argument,t,e.argument.type)},SpreadElement:s,YieldExpression:function(e,t){t.write(e,e.delegate?"yield*":"yield"),e.argument&&(t.write(null," "),this.expr(t,e,e.argument))},AwaitExpression:function(e,t){t.write(e,"await "),this.expr(t,e,e.argument)},TemplateLiteral:function(e,t){var n,r=e.quasis,i=e.expressions;t.write(e,"`");for(var s=0,o=i.length;s<o;s++)n=i[s],t.write(r[s].value,r[s].value.raw),t.write(null,"${"),this.out(n,t,n.type),t.write(null,"}");t.write(r[r.length-1].value,r[r.length-1].value.raw),t.write(e,"`")},TaggedTemplateExpression:function(e,t){this.out(e.tag,t,e.tag.type),this.out(e.quasi,t,e.quasi.type)},ArrayExpression:a=function(e,t){if(t.write(e,"["),e.elements.length>0)for(var n=e.elements,r=n.length,i=0;;){var s=n[i];if(s&&this.expr(t,f,s),((i+=1)<r||null===s)&&t.write(null,","),i>=r)break;t.lineLength()>t.wrapColumn&&t.write(null,t.lineEnd,c(t.indent,t.indentLevel+1))}t.write(null,"]")},ArrayPattern:a,ObjectExpression:function(e,t){var n,r=c(t.indent,t.indentLevel++),i=t.lineEnd,s=r+t.indent;if(t.write(e,"{"),e.properties.length>0){t.write(null,i);for(var o=e.properties,a=o.length,u=0;n=o[u],t.write(null,s),this.out(n,t,"Property"),++u<a;)t.write(e,",",i),t.lineLength()>t.wrapColumn&&t.write(null,t.lineEnd,c(t.indent,t.indentLevel+1));t.write(null,i,r,"}")}else t.write(null,"}");t.indentLevel--},Property:function(e,t){e.method||"get"===e.kind||"set"===e.kind?this.MethodDefinition(e,t):(e.shorthand||(e.computed?(t.write(null,"["),this.out(e.key,t,e.key.type),t.write(null,"]")):this.out(e.key,t,e.key.type),t.write(null,": ")),this.expr(t,f,e.value))},ObjectPattern:function(e,t){if(t.write(e,"{"),e.properties.length>0)for(var n=e.properties,r=n.length,i=0;this.out(n[i],t,"Property"),++i<r;)t.write(null,", ");t.write(null,"}")},SequenceExpression:function(e,t){var n,r=e.expressions;if(r.length>0)for(var i=r.length,s=0;s<i;s++)n=r[s],s&&t.write(null,", "),this.expr(t,f,n)},UnaryExpression:function(e,t){e.prefix?(t.write(e,e.operator),e.operator.length>1&&t.write(e," "),this.expr(t,e,e.argument,!0)):(this.expr(t,e,e.argument),t.write(e,e.operator))},UpdateExpression:function(e,t){e.prefix?(t.write(e,e.operator),this.out(e.argument,t,e.argument.type)):(this.out(e.argument,t,e.argument.type),t.write(e,e.operator))},BinaryExpression:o=function(e,t){var n=e.operator;"in"===n&&t.inForInit&&t.write(null,"("),this.expr(t,e,e.left),t.write(e," ",n," "),this.expr(t,e,e.right,"ArrowFunctionExpression"===e.right.type?2:0),"in"===n&&t.inForInit&&t.write(null,")")},LogicalExpression:o,AssignmentExpression:function(e,t){"ObjectPattern"===e.left.type&&t.write(null,"("),this.BinaryExpression(e,t),"ObjectPattern"===e.left.type&&t.write(null,")")},AssignmentPattern:function(e,t){this.expr(t,e,e.left),t.write(e," = "),this.expr(t,e,e.right)},ConditionalExpression:function(e,t){this.expr(t,e,e.test,!0),t.write(e," ? "),this.expr(t,e,e.consequent),t.write(null," : "),this.expr(t,e,e.alternate)},NewExpression:function(e,t){t.write(e,"new "),this.expr(t,e,e.callee,"CallExpression"===e.callee.type||"ObjectExpression"===e.callee.type?2:0),this.argumentList(e,t)},CallExpression:function(e,t){this.expr(t,e,e.callee,"ObjectExpression"===e.callee.type?2:0),this.argumentList(e,t)},MemberExpression:function(e,t){!("ObjectExpression"===e.object.type||e.object.type.match(/Literal$/)&&e.object.raw&&e.object.raw.match(/^[0-9]/))&&("ArrayExpression"===e.object.type||"CallExpression"===e.object.type||"NewExpression"===e.object.type||r(e)<=r(e.object))?this.out(e.object,t,e.object.type):(t.write(null,"("),this.out(e.object,t,e.object.type),t.write(null,")")),e.computed?(t.write(e,"["),this.out(e.property,t,e.property.type),t.write(null,"]")):(t.write(e,"."),this.out(e.property,t,e.property.type))},Identifier:function(e,t){t.write(e,e.name)},Literal:function(e,t){t.write(e,e.raw)},NullLiteral:function(e,t){t.write(e,"null")},BooleanLiteral:function(e,t){t.write(e,JSON.stringify(e.value))},StringLiteral:function(e,t){t.write(e,JSON.stringify(e.value))},RegExpLiteral:function(e,t){t.write(e,e.extra.raw||"/"+e.pattern+"/"+e.flags)},NumericLiteral:function(e,t){t.write(e,JSON.stringify(e.value))}};t.exports=function(e,t,n){var r="",i=[],s=(t=t||{}).map&&new l(t.map);s&&t.map.sourceContent&&s.setSourceContent(t.map.file,t.map.sourceContent);var o="",a=[],u=[],p={inForInit:0,lineLength:function(){return r.length},sourceAt:function(e,t){return n?n.substring(e,t):"/* Omitted Non-standard node */"},write:function(e){o=arguments[arguments.length-1];for(var n=1;n<arguments.length;n++)if(s&&e&&e.loc&&e.loc.start&&s.addMapping({source:t.map.file,original:{line:e.loc.start.line,column:e.loc.start.column},generated:{line:t.map.startLine+i.length+1,column:r.length}}),arguments[n]===p.lineEnd){if(u.length&&(u.forEach(function(e){"Line"===e.type?r+=" // "+e.value:((" /*"+e.value+"*/").split("\n").forEach(function(e){r+=e,i.push(r),r=""}),r=i.pop())}),u=[]),i.push(r),r="",a.length){var l=i.pop();a.forEach(function(e){var t=c(p.indent,e.indent);"Line"===e.type?i.push(t+"//"+e.value):(t+"/*"+e.value+"*/").split("\n").forEach(function(e){i.push(e)})}),i.push(l),a=[]}}else r+=arguments[n],e&&e.$comments&&(e.$comments.forEach(function(t){var n=e.loc.start.column<t.loc.start.column;t.indent=p.indentLevel,n?u.push(t):a.push(t)}),e.$comments=null)},back:function(){r=r.substring(0,r.length-o.length)},indent:"    ",lineEnd:"\n",indentLevel:0,wrapColumn:80};d.out(e,p),u=e.$comments||[],p.write(e,p.lineEnd);var h=i.join(p.lineEnd);return t&&t.map?{code:h,map:s}:h}},{"source-map":24}],13:[function(e,t,n){"use strict";function r(e,t){["start","end","loc","range"].forEach(function(n){n in e&&!(n in t)&&(t[n]=e[n])})}function i(e,t,n){function r(e){e.replace=u.replace,e.append=u.append,e.index?(Object.defineProperties(e,{index:{enumerable:!0,get:u.index}}),e.remove=u.removeElement):e.remove=u.removeNode,n.unshift(e),i(e.self,t,n),n.shift()}return n||((n=[{self:e}]).replace=function(e,t){n[e].replace(t)}),t(e,function(){e.type in a&&a[e.type](e,n,function t(i,s,o){if(i===e)return a[o||e.type](e,n,t);for(var u=Object.keys(e),c=0;c<u.length;c++){var l=e[u[c]];Array.isArray(l)?l.indexOf(i)>=0&&r({self:i,parent:e,field:u[c],index:!0}):l instanceof Object&&i===l&&r({self:i,parent:e,field:u[c]})}})},n),e}function s(t,n){var r=[],s={ecmaVersion:8,allowHashBang:!0,allowReturnOutsideFunction:!0,allowImportExportEverywhere:!0,locations:!0,onComment:r};if((!n||!n.noNodentExtensions||parseInt(o.version)<4)&&(c||(parseInt(o.version)<4&&console.warn("Nodent: Warning - noNodentExtensions option requires acorn >=v4.x. Extensions installed."),e("acorn-es7-plugin")(o),c=!0),s.plugins=s.plugins||{},s.plugins.asyncawait={asyncExits:!0,awaitAnywhere:!0}),n)for(var a in n)"noNodentExtensions"!==a&&(s[a]=n[a]);var u=o.parse(t,s);return i(u,function(e,t,n){for(t();r.length&&e.loc&&e.loc.start.line>=r[0].loc.start.line&&e.loc.end.line>=r[0].loc.end.line;)e.$comments=e.$comments||[],e.$comments.push(r.shift())}),u}var o=e("acorn"),a=e("acorn/dist/walk").make({AwaitExpression:function(e,t,n){n(e.argument,t,"Expression")},SwitchStatement:function(e,t,n){n(e.discriminant,t,"Expression");for(var r=0;r<e.cases.length;++r)n(e.cases[r],t)},SwitchCase:function(e,t,n){e.test&&n(e.test,t,"Expression");for(var r=0;r<e.consequent.length;++r)n(e.consequent[r],t,"Statement")},TryStatement:function(e,t,n){n(e.block,t,"Statement"),e.handler&&n(e.handler,t,"Statement"),e.finalizer&&n(e.finalizer,t,"Statement")},CatchClause:function(e,t,n){n(e.param,t,"Pattern"),n(e.body,t,"ScopeBody")},Class:function(e,t,n){e.id&&n(e.id,t,"Pattern"),e.superClass&&n(e.superClass,t,"Expression"),n(e.body,t)},ClassBody:function(e,t,n){for(var r=0;r<e.body.length;r++)n(e.body[r],t)},ObjectPattern:function(e,t,n){for(var r=0,i=e.properties;r<i.length;r+=1)n(i[r],t,"Property")},ClassProperty:function(e,t,n){e.key&&n(e.key,t,"Expression"),e.value&&n(e.value,t,"Expression")},ClassMethod:function(e,t,n){e.key&&n(e.key,t,"Expression"),n(e,t,"Function")},ObjectProperty:function(e,t,n){e.key&&n(e.key,t,"Expression"),e.value&&n(e.value,t,"Expression")},ObjectMethod:function(e,t,n){e.key&&n(e.key,t,"Expression"),n(e,t,"Function")}}),u={replace:function(e){return Array.isArray(e)&&1===e.length&&(e=e[0]),"index"in this?(r(this.parent[this.field][this.index],e),Array.isArray(e)?[].splice.apply(this.parent[this.field],[this.index,1].concat(e)):this.parent[this.field][this.index]=e):(r(this.parent[this.field],e),Array.isArray(e)?this.parent[this.field]={type:"BlockStatement",body:e}:this.parent[this.field]=e),this.self},append:function(e){if(Array.isArray(e)&&1===e.length&&(e=e[0]),!("index"in this))throw new Error("Cannot append Element node to non-array");return Array.isArray(e)?[].splice.apply(this.parent[this.field],[this.index+1,0].concat(e)):this.parent[this.field].splice(this.index+1,0,e),this.self},index:function(){return this.parent[this.field].indexOf(this.self)},removeElement:function(){return this.parent[this.field].splice(this.index,1)[0]},removeNode:function(){var e=this.parent[this.field];return delete this.parent[this.field],e}},c=!1,l={};t.exports={part:function(e,t){function n(e,r){if(Array.isArray(r)&&!Array.isArray(e))throw new Error("Can't substitute an array for a node");return r=r||{},Object.keys(e).forEach(function(i){if(!(e[i]instanceof Object))return r[i]=e[i];if(Array.isArray(e[i]))return r[i]=n(e[i],[]);var s;if(s=Array.isArray(r)?function(e){return"function"==typeof e&&(e=e()),r=r.concat(e)}:function(e){return"function"==typeof e&&(e=e()),r[i]=e,r},"Identifier"===e[i].type&&"$"===e[i].name[0])return s(t[e[i].name.slice(1)]);if("LabeledStatement"===e[i].type&&"$"===e[i].label.name){var o=e[i].body.expression;return s(t[o.name||o.value])}return"LabeledStatement"===e[i].type&&"$$"===e[i].label.name.slice(0,2)?s(t[e[i].label.name.slice(2)](n(e[i]).body)):s(n(e[i]))}),r}l[e]||(l[e]=s(e,{noNodentExtensions:!0,locations:!1,ranges:!1,onComment:null}));var r=n(l[e]);return{body:r.body,expr:"ExpressionStatement"===r.body[0].type?r.body[0].expression:null}},parse:s,treeWalker:i}},{acorn:4,"acorn-es7-plugin":1,"acorn/dist/walk":5}],14:[function(e,t,n){function r(){this._array=[],this._set=o?new Map:Object.create(null)}var i=e("./util"),s=Object.prototype.hasOwnProperty,o="undefined"!=typeof Map;r.fromArray=function(e,t){for(var n=new r,i=0,s=e.length;i<s;i++)n.add(e[i],t);return n},r.prototype.size=function(){return o?this._set.size:Object.getOwnPropertyNames(this._set).length},r.prototype.add=function(e,t){var n=o?e:i.toSetString(e),r=o?this.has(e):s.call(this._set,n),a=this._array.length;r&&!t||this._array.push(e),r||(o?this._set.set(e,a):this._set[n]=a)},r.prototype.has=function(e){if(o)return this._set.has(e);var t=i.toSetString(e);return s.call(this._set,t)},r.prototype.indexOf=function(e){if(o){var t=this._set.get(e);if(t>=0)return t}else{var n=i.toSetString(e);if(s.call(this._set,n))return this._set[n]}throw new Error('"'+e+'" is not in the set.')},r.prototype.at=function(e){if(e>=0&&e<this._array.length)return this._array[e];throw new Error("No element indexed by "+e)},r.prototype.toArray=function(){return this._array.slice()},n.ArraySet=r},{"./util":23}],15:[function(e,t,n){var r=e("./base64");n.encode=function(e){var t,n="",i=function(e){return e<0?1+(-e<<1):0+(e<<1)}(e);do{t=31&i,(i>>>=5)>0&&(t|=32),n+=r.encode(t)}while(i>0);return n},n.decode=function(e,t,n){var i,s,o=e.length,a=0,u=0;do{if(t>=o)throw new Error("Expected more digits in base 64 VLQ value.");if(-1===(s=r.decode(e.charCodeAt(t++))))throw new Error("Invalid base64 digit: "+e.charAt(t-1));i=!!(32&s),a+=(s&=31)<<u,u+=5}while(i);n.value=function(e){var t=e>>1;return 1==(1&e)?-t:t}(a),n.rest=t}},{"./base64":16}],16:[function(e,t,n){var r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split("");n.encode=function(e){if(0<=e&&e<r.length)return r[e];throw new TypeError("Must be between 0 and 63: "+e)},n.decode=function(e){return 65<=e&&e<=90?e-65:97<=e&&e<=122?e-97+26:48<=e&&e<=57?e-48+52:43==e?62:47==e?63:-1}},{}],17:[function(e,t,n){function r(e,t,i,s,o,a){var u=Math.floor((t-e)/2)+e,c=o(i,s[u],!0);return 0===c?u:c>0?t-u>1?r(u,t,i,s,o,a):a==n.LEAST_UPPER_BOUND?t<s.length?t:-1:u:u-e>1?r(e,u,i,s,o,a):a==n.LEAST_UPPER_BOUND?u:e<0?-1:e}n.GREATEST_LOWER_BOUND=1,n.LEAST_UPPER_BOUND=2,n.search=function(e,t,i,s){if(0===t.length)return-1;var o=r(-1,t.length,e,t,i,s||n.GREATEST_LOWER_BOUND);if(o<0)return-1;for(;o-1>=0&&0===i(t[o],t[o-1],!0);)--o;return o}},{}],18:[function(e,t,n){function r(){this._array=[],this._sorted=!0,this._last={generatedLine:-1,generatedColumn:0}}var i=e("./util");r.prototype.unsortedForEach=function(e,t){this._array.forEach(e,t)},r.prototype.add=function(e){!function(e,t){var n=e.generatedLine,r=t.generatedLine,s=e.generatedColumn,o=t.generatedColumn;return r>n||r==n&&o>=s||i.compareByGeneratedPositionsInflated(e,t)<=0}(this._last,e)?(this._sorted=!1,this._array.push(e)):(this._last=e,this._array.push(e))},r.prototype.toArray=function(){return this._sorted||(this._array.sort(i.compareByGeneratedPositionsInflated),this._sorted=!0),this._array},n.MappingList=r},{"./util":23}],19:[function(e,t,n){function r(e,t,n){var r=e[t];e[t]=e[n],e[n]=r}function i(e,t,n,s){if(n<s){var o=n-1;r(e,function(e,t){return Math.round(e+Math.random()*(t-e))}(n,s),s);for(var a=e[s],u=n;u<s;u++)t(e[u],a)<=0&&r(e,o+=1,u);r(e,o+1,u);var c=o+1;i(e,t,n,c-1),i(e,t,c+1,s)}}n.quickSort=function(e,t){i(e,t,0,e.length-1)}},{}],20:[function(e,t,n){function r(e){var t=e;return"string"==typeof e&&(t=JSON.parse(e.replace(/^\)\]\}'/,""))),null!=t.sections?new o(t):new i(t)}function i(e){var t=e;"string"==typeof e&&(t=JSON.parse(e.replace(/^\)\]\}'/,"")));var n=a.getArg(t,"version"),r=a.getArg(t,"sources"),i=a.getArg(t,"names",[]),s=a.getArg(t,"sourceRoot",null),o=a.getArg(t,"sourcesContent",null),u=a.getArg(t,"mappings"),l=a.getArg(t,"file",null);if(n!=this._version)throw new Error("Unsupported version: "+n);r=r.map(String).map(a.normalize).map(function(e){return s&&a.isAbsolute(s)&&a.isAbsolute(e)?a.relative(s,e):e}),this._names=c.fromArray(i.map(String),!0),this._sources=c.fromArray(r,!0),this.sourceRoot=s,this.sourcesContent=o,this._mappings=u,this.file=l}function s(){this.generatedLine=0,this.generatedColumn=0,this.source=null,this.originalLine=null,this.originalColumn=null,this.name=null}function o(e){var t=e;"string"==typeof e&&(t=JSON.parse(e.replace(/^\)\]\}'/,"")));var n=a.getArg(t,"version"),i=a.getArg(t,"sections");if(n!=this._version)throw new Error("Unsupported version: "+n);this._sources=new c,this._names=new c;var s={line:-1,column:0};this._sections=i.map(function(e){if(e.url)throw new Error("Support for url field in sections not implemented.");var t=a.getArg(e,"offset"),n=a.getArg(t,"line"),i=a.getArg(t,"column");if(n<s.line||n===s.line&&i<s.column)throw new Error("Section offsets must be ordered and non-overlapping.");return s=t,{generatedOffset:{generatedLine:n+1,generatedColumn:i+1},consumer:new r(a.getArg(e,"map"))}})}var a=e("./util"),u=e("./binary-search"),c=e("./array-set").ArraySet,l=e("./base64-vlq"),p=e("./quick-sort").quickSort;r.fromSourceMap=function(e){return i.fromSourceMap(e)},r.prototype._version=3,r.prototype.__generatedMappings=null,Object.defineProperty(r.prototype,"_generatedMappings",{get:function(){return this.__generatedMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__generatedMappings}}),r.prototype.__originalMappings=null,Object.defineProperty(r.prototype,"_originalMappings",{get:function(){return this.__originalMappings||this._parseMappings(this._mappings,this.sourceRoot),this.__originalMappings}}),r.prototype._charIsMappingSeparator=function(e,t){var n=e.charAt(t);return";"===n||","===n},r.prototype._parseMappings=function(e,t){throw new Error("Subclasses must implement _parseMappings")},r.GENERATED_ORDER=1,r.ORIGINAL_ORDER=2,r.GREATEST_LOWER_BOUND=1,r.LEAST_UPPER_BOUND=2,r.prototype.eachMapping=function(e,t,n){var i,s=t||null;switch(n||r.GENERATED_ORDER){case r.GENERATED_ORDER:i=this._generatedMappings;break;case r.ORIGINAL_ORDER:i=this._originalMappings;break;default:throw new Error("Unknown order of iteration.")}var o=this.sourceRoot;i.map(function(e){var t=null===e.source?null:this._sources.at(e.source);return null!=t&&null!=o&&(t=a.join(o,t)),{source:t,generatedLine:e.generatedLine,generatedColumn:e.generatedColumn,originalLine:e.originalLine,originalColumn:e.originalColumn,name:null===e.name?null:this._names.at(e.name)}},this).forEach(e,s)},r.prototype.allGeneratedPositionsFor=function(e){var t=a.getArg(e,"line"),n={source:a.getArg(e,"source"),originalLine:t,originalColumn:a.getArg(e,"column",0)};if(null!=this.sourceRoot&&(n.source=a.relative(this.sourceRoot,n.source)),!this._sources.has(n.source))return[];n.source=this._sources.indexOf(n.source);var r=[],i=this._findMapping(n,this._originalMappings,"originalLine","originalColumn",a.compareByOriginalPositions,u.LEAST_UPPER_BOUND);if(i>=0){var s=this._originalMappings[i];if(void 0===e.column)for(var o=s.originalLine;s&&s.originalLine===o;)r.push({line:a.getArg(s,"generatedLine",null),column:a.getArg(s,"generatedColumn",null),lastColumn:a.getArg(s,"lastGeneratedColumn",null)}),s=this._originalMappings[++i];else for(var c=s.originalColumn;s&&s.originalLine===t&&s.originalColumn==c;)r.push({line:a.getArg(s,"generatedLine",null),column:a.getArg(s,"generatedColumn",null),lastColumn:a.getArg(s,"lastGeneratedColumn",null)}),s=this._originalMappings[++i]}return r},n.SourceMapConsumer=r,(i.prototype=Object.create(r.prototype)).consumer=r,i.fromSourceMap=function(e){var t=Object.create(i.prototype),n=t._names=c.fromArray(e._names.toArray(),!0),r=t._sources=c.fromArray(e._sources.toArray(),!0);t.sourceRoot=e._sourceRoot,t.sourcesContent=e._generateSourcesContent(t._sources.toArray(),t.sourceRoot),t.file=e._file;for(var o=e._mappings.toArray().slice(),u=t.__generatedMappings=[],l=t.__originalMappings=[],h=0,f=o.length;h<f;h++){var d=o[h],y=new s;y.generatedLine=d.generatedLine,y.generatedColumn=d.generatedColumn,d.source&&(y.source=r.indexOf(d.source),y.originalLine=d.originalLine,y.originalColumn=d.originalColumn,d.name&&(y.name=n.indexOf(d.name)),l.push(y)),u.push(y)}return p(t.__originalMappings,a.compareByOriginalPositions),t},i.prototype._version=3,Object.defineProperty(i.prototype,"sources",{get:function(){return this._sources.toArray().map(function(e){return null!=this.sourceRoot?a.join(this.sourceRoot,e):e},this)}}),i.prototype._parseMappings=function(e,t){for(var n,r,i,o,u,c=1,h=0,f=0,d=0,y=0,m=0,g=e.length,v=0,b={},x={},w=[],E=[];v<g;)if(";"===e.charAt(v))c++,v++,h=0;else if(","===e.charAt(v))v++;else{for((n=new s).generatedLine=c,o=v;o<g&&!this._charIsMappingSeparator(e,o);o++);if(r=e.slice(v,o),i=b[r])v+=r.length;else{for(i=[];v<o;)l.decode(e,v,x),u=x.value,v=x.rest,i.push(u);if(2===i.length)throw new Error("Found a source, but no line and column");if(3===i.length)throw new Error("Found a source and line, but no column");b[r]=i}n.generatedColumn=h+i[0],h=n.generatedColumn,i.length>1&&(n.source=y+i[1],y+=i[1],n.originalLine=f+i[2],f=n.originalLine,n.originalLine+=1,n.originalColumn=d+i[3],d=n.originalColumn,i.length>4&&(n.name=m+i[4],m+=i[4])),E.push(n),"number"==typeof n.originalLine&&w.push(n)}p(E,a.compareByGeneratedPositionsDeflated),this.__generatedMappings=E,p(w,a.compareByOriginalPositions),this.__originalMappings=w},i.prototype._findMapping=function(e,t,n,r,i,s){if(e[n]<=0)throw new TypeError("Line must be greater than or equal to 1, got "+e[n]);if(e[r]<0)throw new TypeError("Column must be greater than or equal to 0, got "+e[r]);return u.search(e,t,i,s)},i.prototype.computeColumnSpans=function(){for(var e=0;e<this._generatedMappings.length;++e){var t=this._generatedMappings[e];if(e+1<this._generatedMappings.length){var n=this._generatedMappings[e+1];if(t.generatedLine===n.generatedLine){t.lastGeneratedColumn=n.generatedColumn-1;continue}}t.lastGeneratedColumn=1/0}},i.prototype.originalPositionFor=function(e){var t={generatedLine:a.getArg(e,"line"),generatedColumn:a.getArg(e,"column")},n=this._findMapping(t,this._generatedMappings,"generatedLine","generatedColumn",a.compareByGeneratedPositionsDeflated,a.getArg(e,"bias",r.GREATEST_LOWER_BOUND));if(n>=0){var i=this._generatedMappings[n];if(i.generatedLine===t.generatedLine){var s=a.getArg(i,"source",null);null!==s&&(s=this._sources.at(s),null!=this.sourceRoot&&(s=a.join(this.sourceRoot,s)));var o=a.getArg(i,"name",null);return null!==o&&(o=this._names.at(o)),{source:s,line:a.getArg(i,"originalLine",null),column:a.getArg(i,"originalColumn",null),name:o}}}return{source:null,line:null,column:null,name:null}},i.prototype.hasContentsOfAllSources=function(){return!!this.sourcesContent&&(this.sourcesContent.length>=this._sources.size()&&!this.sourcesContent.some(function(e){return null==e}))},i.prototype.sourceContentFor=function(e,t){if(!this.sourcesContent)return null;if(null!=this.sourceRoot&&(e=a.relative(this.sourceRoot,e)),this._sources.has(e))return this.sourcesContent[this._sources.indexOf(e)];var n;if(null!=this.sourceRoot&&(n=a.urlParse(this.sourceRoot))){var r=e.replace(/^file:\/\//,"");if("file"==n.scheme&&this._sources.has(r))return this.sourcesContent[this._sources.indexOf(r)];if((!n.path||"/"==n.path)&&this._sources.has("/"+e))return this.sourcesContent[this._sources.indexOf("/"+e)]}if(t)return null;throw new Error('"'+e+'" is not in the SourceMap.')},i.prototype.generatedPositionFor=function(e){var t=a.getArg(e,"source");if(null!=this.sourceRoot&&(t=a.relative(this.sourceRoot,t)),!this._sources.has(t))return{line:null,column:null,lastColumn:null};var n={source:t=this._sources.indexOf(t),originalLine:a.getArg(e,"line"),originalColumn:a.getArg(e,"column")},i=this._findMapping(n,this._originalMappings,"originalLine","originalColumn",a.compareByOriginalPositions,a.getArg(e,"bias",r.GREATEST_LOWER_BOUND));if(i>=0){var s=this._originalMappings[i];if(s.source===n.source)return{line:a.getArg(s,"generatedLine",null),column:a.getArg(s,"generatedColumn",null),lastColumn:a.getArg(s,"lastGeneratedColumn",null)}}return{line:null,column:null,lastColumn:null}},n.BasicSourceMapConsumer=i,(o.prototype=Object.create(r.prototype)).constructor=r,o.prototype._version=3,Object.defineProperty(o.prototype,"sources",{get:function(){for(var e=[],t=0;t<this._sections.length;t++)for(var n=0;n<this._sections[t].consumer.sources.length;n++)e.push(this._sections[t].consumer.sources[n]);return e}}),o.prototype.originalPositionFor=function(e){var t={generatedLine:a.getArg(e,"line"),generatedColumn:a.getArg(e,"column")},n=u.search(t,this._sections,function(e,t){var n=e.generatedLine-t.generatedOffset.generatedLine;return n||e.generatedColumn-t.generatedOffset.generatedColumn}),r=this._sections[n];return r?r.consumer.originalPositionFor({line:t.generatedLine-(r.generatedOffset.generatedLine-1),column:t.generatedColumn-(r.generatedOffset.generatedLine===t.generatedLine?r.generatedOffset.generatedColumn-1:0),bias:e.bias}):{source:null,line:null,column:null,name:null}},o.prototype.hasContentsOfAllSources=function(){return this._sections.every(function(e){return e.consumer.hasContentsOfAllSources()})},o.prototype.sourceContentFor=function(e,t){for(var n=0;n<this._sections.length;n++){var r=this._sections[n].consumer.sourceContentFor(e,!0);if(r)return r}if(t)return null;throw new Error('"'+e+'" is not in the SourceMap.')},o.prototype.generatedPositionFor=function(e){for(var t=0;t<this._sections.length;t++){var n=this._sections[t];if(-1!==n.consumer.sources.indexOf(a.getArg(e,"source"))){var r=n.consumer.generatedPositionFor(e);if(r){return{line:r.line+(n.generatedOffset.generatedLine-1),column:r.column+(n.generatedOffset.generatedLine===r.line?n.generatedOffset.generatedColumn-1:0)}}}}return{line:null,column:null}},o.prototype._parseMappings=function(e,t){this.__generatedMappings=[],this.__originalMappings=[];for(var n=0;n<this._sections.length;n++)for(var r=this._sections[n],i=r.consumer._generatedMappings,s=0;s<i.length;s++){var o=i[s],u=r.consumer._sources.at(o.source);null!==r.consumer.sourceRoot&&(u=a.join(r.consumer.sourceRoot,u)),this._sources.add(u),u=this._sources.indexOf(u);var c=r.consumer._names.at(o.name);this._names.add(c),c=this._names.indexOf(c);var l={source:u,generatedLine:o.generatedLine+(r.generatedOffset.generatedLine-1),generatedColumn:o.generatedColumn+(r.generatedOffset.generatedLine===o.generatedLine?r.generatedOffset.generatedColumn-1:0),originalLine:o.originalLine,originalColumn:o.originalColumn,name:c};this.__generatedMappings.push(l),"number"==typeof l.originalLine&&this.__originalMappings.push(l)}p(this.__generatedMappings,a.compareByGeneratedPositionsDeflated),p(this.__originalMappings,a.compareByOriginalPositions)},n.IndexedSourceMapConsumer=o},{"./array-set":14,"./base64-vlq":15,"./binary-search":17,"./quick-sort":19,"./util":23}],21:[function(e,t,n){function r(e){e||(e={}),this._file=s.getArg(e,"file",null),this._sourceRoot=s.getArg(e,"sourceRoot",null),this._skipValidation=s.getArg(e,"skipValidation",!1),this._sources=new o,this._names=new o,this._mappings=new a,this._sourcesContents=null}var i=e("./base64-vlq"),s=e("./util"),o=e("./array-set").ArraySet,a=e("./mapping-list").MappingList;r.prototype._version=3,r.fromSourceMap=function(e){var t=e.sourceRoot,n=new r({file:e.file,sourceRoot:t});return e.eachMapping(function(e){var r={generated:{line:e.generatedLine,column:e.generatedColumn}};null!=e.source&&(r.source=e.source,null!=t&&(r.source=s.relative(t,r.source)),r.original={line:e.originalLine,column:e.originalColumn},null!=e.name&&(r.name=e.name)),n.addMapping(r)}),e.sources.forEach(function(t){var r=e.sourceContentFor(t);null!=r&&n.setSourceContent(t,r)}),n},r.prototype.addMapping=function(e){var t=s.getArg(e,"generated"),n=s.getArg(e,"original",null),r=s.getArg(e,"source",null),i=s.getArg(e,"name",null);this._skipValidation||this._validateMapping(t,n,r,i),null!=r&&(r=String(r),this._sources.has(r)||this._sources.add(r)),null!=i&&(i=String(i),this._names.has(i)||this._names.add(i)),this._mappings.add({generatedLine:t.line,generatedColumn:t.column,originalLine:null!=n&&n.line,originalColumn:null!=n&&n.column,source:r,name:i})},r.prototype.setSourceContent=function(e,t){var n=e;null!=this._sourceRoot&&(n=s.relative(this._sourceRoot,n)),null!=t?(this._sourcesContents||(this._sourcesContents=Object.create(null)),this._sourcesContents[s.toSetString(n)]=t):this._sourcesContents&&(delete this._sourcesContents[s.toSetString(n)],0===Object.keys(this._sourcesContents).length&&(this._sourcesContents=null))},r.prototype.applySourceMap=function(e,t,n){var r=t;if(null==t){if(null==e.file)throw new Error('SourceMapGenerator.prototype.applySourceMap requires either an explicit source file, or the source map\'s "file" property. Both were omitted.');r=e.file}var i=this._sourceRoot;null!=i&&(r=s.relative(i,r));var a=new o,u=new o;this._mappings.unsortedForEach(function(t){if(t.source===r&&null!=t.originalLine){var o=e.originalPositionFor({line:t.originalLine,column:t.originalColumn});null!=o.source&&(t.source=o.source,null!=n&&(t.source=s.join(n,t.source)),null!=i&&(t.source=s.relative(i,t.source)),t.originalLine=o.line,t.originalColumn=o.column,null!=o.name&&(t.name=o.name))}var c=t.source;null==c||a.has(c)||a.add(c);var l=t.name;null==l||u.has(l)||u.add(l)},this),this._sources=a,this._names=u,e.sources.forEach(function(t){var r=e.sourceContentFor(t);null!=r&&(null!=n&&(t=s.join(n,t)),null!=i&&(t=s.relative(i,t)),this.setSourceContent(t,r))},this)},r.prototype._validateMapping=function(e,t,n,r){if(t&&"number"!=typeof t.line&&"number"!=typeof t.column)throw new Error("original.line and original.column are not numbers -- you probably meant to omit the original mapping entirely and only map the generated position. If so, pass null for the original mapping instead of an object with empty or null values.");if((!(e&&"line"in e&&"column"in e&&e.line>0&&e.column>=0)||t||n||r)&&!(e&&"line"in e&&"column"in e&&t&&"line"in t&&"column"in t&&e.line>0&&e.column>=0&&t.line>0&&t.column>=0&&n))throw new Error("Invalid mapping: "+JSON.stringify({generated:e,source:n,original:t,name:r}))},r.prototype._serializeMappings=function(){for(var e,t,n,r,o=0,a=1,u=0,c=0,l=0,p=0,h="",f=this._mappings.toArray(),d=0,y=f.length;d<y;d++){if(t=f[d],e="",t.generatedLine!==a)for(o=0;t.generatedLine!==a;)e+=";",a++;else if(d>0){if(!s.compareByGeneratedPositionsInflated(t,f[d-1]))continue;e+=","}e+=i.encode(t.generatedColumn-o),o=t.generatedColumn,null!=t.source&&(r=this._sources.indexOf(t.source),e+=i.encode(r-p),p=r,e+=i.encode(t.originalLine-1-c),c=t.originalLine-1,e+=i.encode(t.originalColumn-u),u=t.originalColumn,null!=t.name&&(n=this._names.indexOf(t.name),e+=i.encode(n-l),l=n)),h+=e}return h},r.prototype._generateSourcesContent=function(e,t){return e.map(function(e){if(!this._sourcesContents)return null;null!=t&&(e=s.relative(t,e));var n=s.toSetString(e);return Object.prototype.hasOwnProperty.call(this._sourcesContents,n)?this._sourcesContents[n]:null},this)},r.prototype.toJSON=function(){var e={version:this._version,sources:this._sources.toArray(),names:this._names.toArray(),mappings:this._serializeMappings()};return null!=this._file&&(e.file=this._file),null!=this._sourceRoot&&(e.sourceRoot=this._sourceRoot),this._sourcesContents&&(e.sourcesContent=this._generateSourcesContent(e.sources,e.sourceRoot)),e},r.prototype.toString=function(){return JSON.stringify(this.toJSON())},n.SourceMapGenerator=r},{"./array-set":14,"./base64-vlq":15,"./mapping-list":18,"./util":23}],22:[function(e,t,n){function r(e,t,n,r,i){this.children=[],this.sourceContents={},this.line=null==e?null:e,this.column=null==t?null:t,this.source=null==n?null:n,this.name=null==i?null:i,this[a]=!0,null!=r&&this.add(r)}var i=e("./source-map-generator").SourceMapGenerator,s=e("./util"),o=/(\r?\n)/,a="$$$isSourceNode$$$";r.fromStringWithSourceMap=function(e,t,n){function i(e,t){if(null===e||void 0===e.source)a.add(t);else{var i=n?s.join(n,e.source):e.source;a.add(new r(e.originalLine,e.originalColumn,i,t,e.name))}}var a=new r,u=e.split(o),c=0,l=function(){function e(){return c<u.length?u[c++]:void 0}return e()+(e()||"")},p=1,h=0,f=null;return t.eachMapping(function(e){if(null!==f){if(!(p<e.generatedLine)){var t=(n=u[c]).substr(0,e.generatedColumn-h);return u[c]=n.substr(e.generatedColumn-h),h=e.generatedColumn,i(f,t),void(f=e)}i(f,l()),p++,h=0}for(;p<e.generatedLine;)a.add(l()),p++;if(h<e.generatedColumn){var n=u[c];a.add(n.substr(0,e.generatedColumn)),u[c]=n.substr(e.generatedColumn),h=e.generatedColumn}f=e},this),c<u.length&&(f&&i(f,l()),a.add(u.splice(c).join(""))),t.sources.forEach(function(e){var r=t.sourceContentFor(e);null!=r&&(null!=n&&(e=s.join(n,e)),a.setSourceContent(e,r))}),a},r.prototype.add=function(e){if(Array.isArray(e))e.forEach(function(e){this.add(e)},this);else{if(!e[a]&&"string"!=typeof e)throw new TypeError("Expected a SourceNode, string, or an array of SourceNodes and strings. Got "+e);e&&this.children.push(e)}return this},r.prototype.prepend=function(e){if(Array.isArray(e))for(var t=e.length-1;t>=0;t--)this.prepend(e[t]);else{if(!e[a]&&"string"!=typeof e)throw new TypeError("Expected a SourceNode, string, or an array of SourceNodes and strings. Got "+e);this.children.unshift(e)}return this},r.prototype.walk=function(e){for(var t,n=0,r=this.children.length;n<r;n++)(t=this.children[n])[a]?t.walk(e):""!==t&&e(t,{source:this.source,line:this.line,column:this.column,name:this.name})},r.prototype.join=function(e){var t,n,r=this.children.length;if(r>0){for(t=[],n=0;n<r-1;n++)t.push(this.children[n]),t.push(e);t.push(this.children[n]),this.children=t}return this},r.prototype.replaceRight=function(e,t){var n=this.children[this.children.length-1];return n[a]?n.replaceRight(e,t):"string"==typeof n?this.children[this.children.length-1]=n.replace(e,t):this.children.push("".replace(e,t)),this},r.prototype.setSourceContent=function(e,t){this.sourceContents[s.toSetString(e)]=t},r.prototype.walkSourceContents=function(e){for(var t=0,n=this.children.length;t<n;t++)this.children[t][a]&&this.children[t].walkSourceContents(e);var r=Object.keys(this.sourceContents);for(t=0,n=r.length;t<n;t++)e(s.fromSetString(r[t]),this.sourceContents[r[t]])},r.prototype.toString=function(){var e="";return this.walk(function(t){e+=t}),e},r.prototype.toStringWithSourceMap=function(e){var t={code:"",line:1,column:0},n=new i(e),r=!1,s=null,o=null,a=null,u=null;return this.walk(function(e,i){t.code+=e,null!==i.source&&null!==i.line&&null!==i.column?(s===i.source&&o===i.line&&a===i.column&&u===i.name||n.addMapping({source:i.source,original:{line:i.line,column:i.column},generated:{line:t.line,column:t.column},name:i.name}),s=i.source,o=i.line,a=i.column,u=i.name,r=!0):r&&(n.addMapping({generated:{line:t.line,column:t.column}}),s=null,r=!1);for(var c=0,l=e.length;c<l;c++)10===e.charCodeAt(c)?(t.line++,t.column=0,c+1===l?(s=null,r=!1):r&&n.addMapping({source:i.source,original:{line:i.line,column:i.column},generated:{line:t.line,column:t.column},name:i.name})):t.column++}),this.walkSourceContents(function(e,t){n.setSourceContent(e,t)}),{code:t.code,map:n}},n.SourceNode=r},{"./source-map-generator":21,"./util":23}],23:[function(e,t,n){function r(e){var t=e.match(c);return t?{scheme:t[1],auth:t[2],host:t[3],port:t[4],path:t[5]}:null}function i(e){var t="";return e.scheme&&(t+=e.scheme+":"),t+="//",e.auth&&(t+=e.auth+"@"),e.host&&(t+=e.host),e.port&&(t+=":"+e.port),e.path&&(t+=e.path),t}function s(e){var t=e,s=r(e);if(s){if(!s.path)return e;t=s.path}for(var o,a=n.isAbsolute(t),u=t.split(/\/+/),c=0,l=u.length-1;l>=0;l--)"."===(o=u[l])?u.splice(l,1):".."===o?c++:c>0&&(""===o?(u.splice(l+1,c),c=0):(u.splice(l,2),c--));return""===(t=u.join("/"))&&(t=a?"/":"."),s?(s.path=t,i(s)):t}function o(e){return e}function a(e){if(!e)return!1;var t=e.length;if(t<9)return!1;if(95!==e.charCodeAt(t-1)||95!==e.charCodeAt(t-2)||111!==e.charCodeAt(t-3)||116!==e.charCodeAt(t-4)||111!==e.charCodeAt(t-5)||114!==e.charCodeAt(t-6)||112!==e.charCodeAt(t-7)||95!==e.charCodeAt(t-8)||95!==e.charCodeAt(t-9))return!1;for(var n=t-10;n>=0;n--)if(36!==e.charCodeAt(n))return!1;return!0}function u(e,t){return e===t?0:e>t?1:-1}n.getArg=function(e,t,n){if(t in e)return e[t];if(3===arguments.length)return n;throw new Error('"'+t+'" is a required argument.')};var c=/^(?:([\w+\-.]+):)?\/\/(?:(\w+:\w+)@)?([\w.]*)(?::(\d+))?(\S*)$/,l=/^data:.+\,.+$/;n.urlParse=r,n.urlGenerate=i,n.normalize=s,n.join=function(e,t){""===e&&(e="."),""===t&&(t=".");var n=r(t),o=r(e);if(o&&(e=o.path||"/"),n&&!n.scheme)return o&&(n.scheme=o.scheme),i(n);if(n||t.match(l))return t;if(o&&!o.host&&!o.path)return o.host=t,i(o);var a="/"===t.charAt(0)?t:s(e.replace(/\/+$/,"")+"/"+t);return o?(o.path=a,i(o)):a},n.isAbsolute=function(e){return"/"===e.charAt(0)||!!e.match(c)},n.relative=function(e,t){""===e&&(e="."),e=e.replace(/\/$/,"");for(var n=0;0!==t.indexOf(e+"/");){var r=e.lastIndexOf("/");if(r<0)return t;if((e=e.slice(0,r)).match(/^([^\/]+:\/)?\/*$/))return t;++n}return Array(n+1).join("../")+t.substr(e.length+1)};var p=!("__proto__"in Object.create(null));n.toSetString=p?o:function(e){return a(e)?"$"+e:e},n.fromSetString=p?o:function(e){return a(e)?e.slice(1):e},n.compareByOriginalPositions=function(e,t,n){var r=e.source-t.source;return 0!==r?r:0!=(r=e.originalLine-t.originalLine)?r:0!=(r=e.originalColumn-t.originalColumn)||n?r:0!=(r=e.generatedColumn-t.generatedColumn)?r:0!=(r=e.generatedLine-t.generatedLine)?r:e.name-t.name},n.compareByGeneratedPositionsDeflated=function(e,t,n){var r=e.generatedLine-t.generatedLine;return 0!==r?r:0!=(r=e.generatedColumn-t.generatedColumn)||n?r:0!=(r=e.source-t.source)?r:0!=(r=e.originalLine-t.originalLine)?r:0!=(r=e.originalColumn-t.originalColumn)?r:e.name-t.name},n.compareByGeneratedPositionsInflated=function(e,t){var n=e.generatedLine-t.generatedLine;return 0!==n?n:0!=(n=e.generatedColumn-t.generatedColumn)?n:0!==(n=u(e.source,t.source))?n:0!=(n=e.originalLine-t.originalLine)?n:0!=(n=e.originalColumn-t.originalColumn)?n:u(e.name,t.name)}},{}],24:[function(e,t,n){n.SourceMapGenerator=e("./lib/source-map-generator").SourceMapGenerator,n.SourceMapConsumer=e("./lib/source-map-consumer").SourceMapConsumer,n.SourceNode=e("./lib/source-node").SourceNode},{"./lib/source-map-consumer":20,"./lib/source-map-generator":21,"./lib/source-node":22}],25:[function(e,t,n){t.exports={_args:[[{raw:"nodent-compiler@>=3.1.5",scope:null,escapedName:"nodent-compiler",name:"nodent-compiler",rawSpec:">=3.1.5",spec:">=3.1.5",type:"range"},"/Users/<USER>/Documents/JSON/ajv/node_modules/nodent"]],_from:"nodent-compiler@>=3.1.5",_id:"nodent-compiler@3.1.5",_inCache:!0,_location:"/nodent-compiler",_nodeVersion:"8.9.1",_npmOperationalInternal:{host:"s3://npm-registry-packages",tmp:"tmp/nodent-compiler-3.1.5.tgz_1511792299537_0.15715787676163018"},_npmUser:{name:"matatbread",email:"<EMAIL>"},_npmVersion:"5.5.1",_phantomChildren:{},_requested:{raw:"nodent-compiler@>=3.1.5",scope:null,escapedName:"nodent-compiler",name:"nodent-compiler",rawSpec:">=3.1.5",spec:">=3.1.5",type:"range"},_requiredBy:["/nodent"],_resolved:"https://registry.npmjs.org/nodent-compiler/-/nodent-compiler-3.1.5.tgz",_shasum:"8c09289eacf7256bda89c2b88941681d5cccf80c",_shrinkwrap:null,_spec:"nodent-compiler@>=3.1.5",_where:"/Users/<USER>/Documents/JSON/ajv/node_modules/nodent",author:{name:"Mat At Bread",email:"<EMAIL>"},bugs:{url:"https://github.com/MatAtBread/nodent/issues"},dependencies:{acorn:">=2.5.2","acorn-es7-plugin":">=1.1.6","source-map":"^0.5.6"},description:"NoDent - Asynchronous Javascript language extensions",devDependencies:{},directories:{},dist:{integrity:"sha512-Istg796un2lALiy/eFNnLbAEMovQqrtpVqXVY8PKs6ycsyBbK480D55misJBQ1QxvstcJ7Hk9xbSVkV8lIi+tg==",shasum:"8c09289eacf7256bda89c2b88941681d5cccf80c",tarball:"https://registry.npmjs.org/nodent-compiler/-/nodent-compiler-3.1.5.tgz"},engines:"node >= 0.10.0",gitHead:"93054f019902e2b107e7be681836273f35a02614",homepage:"https://github.com/MatAtBread/nodent-compiler#readme",keywords:["Javascript","ES7","async","await","language","extensions","Node","callback","generator","Promise","asynchronous"],license:"BSD-2-Clause",main:"compiler.js",maintainers:[{name:"matatbread",email:"<EMAIL>"}],name:"nodent-compiler",optionalDependencies:{},readme:"ERROR: No README data found!",repository:{type:"git",url:"git+https://github.com/MatAtBread/nodent-compiler.git"},scripts:{test:"node tests/basic.js # Please install 'nodent' and test the compiler fully from there."},version:"3.1.5"}},{}],26:[function(e,t,n){"use strict";function r(e,t){if(Function.prototype.$asyncspawn||Object.defineProperty(Function.prototype,"$asyncspawn",{value:r,enumerable:!1,configurable:!0,writable:!0}),this instanceof Function){var n=this;return new e(function(e,r){function i(t,n){var o;try{if((o=t.call(s,n)).done){if(o.value!==e){if(o.value&&o.value===o.value.then)return o.value(e,r);e&&e(o.value),e=null}return}o.value.then?o.value.then(function(e){i(s.next,e)},function(e){i(s.throw,e)}):i(s.next,o.value)}catch(e){return r&&r(e),void(r=null)}}var s=n.call(t,e,r);i(s.next)})}}var i=function(e,t){for(var n=t.toString(),r="return "+n,i=n.match(/.*\(([^)]*)\)/)[1],s=/['"]!!!([^'"]*)['"]/g,o=[];;){var a=s.exec(r);if(!a)break;o.push(a)}return o.reverse().forEach(function(t){r=r.slice(0,t.index)+e[t[1]]+r.substr(t.index+t[0].length)}),r=r.replace(/\/\*[^*]*\*\//g," ").replace(/\s+/g," "),new Function(i,r)()}({zousan:e("./zousan").toString(),thenable:e("./thenableFactory").toString()},function e(t,n){function r(){return i.apply(t,arguments)}Function.prototype.$asyncbind||Object.defineProperty(Function.prototype,"$asyncbind",{value:e,enumerable:!1,configurable:!0,writable:!0}),e.trampoline||(e.trampoline=function(e,t,n,r,i){return function s(o){for(;o;){if(o.then)return o=o.then(s,r),i?void 0:o;try{if(o.pop){if(o.length)return o.pop()?t.call(e):o;o=n}else o=o.call(e)}catch(e){return r(e)}}}}),e.LazyThenable||(e.LazyThenable="!!!thenable"(),e.EagerThenable=e.Thenable=(e.EagerThenableFactory="!!!zousan")());var i=this;switch(n){case!0:return new e.Thenable(r);case 0:return new e.LazyThenable(r);case void 0:return r.then=r,r;default:return function(){try{return i.apply(t,arguments)}catch(e){return n(e)}}}});i(),r(),t.exports={$asyncbind:i,$asyncspawn:r}},{"./thenableFactory":27,"./zousan":28}],27:[function(e,t,n){t.exports=function(){function e(e){return e&&e instanceof Object&&"function"==typeof e.then}function t(n,r,i){try{var s=i?i(r):r;if(n===s)return n.reject(new TypeError("Promise resolution loop"));e(s)?s.then(function(e){t(n,e)},function(e){n.reject(e)}):n.resolve(s)}catch(e){n.reject(e)}}function n(){}function r(e){}function i(r,i){var s=new n;try{this._resolver(function(n){return e(n)?n.then(r,i):t(s,n,r)},function(e){t(s,e,i)})}catch(e){t(s,e,i)}return s}function s(e){this._resolver=e,this.then=i}return n.prototype={resolve:r,reject:r,then:function(e,t){this.resolve=e,this.reject=t}},s.resolve=function(e){return s.isThenable(e)?e:{then:function(t){return t(e)}}},s.isThenable=e,s}},{}],28:[function(e,t,n){(function(e){"use strict";t.exports=function(t){function n(e){if(e){var t=this;e(function(e){t.resolve(e)},function(e){t.reject(e)})}}function r(e,t){if("function"==typeof e.y)try{var n=e.y.call(void 0,t);e.p.resolve(n)}catch(t){e.p.reject(t)}else e.p.resolve(t)}function i(e,t){if("function"==typeof e.n)try{var n=e.n.call(void 0,t);e.p.resolve(n)}catch(t){e.p.reject(t)}else e.p.reject(t)}t=t||"object"==typeof e&&e.nextTick||"function"==typeof setImmediate&&setImmediate||function(e){setTimeout(e,0)};var s=function(){function e(){for(;n.length-r;){try{n[r]()}catch(e){}n[r++]=void 0,r===i&&(n.splice(0,i),r=0)}}var n=[],r=0,i=1024;return function(i){n.push(i),n.length-r==1&&t(e)}}();return n.prototype={resolve:function(e){if(void 0===this.state){if(e===this)return this.reject(new TypeError("Attempt to resolve promise with self"));var t=this;if(e&&("function"==typeof e||"object"==typeof e))try{var n=0,i=e.then;if("function"==typeof i)return void i.call(e,function(e){n++||t.resolve(e)},function(e){n++||t.reject(e)})}catch(e){return void(n||this.reject(e))}this.state=r,this.v=e,t.c&&s(function(){for(var n=0,i=t.c.length;n<i;n++)r(t.c[n],e)})}},reject:function(e){if(void 0===this.state){this.state=i,this.v=e;var t=this.c;t&&s(function(){for(var n=0,r=t.length;n<r;n++)i(t[n],e)})}},then:function(e,t){var r=new n,i={y:e,n:t,p:r};if(void 0===this.state)this.c?this.c.push(i):this.c=[i];else{var o=this.state,a=this.v;s(function(){o(i,a)})}return r}},n.resolve=function(e){if(e&&e instanceof n)return e;var t=new n;return t.resolve(e),t},n.reject=function(e){if(e&&e instanceof n)return e;var t=new n;return t.reject(e),t},n.version="2.3.3-nodent",n}}).call(this,e("_process"))},{_process:32}],29:[function(e,t,n){t.exports=function(e,t,n,r){var i=[[],[]],s=[/(.*)(<script[^>]*>)(.*)/i,/(.*)(<\/script>)(.*)/i],o=0,a=!0;t=t.split("\n");for(var u=0;u<t.length;){var c=s[o].exec(t[u]);c&&0==o&&c[2].match("src=")&&(c=null),c?(1==o?(i[o].push(c[1]),pr=e.compile(i[1].join("\n"),n,3,r.compiler).code,a&&r.runtime&&(a=!1,r.runtime&&i[0].push("Function.prototype.$asyncbind = "+e.$asyncbind.toString()+";\n")),i[0].push(pr),i[1]=[],i[o=0].push(c[2])):(i[o].push(c[1]),i[o].push(c[2]),o=1),t[u]=c[3]):i[o].push(t[u++])}return i[0].join("\n")}},{}],30:[function(e,t,n){(function(e){function t(e,t){for(var n=0,r=e.length-1;r>=0;r--){var i=e[r];"."===i?e.splice(r,1):".."===i?(e.splice(r,1),n++):n&&(e.splice(r,1),n--)}if(t)for(;n--;n)e.unshift("..");return e}function r(e,t){if(e.filter)return e.filter(t);for(var n=[],r=0;r<e.length;r++)t(e[r],r,e)&&n.push(e[r]);return n}var i=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/,s=function(e){return i.exec(e).slice(1)};n.resolve=function(){for(var n="",i=!1,s=arguments.length-1;s>=-1&&!i;s--){var o=s>=0?arguments[s]:e.cwd();if("string"!=typeof o)throw new TypeError("Arguments to path.resolve must be strings");o&&(n=o+"/"+n,i="/"===o.charAt(0))}return n=t(r(n.split("/"),function(e){return!!e}),!i).join("/"),(i?"/":"")+n||"."},n.normalize=function(e){var i=n.isAbsolute(e),s="/"===o(e,-1);return(e=t(r(e.split("/"),function(e){return!!e}),!i).join("/"))||i||(e="."),e&&s&&(e+="/"),(i?"/":"")+e},n.isAbsolute=function(e){return"/"===e.charAt(0)},n.join=function(){var e=Array.prototype.slice.call(arguments,0);return n.normalize(r(e,function(e,t){if("string"!=typeof e)throw new TypeError("Arguments to path.join must be strings");return e}).join("/"))},n.relative=function(e,t){function r(e){for(var t=0;t<e.length&&""===e[t];t++);for(var n=e.length-1;n>=0&&""===e[n];n--);return t>n?[]:e.slice(t,n-t+1)}e=n.resolve(e).substr(1),t=n.resolve(t).substr(1);for(var i=r(e.split("/")),s=r(t.split("/")),o=Math.min(i.length,s.length),a=o,u=0;u<o;u++)if(i[u]!==s[u]){a=u;break}var c=[];for(u=a;u<i.length;u++)c.push("..");return(c=c.concat(s.slice(a))).join("/")},n.sep="/",n.delimiter=":",n.dirname=function(e){var t=s(e),n=t[0],r=t[1];return n||r?(r&&(r=r.substr(0,r.length-1)),n+r):"."},n.basename=function(e,t){var n=s(e)[2];return t&&n.substr(-1*t.length)===t&&(n=n.substr(0,n.length-t.length)),n},n.extname=function(e){return s(e)[3]};var o="b"==="ab".substr(-1)?function(e,t,n){return e.substr(t,n)}:function(e,t,n){return t<0&&(t=e.length+t),e.substr(t,n)}}).call(this,e("_process"))},{_process:32}],31:[function(e,t,n){(function(e){"use strict";var n="win32"===e.platform,r=/^([a-zA-Z]:|[\\\/]{2}[^\\\/]+[\\\/]+[^\\\/]+)?([\\\/])?([\s\S]*?)$/,i=/^([\s\S]*?)((?:\.{1,2}|[^\\\/]+?|)(\.[^.\/\\]*|))(?:[\\\/]*)$/,s={};s.parse=function(e){if("string"!=typeof e)throw new TypeError("Parameter 'pathString' must be a string, not "+typeof e);var t=function(e){var t=r.exec(e),n=(t[1]||"")+(t[2]||""),s=t[3]||"",o=i.exec(s);return[n,o[1],o[2],o[3]]}(e);if(!t||4!==t.length)throw new TypeError("Invalid path '"+e+"'");return{root:t[0],dir:t[0]+t[1].slice(0,-1),base:t[2],ext:t[3],name:t[2].slice(0,t[2].length-t[3].length)}};var o=/^(\/?|)([\s\S]*?)((?:\.{1,2}|[^\/]+?|)(\.[^.\/]*|))(?:[\/]*)$/,a={};a.parse=function(e){if("string"!=typeof e)throw new TypeError("Parameter 'pathString' must be a string, not "+typeof e);var t=function(e){return o.exec(e).slice(1)}(e);if(!t||4!==t.length)throw new TypeError("Invalid path '"+e+"'");return t[1]=t[1]||"",t[2]=t[2]||"",t[3]=t[3]||"",{root:t[0],dir:t[0]+t[1].slice(0,-1),base:t[2],ext:t[3],name:t[2].slice(0,t[2].length-t[3].length)}},t.exports=n?s.parse:a.parse,t.exports.posix=a.parse,t.exports.win32=s.parse}).call(this,e("_process"))},{_process:32}],32:[function(e,t,n){function r(){throw new Error("setTimeout has not been defined")}function i(){throw new Error("clearTimeout has not been defined")}function s(e){if(l===setTimeout)return setTimeout(e,0);if((l===r||!l)&&setTimeout)return l=setTimeout,setTimeout(e,0);try{return l(e,0)}catch(t){try{return l.call(null,e,0)}catch(t){return l.call(this,e,0)}}}function o(){y&&f&&(y=!1,f.length?d=f.concat(d):m=-1,d.length&&a())}function a(){if(!y){var e=s(o);y=!0;for(var t=d.length;t;){for(f=d,d=[];++m<t;)f&&f[m].run();m=-1,t=d.length}f=null,y=!1,function(e){if(p===clearTimeout)return clearTimeout(e);if((p===i||!p)&&clearTimeout)return p=clearTimeout,clearTimeout(e);try{p(e)}catch(t){try{return p.call(null,e)}catch(t){return p.call(this,e)}}}(e)}}function u(e,t){this.fun=e,this.array=t}function c(){}var l,p,h=t.exports={};!function(){try{l="function"==typeof setTimeout?setTimeout:r}catch(e){l=r}try{p="function"==typeof clearTimeout?clearTimeout:i}catch(e){p=i}}();var f,d=[],y=!1,m=-1;h.nextTick=function(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var n=1;n<arguments.length;n++)t[n-1]=arguments[n];d.push(new u(e,t)),1!==d.length||y||s(a)},u.prototype.run=function(){this.fun.apply(null,this.array)},h.title="browser",h.browser=!0,h.env={},h.argv=[],h.version="",h.versions={},h.on=c,h.addListener=c,h.once=c,h.off=c,h.removeListener=c,h.removeAllListeners=c,h.emit=c,h.prependListener=c,h.prependOnceListener=c,h.listeners=function(e){return[]},h.binding=function(e){throw new Error("process.binding is not supported")},h.cwd=function(){return"/"},h.chdir=function(e){throw new Error("process.chdir is not supported")},h.umask=function(){return 0}},{}],33:[function(e,t,n){var r=e("./lib/core"),i=e("./lib/async");i.core=r,i.isCore=function(e){return r[e]},i.sync=e("./lib/sync"),t.exports=i},{"./lib/async":34,"./lib/core":37,"./lib/sync":39}],34:[function(e,t,n){(function(n){var r=e("./core"),i=e("fs"),s=e("path"),o=e("./caller.js"),a=e("./node-modules-paths.js");t.exports=function(e,t,u){function c(t,n,r){t?d(t):n?d(null,n,r):h(w,function(t,n,r){if(t)d(t);else if(n)d(null,n,r);else{var i=new Error("Cannot find module '"+e+"' from '"+x+"'");i.code="MODULE_NOT_FOUND",d(i)}})}function l(e,t,n){function r(e,t,n){function i(n,i,l){if(c=i,n)return o(n);if(l&&c&&y.pathFilter){var p=s.relative(l,u),h=p.slice(0,p.length-e[0].length),f=y.pathFilter(c,t,h);if(f)return r([""].concat(b.slice()),s.resolve(l,f),c)}g(u,a)}function a(n,i){return n?o(n):i?o(null,u,c):void r(e.slice(1),t,c)}if(0===e.length)return o(null,void 0,n);var u=t+e[0],c=n;c?i(null,c):p(s.dirname(u),i)}var i=t,o=n;"function"==typeof i&&(o=i,i=void 0);r([""].concat(b),e,i)}function p(e,t){if(""===e||"/"===e)return t(null);if("win32"===n.platform&&/^\w:[/\\]*$/.test(e))return t(null);if(/[/\\]node_modules[/\\]*$/.test(e))return t(null);var r=s.join(e,"package.json");g(r,function(n,i){if(!i)return p(s.dirname(e),t);v(r,function(n,i){n&&t(n);try{var s=JSON.parse(i)}catch(e){}s&&y.packageFilter&&(s=y.packageFilter(s,r)),t(null,s,e)})})}function h(e,t,n){var r=n,i=t;"function"==typeof i&&(r=i,i=y.package);var o=s.join(e,"package.json");g(o,function(t,n){return t?r(t):n?void v(o,function(t,n){if(t)return r(t);try{var i=JSON.parse(n)}catch(e){}if(y.packageFilter&&(i=y.packageFilter(i,o)),i.main)return"."!==i.main&&"./"!==i.main||(i.main="index"),void l(s.resolve(e,i.main),i,function(t,n,i){if(t)return r(t);if(n)return r(null,n,i);if(!i)return l(s.join(e,"index"),i,r);h(s.resolve(e,i.main),i,function(t,n,i){return t?r(t):n?r(null,n,i):void l(s.join(e,"index"),i,r)})});l(s.join(e,"/index"),i,r)}):l(s.join(e,"index"),i,r)})}function f(t,n){function r(e,r,i){return e?t(e):r?t(null,r,i):void f(t,n.slice(1))}if(0===n.length)return t(null,void 0);var i=n[0];l(s.join(i,e),void 0,function(n,o,a){return n?t(n):o?t(null,o,a):void h(s.join(i,e),void 0,r)})}var d=u,y=t||{};if("function"==typeof y&&(d=y,y={}),"string"!=typeof e){var m=new TypeError("Path must be a string.");return n.nextTick(function(){d(m)})}var g=y.isFile||function(e,t){i.stat(e,function(e,n){return e?"ENOENT"===e.code||"ENOTDIR"===e.code?t(null,!1):t(e):t(null,n.isFile()||n.isFIFO())})},v=y.readFile||i.readFile,b=y.extensions||[".js"],x=y.basedir||s.dirname(o());if(y.paths=y.paths||[],/^(?:\.\.?(?:\/|$)|\/|([A-Za-z]:)?[/\\])/.test(e)){var w=s.resolve(x,e);".."!==e&&"/"!==e.slice(-1)||(w+="/"),/\/$/.test(e)&&w===x?h(w,y.package,c):l(w,y.package,c)}else!function(e,t,n){f(n,a(t,y))}(0,x,function(t,n,i){if(t)d(t);else if(n)d(null,n,i);else{if(r[e])return d(null,e);var s=new Error("Cannot find module '"+e+"' from '"+x+"'");s.code="MODULE_NOT_FOUND",d(s)}})}}).call(this,e("_process"))},{"./caller.js":35,"./core":37,"./node-modules-paths.js":38,_process:32,fs:7,path:30}],35:[function(e,t,n){t.exports=function(){var e=Error.prepareStackTrace;Error.prepareStackTrace=function(e,t){return t};var t=(new Error).stack;return Error.prepareStackTrace=e,t[2].getFileName()}},{}],36:[function(e,t,n){t.exports={assert:!0,buffer_ieee754:"< 0.9.7",buffer:!0,child_process:!0,cluster:!0,console:!0,constants:!0,crypto:!0,_debugger:"< 8",dgram:!0,dns:!0,domain:!0,events:!0,freelist:"< 6",fs:!0,http:!0,http2:">= 8.8",https:!0,_http_server:">= 0.11",_linklist:"< 8",module:!0,net:!0,os:!0,path:!0,perf_hooks:">= 8.5",process:">= 1",punycode:!0,querystring:!0,readline:!0,repl:!0,stream:!0,string_decoder:!0,sys:!0,timers:!0,tls:!0,tty:!0,url:!0,util:!0,v8:">= 1",vm:!0,zlib:!0}},{}],37:[function(e,t,n){(function(n){function r(e){if(!0===e)return!0;for(var t=e.split(" "),n=t[0],r=t[1].split("."),s=0;s<3;++s){var o=Number(i[s]||0),a=Number(r[s]||0);if(o!==a)return"<"===n?o<a:">="===n&&o>=a}return!1}var i=n.versions&&n.versions.node&&n.versions.node.split(".")||[],s=e("./core.json"),o={};for(var a in s)Object.prototype.hasOwnProperty.call(s,a)&&(o[a]=r(s[a]));t.exports=o}).call(this,e("_process"))},{"./core.json":36,_process:32}],38:[function(e,t,n){var r=e("path"),i=e("fs"),s=r.parse||e("path-parse");t.exports=function(e,t){var n=t&&t.moduleDirectory?[].concat(t.moduleDirectory):["node_modules"],o=r.resolve(e);if(t&&!1===t.preserveSymlinks)try{o=i.realpathSync(o)}catch(e){if("ENOENT"!==e.code)throw e}var a="/";/^([A-Za-z]:)/.test(o)?a="":/^\\\\/.test(o)&&(a="\\\\");for(var u=[o],c=s(o);c.dir!==u[u.length-1];)u.push(c.dir),c=s(c.dir);var l=u.reduce(function(e,t){return e.concat(n.map(function(e){return r.join(a,t,e)}))},[]);return t&&t.paths?l.concat(t.paths):l}},{fs:7,path:30,"path-parse":31}],39:[function(e,t,n){var r=e("./core"),i=e("fs"),s=e("path"),o=e("./caller.js"),a=e("./node-modules-paths.js");t.exports=function(e,t){function n(e){if(l(e))return e;for(var t=0;t<h.length;t++){var n=e+h[t];if(l(n))return n}}function u(e){var t=s.join(e,"/package.json");if(l(t))try{var r=p(t,"UTF8"),i=JSON.parse(r);if(c.packageFilter&&(i=c.packageFilter(i,e)),i.main){"."!==i.main&&"./"!==i.main||(i.main="index");var o=n(s.resolve(e,i.main));if(o)return o;var a=u(s.resolve(e,i.main));if(a)return a}}catch(e){}return n(s.join(e,"/index"))}if("string"!=typeof e)throw new TypeError("Path must be a string.");var c=t||{},l=c.isFile||function(e){try{var t=i.statSync(e)}catch(e){if(e&&("ENOENT"===e.code||"ENOTDIR"===e.code))return!1;throw e}return t.isFile()||t.isFIFO()},p=c.readFileSync||i.readFileSync,h=c.extensions||[".js"],f=c.basedir||s.dirname(o());if(c.paths=c.paths||[],/^(?:\.\.?(?:\/|$)|\/|([A-Za-z]:)?[/\\])/.test(e)){var d=s.resolve(f,e);".."!==e&&"/"!==e.slice(-1)||(d+="/");var y=n(d)||u(d);if(y)return y}else{var m=function(e,t){for(var r=a(t,c),i=0;i<r.length;i++){var o=r[i],l=n(s.join(o,"/",e));if(l)return l;var p=u(s.join(o,"/",e));if(p)return p}}(e,f);if(m)return m}if(r[e])return e;var g=new Error("Cannot find module '"+e+"' from '"+f+"'");throw g.code="MODULE_NOT_FOUND",g}},{"./caller.js":35,"./core":37,"./node-modules-paths.js":38,fs:7,path:30}],nodent:[function(require,module,exports){(function(process,global,__dirname){"use strict";function copyObj(e){var t={};return e.forEach(function(e){if(e&&"object"==typeof e)for(var n in e)t[n]=e[n]}),t}function globalErrorHandler(e){throw e}function isDirective(e){return"ExpressionStatement"===e.type&&("StringLiteral"===e.expression.type||"Literal"===e.expression.type&&"string"==typeof e.expression.value)}function parseCompilerOptions(code,log,filename){log||(log=console.warn.bind(console));var regex,set,parseOpts={};if("string"==typeof code)(regex=code.match(useDirective))&&(set=regex[1]||"default");else for(var i=0;i<code.body.length&&isDirective(code.body[i].type);i++){var test="'"+code.body[i].value+"'";if(regex=test.match(useDirective)){set=regex[1]||"default";break}}if(!regex){if(!defaultCodeGenOpts.noUseDirective)return null;set="default",regex=[null,null,"{}"]}if(set)try{filename?require("fs").lstatSync(filename).isDirectory()||(filename=require("path").dirname(filename)):filename=require("path").resolve(".");var packagePath=require("resolve").sync("package.json",{moduleDirectory:[""],extensions:[""],basedir:filename}),packageOptions=JSON.parse(fs.readFileSync(packagePath)).nodent.directive[set]}catch(e){}try{parseOpts=copyObj([optionSets[set],packageOptions,regex[2]&&JSON.parse(regex[2])])}catch(e){log("Invalid literal compiler option: "+(regex&&regex[0]||"<no options found>"))}return Object.keys(hostOptions).forEach(function(k){"host"===parseOpts[k]&&(parseOpts[k]=function(){try{return eval(hostOptions[k]),!0}catch(e){return!1}}())}),parseOpts.promises||parseOpts.es7||parseOpts.generators||parseOpts.engine?((parseOpts.promises||parseOpts.es7)&&parseOpts.generators&&(log("No valid 'use nodent' directive, assumed -es7 mode"),parseOpts=optionSets.es7),(parseOpts.generators||parseOpts.engine)&&(parseOpts.promises=!0),parseOpts.promises&&(parseOpts.es7=!0),parseOpts):null}function stripBOM(e){return 65279===e.charCodeAt(0)&&(e=e.slice(1)),"#!"===e.substring(0,2)&&(e="//"+e),e}function compileNodentedFile(e,t){return t=t||e.log,function(n,r,i){var s=stripBOM(fs.readFileSync(r,"utf8")),o=e.parse(s,r,i);i=i||parseCompilerOptions(o.ast,t,r),e.asynchronize(o,void 0,i,t),e.prettyPrint(o,i),n._compile(o.code,o.filename)}}function asyncify(e){return e=e||Thenable,function(t,n,r){if(Array.isArray(n)){var i=n;n=function(e,t){return i.indexOf(e)>=0}}else n=n||function(e,t){return!(e.match(/Sync$/)&&e.replace(/Sync$/,"")in t)};r||(r="");var s=Object.create(t);for(var o in s)!function(){var i=o;try{"function"!=typeof t[i]||s[i+r]&&s[i+r].isAsync||!n(i,s)||(s[i+r]=function(){var n=Array.prototype.slice.call(arguments);return new e(function(e,r){var s=function(t,n){if(t)return r(t);switch(arguments.length){case 0:return e();case 2:return e(n);default:return e(Array.prototype.slice.call(arguments,1))}};n.length>t[i].length?n.push(s):n[t[i].length-1]=s;t[i].apply(t,n)})},s[i+r].isAsync=!0)}catch(e){}}();return s.super=t,s}}function generateRequestHandler(e,t,n){var r={},i=this;t||(t=/\.njs$/),n?n.compiler||(n.compiler={}):n={compiler:{}};var s=copyObj([NodentCompiler.initialCodeGenOpts,n.compiler]);return function(o,a,u){function c(e){a.statusCode=500,a.write(e.toString()),a.end()}if(r[o.url])return a.setHeader("Content-Type",r[o.url].contentType),n.setHeaders&&n.setHeaders(a),a.write(r[o.url].output),void a.end();if(!(o.url.match(t)||n.htmlScriptRegex&&o.url.match(n.htmlScriptRegex)))return u&&u();var l=e+o.url;if(n.extensions&&!fs.existsSync(l))for(var p=0;p<n.extensions.length;p++)if(fs.existsSync(l+"."+n.extensions[p])){l=l+"."+n.extensions[p];break}fs.readFile(l,function(e,t){if(e)return c(e);try{var u,l;n.htmlScriptRegex&&o.url.match(n.htmlScriptRegex)?(u=require("./htmlScriptParser")(i,t.toString(),o.url,n),l="text/html"):(n.runtime?(u="Function.prototype."+s.$asyncbind+" = "+$asyncbind.toString()+";",s.generators&&(u+="Function.prototype."+s.$asyncspawn+" = "+$asyncspawn.toString()+";"),s.wrapAwait&&!s.promises&&(u+="Object."+s.$makeThenable+" = "+Thenable.resolve.toString()+";"),s.mapStartLine=u.split("\n").length,u+="\n"):u="",u+=i.compile(t.toString(),o.url,null,s).code,l="application/javascript"),a.setHeader("Content-Type",l),n.enableCache&&(r[o.url]={output:u,contentType:l}),n.setHeaders&&n.setHeaders(a),a.write(u),a.end()}catch(e){return c(e)}})}}function requireCover(e,t){t=t||{};var n=e+"|"+Object.keys(t).sort().reduce(function(e,n){return e+n+JSON.stringify(t[n])},"");return this.covers[n]||(e.indexOf("/")>=0?this.covers[n]=require(e):this.covers[n]=require(__dirname+"/covers/"+e)),this.covers[n](this,t)}function prepareMappedStackTrace(e,t){return e+t.map(function(e){var t=e.getFileName();if(t&&NodentCompiler.prototype.smCache[t]){var n=NodentCompiler.prototype.smCache[t].smc.originalPositionFor({line:e.getLineNumber(),column:e.getColumnNumber()});if(n&&n.line){var r=e.toString();return"\n    at "+r.substring(0,r.length-1)+" => …"+n.source+":"+n.line+":"+n.column+(e.getFunctionName()?")":"")}}return"\n    at "+e}).join("")}function setGlobalEnvironment(e){var t={};t[defaultCodeGenOpts.$asyncbind]={value:$asyncbind,writable:!0,enumerable:!1,configurable:!0},t[defaultCodeGenOpts.$asyncspawn]={value:$asyncspawn,writable:!0,enumerable:!1,configurable:!0};try{Object.defineProperties(Function.prototype,t)}catch(t){e.log("Function prototypes already assigned: ",t.messsage)}defaultCodeGenOpts[defaultCodeGenOpts.$error]in global||(global[defaultCodeGenOpts[defaultCodeGenOpts.$error]]=globalErrorHandler),e.augmentObject&&Object.defineProperties(Object.prototype,{asyncify:{value:function(e,t,n){return asyncify(e)(this,t,n)},writable:!0,configurable:!0},isThenable:{value:function(){return Thenable.isThenable(this)},writable:!0,configurable:!0}}),Object[defaultCodeGenOpts.$makeThenable]=Thenable.resolve}function initialize(e){function t(n,r){if(!r.match(/nodent\/nodent\.js$/)){if(r.match(/node_modules\/nodent\/.*\.js$/))return stdJSLoader(n,r);for(var o=0;o<i.length;o++)if(r.slice(0,i[o].path.length)==i[o].path){if(i[o].jsCompiler){if(i[o].jsCompiler===t)break;return i[o].jsCompiler.apply(this,arguments)}return stdJSLoader(n,r)}var a=parseCompilerOptions(stripBOM(fs.readFileSync(r,"utf8")),e.log,r);return a?s(n,r,a):stdJSLoader(n,r)}var u={path:r.replace(/\/node_modules\/nodent\/nodent\.js$/,"")};u.path&&(u.version=JSON.parse(fs.readFileSync(r.replace(/nodent\.js$/,"package.json"))).version,stdJSLoader(n,r),function(e,t){e=e.split("."),t=t.split(".");for(var n=0;n<3;n++){if(e[n]<t[n])return-1;if(e[n]>t[n])return 1}return 0}(u.version,NodentCompiler.prototype.version)<0&&(u.originalNodentLoader=n.exports,n.exports=function(){var t=require.extensions[".js"],n=u.originalNodentLoader.apply(this,arguments);return u.jsCompiler=require.extensions[".js"],require.extensions[".js"]=t,setGlobalEnvironment(e),n},Object.keys(u.originalNodentLoader).forEach(function(e){n.exports[e]=u.originalNodentLoader[e]}),i.push(u),i=i.sort(function(e,t){return t.path.length-e.path.length})))}function n(t){if(Array.isArray(t))return t.forEach(n);if(require.extensions[t]){Object.keys(e).filter(function(t){return compiler[t]!=e[t]}).length&&e.log("File extension "+t+" already configured for async/await compilation.")}require.extensions[t]=compileNodentedFile(compiler,e.log)}if(e){for(var r in e)if("use"!==r&&!config.hasOwnProperty(r))throw new Error("NoDent: unknown option: "+r+"="+JSON.stringify(e[r]))}else e={};compiler?compiler.setOptions(e):(Object.keys(config).forEach(function(t){t in e||(e[t]=config[t])}),compiler=new NodentCompiler(e)),e.dontMapStackTraces||(Error.prepareStackTrace=prepareMappedStackTrace),setGlobalEnvironment(e);var i=[];if(!e.dontInstallRequireHook){if(!stdJSLoader){stdJSLoader=require.extensions[".js"];var s=compileNodentedFile(compiler,e.log);require.extensions[".js"]=t}e.extension&&n(e.extension)}return e.use&&(Array.isArray(e.use)?(e.log("Warning: nodent({use:[...]}) is deprecated. Use nodent.require(module,options)\n"+(new Error).stack.split("\n")[2]),e.use.length&&e.use.forEach(function(e){compiler[e]=compiler.require(e)})):(e.log("Warning: nodent({use:{...}}) is deprecated. Use nodent.require(module,options)\n"+(new Error).stack.split("\n")[2]),Object.keys(e.use).forEach(function(t){compiler[t]=compiler.require(t,e.use[t])}))),compiler}function runFromCLI(){function e(e,n){try{var s,o;if(r.fromast){if(e=JSON.parse(e),s={origCode:"",filename:t,ast:e},!(o=parseCompilerOptions(e,i.log))){var a=r.use?'"use nodent-'+r.use+'";':'"use nodent";';o=parseCompilerOptions(a,i.log),console.warn("/* "+t+": No 'use nodent*' directive, assumed "+a+" */")}}else(o=parseCompilerOptions(r.use?'"use nodent-'+r.use+'";':e,i.log))||(o=parseCompilerOptions('"use nodent";',i.log),r.dest||console.warn("/* "+t+": 'use nodent*' directive missing/ignored, assumed 'use nodent;' */")),s=i.parse(e,t,o);if(r.parseast||r.pretty||i.asynchronize(s,void 0,o,i.log),i.prettyPrint(s,o),r.out||r.pretty||r.dest){if(r.dest&&!n)throw new Error("Can't write unknown file to "+r.dest);var u="";r.runtime&&(u+="Function.prototype.$asyncbind = "+Function.prototype.$asyncbind.toString()+";\n",u+="global.$error = global.$error || "+global.$error.toString()+";\n"),u+=s.code,n&&r.dest?(fs.writeFileSync(r.dest+n,u),console.log("Compiled",r.dest+n)):console.log(u)}(r.minast||r.parseast)&&console.log(JSON.stringify(s.ast,function(e,t){return"$"===e[0]||e.match(/^(start|end|loc)$/)?void 0:t},2,null)),r.ast&&console.log(JSON.stringify(s.ast,function(e,t){return"$"===e[0]?void 0:t},0)),r.exec&&new Function(s.code)()}catch(e){console.error(e)}}var t,n=require("path"),r=(process.env.NODENT_OPTS&&JSON.parse(process.env.NODENT_OPTS),function(e){for(var t=[],n=e||2;n<process.argv.length;n++)if("--"===process.argv[n].slice(0,2)){var r=process.argv[n].slice(2).split("=");t[r[0]]=r[1]||!0}else t.push(process.argv[n]);return t}());initialize.setDefaultCompileOptions({sourcemap:r.sourcemap,wrapAwait:r.wrapAwait,lazyThenables:r.lazyThenables,noRuntime:r.noruntime,es6target:r.es6target,parser:r.noextensions?{noNodentExtensions:!0}:void 0});var i=initialize({augmentObject:!0});if(!(r.fromast||r.parseast||r.pretty||r.out||r.dest||r.ast||r.minast||r.exec))try{var s=n.resolve(r[0]);return require(s)}catch(e){throw e&&(e.message=r[0]+": "+e.message),e}if(0==r.length||"-"===r[0])return t="(stdin)",function(e){return new Thenable(function(t,n){var r=[];e.on("data",function(e){r.push(e)}),e.on("end",function(){var e=r.map(function(e){return e.toString()}).join("");return t(e)}),e.on("error",n)}.$asyncbind(this))}(process.stdin).then(e,globalErrorHandler);for(var o=0;o<r.length;o++)t=n.resolve(r[o]),e(stripBOM(fs.readFileSync(t,"utf8")),r[o])}var stdJSLoader,fs=require("fs"),NodentCompiler=require("nodent-compiler"),config={log:function(e){console.warn("Nodent: "+e)},augmentObject:!1,extension:".njs",dontMapStackTraces:!1,asyncStackTrace:!1,babelTree:!1,dontInstallRequireHook:!1},defaultCodeGenOpts=Object.create(NodentCompiler.initialCodeGenOpts,{es7:{value:!0,writable:!0,enumerable:!0}}),optionSets={default:defaultCodeGenOpts,es7:Object.create(defaultCodeGenOpts),promise:Object.create(defaultCodeGenOpts,{promises:{value:!0,writable:!0,enumerable:!0}}),generator:Object.create(defaultCodeGenOpts,{generators:{value:!0,writable:!0,enumerable:!0},es7:{value:!1,writable:!0,enumerable:!0}}),engine:Object.create(defaultCodeGenOpts,{engine:{value:!0,writable:!0,enumerable:!0},promises:{value:!0,writable:!0,enumerable:!0}}),host:Object.create(defaultCodeGenOpts,{promises:{value:"host",writable:!0,enumerable:!0},es6target:{value:"host",writable:!0,enumerable:!0},engine:{value:"host",writable:!0,enumerable:!0}})};optionSets.promises=optionSets.promise,optionSets.generators=optionSets.generator;var useDirective=/^\s*['"]use\s+nodent-?([a-zA-Z0-9]*)?(\s*.*)?['"]\s*;/,runtimes=require("nodent-runtime"),$asyncbind=runtimes.$asyncbind,$asyncspawn=runtimes.$asyncspawn,Thenable=$asyncbind.Thenable,hostOptions={promises:"Promise",es6target:"()=>0",engine:"(async ()=>0)",noRuntime:"Promise"};NodentCompiler.prototype.Thenable=Thenable,NodentCompiler.prototype.EagerThenable=$asyncbind.EagerThenableFactory,NodentCompiler.prototype.asyncify=asyncify,NodentCompiler.prototype.require=requireCover,NodentCompiler.prototype.generateRequestHandler=generateRequestHandler,NodentCompiler.prototype.$asyncspawn=$asyncspawn,NodentCompiler.prototype.$asyncbind=$asyncbind,NodentCompiler.prototype.parseCompilerOptions=parseCompilerOptions,$asyncbind.call($asyncbind);var compiler;initialize.setDefaultCompileOptions=function(e,t){return e&&Object.keys(e).forEach(function(t){if(!(t in defaultCodeGenOpts))throw new Error("NoDent: unknown compiler option: "+t);defaultCodeGenOpts[t]=e[t]}),t&&Object.keys(t).forEach(function(e){if(!(e in t))throw new Error("NoDent: unknown configuration option: "+e);config[e]=t[e]}),initialize},initialize.setCompileOptions=function(e,t){return optionSet[e]=optionSet[e]||copyObj([defaultCodeGenOpts]),t&&Object.keys(t).forEach(function(n){if(!(n in defaultCodeGenOpts))throw new Error("NoDent: unknown compiler option: "+n);optionSet[e][n]=t[n]}),initialize},initialize.asyncify=asyncify,initialize.Thenable=$asyncbind.Thenable,initialize.EagerThenable=$asyncbind.EagerThenableFactory,module.exports=initialize,require.main===module&&process.argv.length>=3&&runFromCLI()}).call(this,require("_process"),"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{},"/node_modules/nodent")},{"./htmlScriptParser":29,_process:32,fs:7,"nodent-compiler":10,"nodent-runtime":26,path:30,resolve:33}]},{},[]);